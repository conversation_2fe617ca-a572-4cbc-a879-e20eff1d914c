<?php 
/**
 * Plugin Name: Guardian-Gaze-new
 * Description: A comprehensive security plugin for WordPress that scans for malicious code.
 * Version: 1.0.0
 * Author: Guardian-Gaze Team
 * Text Domain: guardian-gaze-new
 * Domain Path: /languages
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Plugin version
define('GUARDIAN_GAZE_VERSION', '1.0.0');
define('GUARDIAN_GAZE_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('GUARDIAN_GAZE_PLUGIN_URL', plugin_dir_url(__FILE__));


// Include core functionality
require_once GUARDIAN_GAZE_PLUGIN_DIR . 'includes/class-guardian-gaze.php';
require_once GUARDIAN_GAZE_PLUGIN_DIR . 'includes/class-guardian-gaze-scanner.php';
require_once GUARDIAN_GAZE_PLUGIN_DIR . 'includes/class-guardian-gaze-api.php';
require_once GUARDIAN_GAZE_PLUGIN_DIR . 'includes/class-guardian-gaze-activator.php';
require_once GUARDIAN_GAZE_PLUGIN_DIR . 'includes/class-guardian-gaze-decryptor.php';
require_once GUARDIAN_GAZE_PLUGIN_DIR . 'admin/class-guardian-gaze-settings.php';

// Activation hook
register_activation_hook(__FILE__, array('Guardian_Gaze_Activator', 'activate'));

// Initialize the plugin
function run_guardian_gaze() {
    $plugin = new Guardian_Gaze();
    $settings = new Guardian_Gaze_Settings();
    $plugin->run();
}

run_guardian_gaze(); 