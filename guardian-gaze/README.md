# Guardian Gaze - WordPress Security Scanner

Guardian Gaze is a comprehensive WordPress security plugin that scans your WordPress installation for potential malicious code using regularly updated patterns.

## Features

- Scan different WordPress directories:
  - Entire wp-content directory
  - Active theme only
  - Plugins directory
- Real-time pattern updates from external API
- Recursive file scanning for PHP, JS, HTML, and TXT files
- Clean, modern admin interface
- AJAX-based scanning to prevent timeouts
- Detailed results with file locations and line numbers
- Severity indicators for found matches

## Installation

1. Download the plugin
2. Upload to your WordPress site's `/wp-content/plugins/` directory
3. Activate the plugin through the 'Plugins' menu in WordPress
4. Access Guardian Gaze from the WordPress admin menu

## Usage

1. Navigate to the Guardian Gaze menu in your WordPress admin panel
2. Select the directory you want to scan:
   - wp-content directory
   - Active theme only
   - Plugins directory
3. Click "Start Scan"
4. View results in the table below
   - Results show file location, line number, and matched pattern
   - Severity levels are color-coded for easy identification

## API Integration

The plugin fetches malicious code patterns from:
```
https://testing-wp.internal-ggfw.site/ggfw/api/patterns.php
```

Expected API Response Format:
```json
{
    "patterns": [
        {
            "name": "Pattern Name",
            "pattern": "Regular Expression Pattern",
            "severity": "high|medium|low"
        }
    ]
}
```

## Requirements

- WordPress 5.0 or higher
- PHP 7.2 or higher

## Security Considerations

- The plugin requires admin privileges to perform scans
- All API responses are validated before use
- Regular expressions are tested for validity before execution
- File paths are sanitized to prevent directory traversal

## Support

For support or feature requests, please open an issue in the repository.

## License

This plugin is licensed under the GPL v2 or later. 