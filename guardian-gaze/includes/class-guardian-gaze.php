<?php
/**
 * The core plugin class.
 */
class Guardian_Gaze {
    /**
     * The scanner instance.
     *
     * @var Guardian_Gaze_Scanner
     */
    protected $scanner;

    /**
     * The API handler instance.
     *
     * @var Guardian_Gaze_API
     */
    protected $api;

    /**
     * Initialize the plugin.
     */
    public function __construct() {
        $this->load_dependencies();
        $this->setup_admin();
        $this->define_admin_hooks();
    }

    /**
     * Load required dependencies.
     */
    private function load_dependencies() {
        $this->scanner = new Guardian_Gaze_Scanner();
        $this->api = new Guardian_Gaze_API();
    }

    /**
     * Register all admin-related hooks.
     */
    private function define_admin_hooks() {
        add_action('admin_menu', array($this, 'add_plugin_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_styles'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_guardian_gaze_scan', array($this->scanner, 'handle_scan_request'));
        add_action('wp_ajax_guardian_gaze_get_stats', array($this->scanner, 'handle_get_stats'));
        add_action('wp_ajax_guardian_gaze_toggle_pause', array($this->scanner, 'handle_toggle_pause'));
        add_action('wp_ajax_guardian_gaze_register_site', array($this->api, 'register_site'));
        
    }

    /**
     * Setup admin-specific functionality.
     */
    private function setup_admin() {
        require_once GUARDIAN_GAZE_PLUGIN_DIR . 'admin/class-guardian-gaze-admin.php';
    }

    /**
     * Add menu item to WordPress admin.
     */
    public function add_plugin_admin_menu() {
        add_menu_page(
            'Guardian Gaze', 
            'Guardian Gaze',
            'manage_options',
            'guardian-gaze',
            array($this, 'display_plugin_admin_page'),
            'dashicons-shield',
            100
        );
    }

    /**
     * Register admin styles.
     */
    public function enqueue_admin_styles() {
        wp_enqueue_style(
            'guardian-gaze-admin',
            GUARDIAN_GAZE_PLUGIN_URL . 'admin/css/guardian-gaze-admin.css',
            array(),
            GUARDIAN_GAZE_VERSION,
            'all'
        );
    }

    /**
     * Register admin scripts.
     */
    public function enqueue_admin_scripts() {
        wp_enqueue_script(
            'guardian-gaze-admin',
            GUARDIAN_GAZE_PLUGIN_URL . 'admin/js/guardian-gaze-admin.js',
            array('jquery'),
            GUARDIAN_GAZE_VERSION,
            false
        );

        wp_localize_script(
            'guardian-gaze-admin',
            'guardian_gaze',
            array(
                'nonce' => wp_create_nonce('guardian_gaze_nonce')
            )
        );
    }

    /**
     * Display the admin page.
     */
    public function display_plugin_admin_page() {
        // $_POST['scan_type'] = 'root';
        // $this->scanner->handle_scan_request2();
        // exit;
        $is_registered = $this->api->checkRegistered();
        
        require_once GUARDIAN_GAZE_PLUGIN_DIR . 'admin/partials/guardian-gaze-admin-display.php';
    }

    /**
     * Run the plugin.
     */
    public function run() {
        // Plugin is now initialized and running
    }
} 