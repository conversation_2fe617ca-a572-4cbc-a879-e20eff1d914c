<?php
/**
 * The scanner class that handles file scanning functionality.
 */
class Guardian_Gaze_Scanner {
    /**
     * API instance
     *
     * @var Guardian_Gaze_API
     */
    private $api;

    /**
     * Scanning statistics
     *
     * @var array
     */
    private $stats = array(
        'total_files' => 0,
        'scanned_files' => 0,
        'total_folders' => 0,
        'scanned_folders' => 0,
        'current_file' => '',
        'current_folder' => '',
        'is_paused' => false
    );

    /**
     * Scan start time
     *
     * @var string
     */
    private $scan_start_time;
    private $patterns = array();
    private $results = array();

    /**
     * Initialize the scanner
     */
    public function __construct() {
        $this->api = new Guardian_Gaze_API();
        add_action('wp_ajax_guardian_gaze_scan', array($this, 'handle_scan_request'));
        add_action('wp_ajax_guardian_gaze_toggle_pause', array($this, 'handle_toggle_pause'));
        add_action('wp_ajax_guardian_gaze_get_stats', array($this, 'handle_get_stats'));

        // Add plugins to excluded folders if not already there
        // $this->update_excluded_folders();
    }

    

    /**
     * Handle AJAX scan request
     */
    public function handle_scan_request() {
        check_ajax_referer('guardian_gaze_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Unauthorized access');
        }

        // Increase time limit for large scans
        @set_time_limit(600); // Set to 10 minutes
        ini_set('memory_limit', '512M'); // Increase memory limit too

        $scan_type = isset($_POST['scan_type']) ? sanitize_text_field($_POST['scan_type']) : 'wp-content';
        if($scan_type == 'root') {
            $this->perform_integrity_check();
            $scan_type = 'wp-content';
        }
        $start_path = $this->get_scan_path($scan_type);

        if (!$start_path) {
            wp_send_json_error('Invalid scan type');
        }

        // Record scan start time
        $this->scan_start_time = current_time('mysql');

        // Reset statistics
        $this->stats['total_files'] = 0;
        $this->stats['scanned_files'] = 0;
        $this->stats['total_folders'] = 0;
        $this->stats['scanned_folders'] = 0;
        $this->stats['current_file'] = '';
        $this->stats['current_folder'] = '';
        $this->stats['is_paused'] = false;

        // Count total files and folders first
        $this->count_total_items($start_path);

        $patterns = $this->api->get_patterns();
        if (is_wp_error($patterns)) {
            wp_send_json_error('Failed to fetch patterns: ' . $patterns->get_error_message());
        }
        $this->patterns = $patterns;

        update_option('guardian_gaze_scan_stats', $this->stats);
        $this->results = $this->scan_directory($start_path, $patterns);
        
        // Save scan results to database
        $scan_id = $this->save_scan_results($this->results, $scan_type);
        
        wp_send_json_success(array(
            'results' => $this->results,
            'scan_id' => $scan_id
        ));
    }
    public function handle_scan_request2() {
        
        $scan_type = isset($_POST['scan_type']) ? sanitize_text_field($_POST['scan_type']) : 'root';
        $start_path = $this->get_scan_path($scan_type);

        if (!$start_path) {
            wp_send_json_error('Invalid scan type');
        }

        // Increase time limit for large scans
        @set_time_limit(600); // Set to 10 minutes
        ini_set('memory_limit', '512M'); // Increase memory limit too

        // Record scan start time
        $this->scan_start_time = current_time('mysql');

        // Reset statistics
        $this->stats['total_files'] = 0;
        $this->stats['scanned_files'] = 0;
        $this->stats['total_folders'] = 0;
        $this->stats['scanned_folders'] = 0;
        $this->stats['current_file'] = '';
        $this->stats['is_paused'] = false;

        // Count total files and folders first
        $this->count_total_items($start_path);

        $patterns = array();
        
        

        $results = $this->scan_directory($start_path, $patterns);
        echo $results;
        
        // Save scan results to database
        $scan_id = $this->save_scan_results($results, $scan_type);
        
        wp_send_json_success(array(
            'results' => $results,
            'scan_id' => $scan_id
        ));
    }

    /**
     * Handle pause/resume toggle
     */
    public function handle_toggle_pause() {
        check_ajax_referer('guardian_gaze_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Unauthorized access');
        }

        $stats = get_option('guardian_gaze_scan_stats', $this->stats);
        $stats['is_paused'] = !$stats['is_paused'];
        update_option('guardian_gaze_scan_stats', $stats);
        
        wp_send_json_success(array('is_paused' => $stats['is_paused']));
    }

    /**
     * Handle stats request
     */
    public function handle_get_stats() {
        check_ajax_referer('guardian_gaze_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Unauthorized access');
        }

        $stats = get_option('guardian_gaze_scan_stats', $this->stats);
        wp_send_json_success($stats);
    }

    /**
     * Count total files and folders
     */
    private function count_total_items($dir) {
        if (!is_dir($dir) || $this->should_skip_folder($dir)) {
            return;
        }

        $this->stats['total_folders']++;

        $items = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($items as $item) {
            if ($item->isDir()) {
                if (!$this->should_skip_folder($item->getRealPath())) {
                    $this->stats['total_folders']++;
                }
            } elseif ($item->isFile() && $this->is_scannable_file($item)) {
                $this->stats['total_files']++;
            }
        }
        
        update_option('guardian_gaze_scan_stats', $this->stats);
    }

    /**
     * Get the appropriate path based on scan type
     */
    private function get_scan_path($scan_type) {
        switch ($scan_type) {
            case 'root':
                return ABSPATH;
            case 'wp-content':
                return WP_CONTENT_DIR;
            case 'themes':
                return get_template_directory();
            case 'plugins':
                return WP_PLUGIN_DIR;
            default:
                return false;
        }
    }

    /**
     * Check if file is in an excluded folder
     */
    private function is_in_excluded_folder($file_path) {
        // $default_excluded = 'node_modules,vendor,uploads,plugins';
        $excluded_folders = get_option('guardian_gaze_excluded_folders');
        // $excluded_folders = !empty($excluded_option) ? $excluded_option : $default_excluded;
        $excluded_folders = array_map('trim', explode(',', $excluded_folders));
        
        // Convert file path to relative path from WordPress root
        $relative_path = str_replace(ABSPATH, '', $file_path);
        
        // Check if any excluded folder is in the path
        foreach ($excluded_folders as $folder) {
            if (strpos($relative_path, '/' . $folder . '/') !== false) {
                error_log('Guardian Gaze: Skipping file in excluded folder: ' . $relative_path);
                return true;
            }
        }
        
        return false;
    }

    /**
     * Scan a directory recursively
     */
    private function scan_directory($dir, $patterns) {
        if (!is_dir($dir) || $this->should_skip_folder($dir)) {
            return array();
        }

        $this->stats['scanned_folders']++;
        update_option('guardian_gaze_scan_stats', $this->stats);

        
        $items = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($items as $item) {
            // Check if scanning is paused
            $current_stats = get_option('guardian_gaze_scan_stats', $this->stats);
            if ($current_stats['is_paused']) {
                sleep(2);
                continue;
            }

            if ($item->isDir()) {
                if (!$this->should_skip_folder($item->getRealPath())) {
                    $this->stats['scanned_folders']++;
                    $this->stats['current_folder'] = str_replace(ABSPATH, '', $item->getRealPath());
                    update_option('guardian_gaze_scan_stats', $this->stats);
                }
            } elseif ($item->isFile() && $this->is_scannable_file($item) && !$this->is_in_excluded_folder($item->getRealPath())) {
                $this->stats['current_file'] = str_replace(ABSPATH, '', $item->getRealPath());
                $this->stats['scanned_files']++;
                update_option('guardian_gaze_scan_stats', $this->stats);

                $file_results = $this->scan_file($item, $patterns);
                if (!empty($file_results)) {
                    $results[] = $file_results;
                }
            }
        }

        return $results;
    }

    /**
     * Check if file should be scanned based on extension
     */
    private function is_scannable_file($file) {
        $excluded_extensions = explode(',', get_option('guardian_gaze_excluded_extensions', 'png,jpg,jpeg,gif,bmp,tif,tiff,psd,svg,webp,doc,docx,otf,ttf,fla,flv,mov,mp3,pdf,css,pot,po,mo,so,exe,zip,7z,gz,rar'));
        $excluded_extensions = array_map('trim', $excluded_extensions);
        return !in_array(strtolower($file->getExtension()), $excluded_extensions);
    }

    /**
     * Check if folder should be skipped
     */
    private function should_skip_folder($path) {
        // Get excluded folders from options with default value if not set
        // $default_excluded = 'node_modules,vendor,uploads,plugins';
        $excluded_folders = get_option('guardian_gaze_excluded_folders');
        // $excluded_folders = !empty($excluded_option) ? $excluded_option : $default_excluded;
        
        // Convert to array and trim whitespace
        $excluded_folders = array_map('trim', explode(',', $excluded_folders));
        
        // Get folder name and relative path
        $folder_name = basename($path);
        $relative_path = str_replace(ABSPATH, '', $path);
        
        // Check if the folder itself is excluded
        if (in_array($folder_name, $excluded_folders)) {
            error_log('Guardian Gaze: Skipping excluded folder: ' . $relative_path);
            return true;
        }
        
        // Check if the folder is inside an excluded folder
        foreach ($excluded_folders as $excluded) {
            if (strpos($relative_path, '/' . $excluded . '/') !== false) {
                error_log('Guardian Gaze: Skipping folder inside excluded directory: ' . $relative_path);
                return true;
            }
        }
        
        return false;
    }

    /**
     * Scan a single file for matches
     */
    private function scan_file($file, $patterns) {
        if (!$file->isFile() || !$file->isReadable()) {
            return null;
        }
        $content = file_get_contents($file->getRealPath());
        $matches = array();

        foreach ($patterns as $key => $pattern) {
            if (preg_match_all($pattern, $content, $pattern_matches, PREG_OFFSET_CAPTURE)) {
                foreach ($pattern_matches[0] as $match) {
                    $line_number = substr_count(substr($content, 0, $match[1]), "\n") + 1;
                    
                    $matches[] = array(
                        'pattern_name' => $key,
                        'line' => $line_number,
                        'match' => substr($match[0], 0, 100).".....",
                        'severity' => ""
                    );
                }
            }
        }

        if (!empty($matches)) {
            return array(
                'file' => str_replace(ABSPATH, '', $file->getRealPath()),
                'matches' => $matches
            );
        }

        return null;
    }

    /**
     * Save scan results to database
     */
    private function save_scan_results($results, $scan_type) {
        global $wpdb;
        
        // Insert scan log
        $scan_data = array(
            'scan_date' => current_time('mysql'),
            'scan_type' => $scan_type,
            'total_files' => $this->stats['total_files'],
            'total_folders' => $this->stats['total_folders'],
            'infected_files' => count($results),
            'scan_duration' => time() - strtotime($this->scan_start_time),
            'status' => 'completed'
        );
        
        $wpdb->insert(
            $wpdb->prefix . 'guardian_gaze_scan_logs',
            $scan_data,
            array('%s', '%s', '%d', '%d', '%d', '%d', '%s')
        );
        
        $scan_id = $wpdb->insert_id;
        
        // Insert infected files
        if (!empty($results)) {
            foreach ($results as $result) {
                foreach ($result['matches'] as $match) {
                    $wpdb->insert(
                        $wpdb->prefix . 'guardian_gaze_infected_files',
                        array(
                            'scan_id' => $scan_id,
                            'file_path' => $result['file'],
                            'pattern_name' => $match['pattern_name'],
                            'line_number' => $match['line'],
                            'match_content' => $match['match'],
                            'severity' => $match['severity'],
                            'detection_date' => current_time('mysql'),
                            'status' => 'detected'
                        ),
                        array('%d', '%s', '%s', '%d', '%s', '%s', '%s', '%s')
                    );
                }
            }
        }
        
        return $scan_id;
    }
    private function perform_integrity_check() {
        $this->stats['current_file'] = "Integrity Check";
        try {
            integrity_log('Starting integrity check');
            
            if (!function_exists('wp_remote_get')) {
                require_once(ABSPATH . WPINC . '/http.php');
            }
            
            // Get the WordPress version
            global $wp_version;
            
            // Get the checksums from WordPress API
            $checksums = get_core_checksums2($wp_version, 'en_US');
            
            if (!$checksums) {
                return false;
            }
            
            $mismatched_files = array();
            $wordpress_dir = ABSPATH;
            
            foreach ($checksums as $file => $checksum) {
                // Skip wp-content directory
                if (strpos($file, 'wp-content/') === 0) {
                    continue;
                }
                
                $file_path = $wordpress_dir . $file;
                
                if (!file_exists($file_path)) {
                    $file_obj = new SplFileInfo($file_path);
                    $file_results = $this->scan_file($file_obj, $this->patterns);
                    if (!empty($file_results)) {
                        $this->results[] = $file_results;
                    }
                    continue;
                }
                
                if (!is_readable($file_path)) {
                    
                    $file_obj = new SplFileInfo($file_path);
                    $file_results = $this->scan_file($file_obj, $this->patterns);
                    if (!empty($file_results)) {
                        $this->results[] = $file_results;
                    }
                    continue;
                }
                
                if (md5_file($file_path) !== $checksum) {
                    // scan file for patterns
                    $file_obj = new SplFileInfo($file_path);
                    $file_results = $this->scan_file($file_obj, $this->patterns);
                    if (!empty($file_results)) {
                        $this->results[] = $file_results;
                    }
                }
            }
            $updatedFiles = array();
            // Display results
            if (empty($mismatched_files)) {
                return false;
            } else {
                foreach ($mismatched_files as $file) {
                    $updatedFiles[] = array(
                        'file' => $file['file'],
                        'status' => $file['status']
                    );
                }
            }
            
            // Return the list of mismatched files
            return $updatedFiles;
            
        } catch (Exception $e) {
            return false;
        }
    }
    private function get_core_checksums2($version, $locale) {
        
        $url = 'https://api.wordpress.org/core/checksums/1.0/?version=' . $version . '&locale=' . $locale;
        $response = wp_remote_get($url);
        
        if (is_wp_error($response)) {
            return false;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (!isset($data['checksums']) || !is_array($data['checksums'])) {
            return false;
        }
        
        return $data['checksums'];
    }
} 