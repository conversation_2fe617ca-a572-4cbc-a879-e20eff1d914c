<?php
/**
 * Pattern decryption utility class
 */
class Guardian_Gaze_Decryptor {
    /**
     * Encryption key
     * @var string
     */
    private $key;

    /**
     * Initialize the decryptor
     */
    public function __construct() {
        // Get the API key to use as encryption key
        $this->key = get_option('guardian_gaze_api_key', '');
    }

    /**
     * Decrypt patterns received from API
     *
     * @param array $encrypted_patterns Array of encrypted patterns
     * @return array Decrypted patterns
     */

     /**
     * Decrypt patterns using site-specific key
     */
    public function decrypt_patterns($encrypted_data) {
        try {
            $decoded = base64_decode($encrypted_data);
            if ($decoded === false) {
                return new WP_Error('Invalid pattern data forma');
            }
            
            $ivlen = openssl_cipher_iv_length('aes-256-gcm');
            
            $iv = substr($decoded, 0, $ivlen);
            $tag = substr($decoded, $ivlen, 16);
            $ciphertext = substr($decoded, $ivlen + 16);
            $site_key = $this->generateSiteKey();
            if ($site_key === false) {
                return new WP_Error('Invalid site key', 'Failed to generate site key');
            }
            
            $decrypted = openssl_decrypt(
                $ciphertext,
                'aes-256-gcm',
                $site_key,
                OPENSSL_RAW_DATA,
                $iv,
                $tag
            );

            if ($decrypted === false) {
                $error = openssl_error_string();
                return new WP_Error('decryption_failed', 'Pattern decryption failed: ' . ($error ? $error : 'Unknown error'));

            }
            
            
            $patterns = json_decode($decrypted, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return new WP_Error('Invalid pattern data structure', 'JSON decode error: ' . json_last_error_msg());
            }
            
            return $patterns;
            
        } catch (\Exception $e) {
            throw $e;
        }
    }
    // site key 

    private function generateSiteKey() {
        try {
            $site_url = get_site_url();
            $email = get_option('admin_email');
            
            // Use same method as server to generate key
            $data = $site_url . '|' . $email;
            $key = hash('sha256', $data, true);
            
            return $key;
        } catch (\Exception $e) {
            throw $e;
        }
    }
} 