<?php
/**
 * Handles API interactions for the plugin.
 */
class Guardian_Gaze_API {
    /**
     * API endpoint for patterns
     *
     * @var string
     */
    private $patterns_api_url = 'http://localhost/wp/rafay/api/patterns.php'; // local development
    // private $patterns_api_url = 'https://testing-wp.internal-ggfw.site/ggfw/api/patterns.php'; // live testing site
    // Uncomment the line below for production use
    // private $patterns_api_url = 'https://guardian-gaze.com/api/patterns.php'; // live site

    /**
     * Initialize the API handler
     */
    public function __construct() {
        // Constructor code if needed
    }

    /**
     * Register site with the definitions server
     */
    public function register_site() {
        check_ajax_referer('guardian_gaze_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Unauthorized access');
        }

        $api_key = get_option('guardian_gaze_api_key', '');
        $current_version = get_option('guardian_gaze_definitions_version', '1.0.0');

        $response = wp_remote_post($this->patterns_api_url, array(
            'body' => array(
                'action' => 'register_site',
                'email' => get_option('admin_email'),
                'site_url' => get_site_url(),
                'api_key' => $api_key,
                'plugin_version' => GUARDIAN_GAZE_VERSION,
                'current_version' => $current_version
            )
        ));

        if (is_wp_error($response)) {
            wp_send_json_error('Failed to connect to definitions server: ' . $response->get_error_message());
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if ($data && isset($data['success']) && $data['success']) {
            update_option('guardian_gaze_site_registered', true);
            update_option('guardian_gaze_registration_date', current_time('mysql'));
            wp_send_json_success($data);
        } else {
            wp_send_json_error('Failed to register site: ' . ($data['message'] ?? 'Unknown error'));
        }
    }

    /**
     * Get patterns from the API
     */
    public function get_patterns() {
        
        $api_key = get_option('guardian_gaze_api_key', '');
        $current_version = get_option('guardian_gaze_definitions_version', '1.0.0');

        $response = wp_remote_post($this->patterns_api_url, array(
            'body' => array(
                'action' => 'get_patterns',
                'email' => get_option('admin_email'),
                'site_url' => get_site_url(),
                'api_key' => $api_key,
                'plugin_version' => GUARDIAN_GAZE_VERSION,
                'current_version' => $current_version
            )
        ));

        if (is_wp_error($response)) {
            return new WP_Error('connection_error', 'Failed to connect to definitions server: ' . $response->get_error_message());
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if ($data && isset($data['success']) && $data['success'] && isset($data['patterns'])) {
            // Initialize decryptor and decrypt patterns
            $decryptor = new Guardian_Gaze_Decryptor();
            $patterns = $decryptor->decrypt_patterns($data['patterns']);
            return $patterns;
        } else {
            return new WP_Error('invalid_response', 'Invalid response from patterns API: ' . ($data['message'] ?? 'Unknown error'));
        }
    }

    function checkRegistered()
    {
        
        if (!current_user_can('manage_options')) {
            return false;
        }

        $api_key = get_option('guardian_gaze_api_key', '');
        $current_version = get_option('guardian_gaze_definitions_version', '1.0.0');

        $response = wp_remote_post($this->patterns_api_url, array(
            'body' => array(
                'action' => 'check_registration',
                'email' => get_option('admin_email'),
                'site_url' => get_site_url(),
                'api_key' => $api_key,
                'plugin_version' => GUARDIAN_GAZE_VERSION,
                'current_version' => $current_version
            )
        ));

        if (is_wp_error($response)) {
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if ($data && isset($data['success']) && $data['success']) {
            return $data;
        } else {
            return false;
        }
    }
} 