<?php
/**
 * Fired during plugin activation.
 */
class Guardian_Gaze_Activator {

    /**
     * Activate the plugin.
     */
    public static function activate() {
        global $wpdb;
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        // Generate and save API key if not exists
        if (!get_option('guardian_gaze_api_key')) {
            $api_key = self::generate_api_key();
            update_option('guardian_gaze_api_key', $api_key);
        }

        // Create scan logs table
        $scan_logs_table = $wpdb->prefix . 'guardian_gaze_scan_logs';
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE IF NOT EXISTS $scan_logs_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            scan_date datetime NOT NULL,
            scan_type varchar(50) NOT NULL,
            total_files int(11) NOT NULL,
            total_folders int(11) NOT NULL,
            infected_files int(11) NOT NULL,
            scan_duration int(11) NOT NULL,
            status varchar(20) NOT NULL,
            PRIMARY KEY  (id)
        ) $charset_collate;";
        dbDelta($sql);

        // Create infected files table
        $infected_files_table = $wpdb->prefix . 'guardian_gaze_infected_files';
        $sql = "CREATE TABLE IF NOT EXISTS $infected_files_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            scan_id bigint(20) NOT NULL,
            file_path varchar(512) NOT NULL,
            pattern_name varchar(100) NOT NULL,
            line_number int(11) NOT NULL,
            match_content text NOT NULL,
            severity varchar(20) NOT NULL,
            detection_date datetime NOT NULL,
            status varchar(20) NOT NULL,
            PRIMARY KEY  (id),
            KEY scan_id (scan_id)
        ) $charset_collate;";
        dbDelta($sql);

        // Create scan details table for storing individual file scans
        $scan_details_table = $wpdb->prefix . 'guardian_gaze_scan_details';
        $sql = "CREATE TABLE IF NOT EXISTS $scan_details_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            scan_id bigint(20) NOT NULL,
            file_path varchar(512) NOT NULL,
            scan_date datetime NOT NULL,
            scan_status varchar(20) NOT NULL,
            scan_duration decimal(10,2) NOT NULL,
            file_size bigint(20) NOT NULL,
            PRIMARY KEY  (id),
            KEY scan_id (scan_id)
        ) $charset_collate;";
        dbDelta($sql);
    }

    /**
     * Generate a unique API key
     */
    private static function generate_api_key() {
        $key_length = 32;
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $api_key = '';
        
        for ($i = 0; $i < $key_length; $i++) {
            $api_key .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        return $api_key;
    }
} 