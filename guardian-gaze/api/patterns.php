<?php
/**
 * Guardian Gaze - Malicious Code Patterns API
 * 
 * This file provides regex patterns for detecting potentially malicious code.
 * Each pattern includes:
 * - name: A descriptive name of what it detects
 * - pattern: The regex pattern
 * - severity: high|medium|low
 */

// Set JSON content type
header('Content-Type: application/json');

// Allow from WordPress origin only (you should replace * with your actual domain in production)
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

// Define the patterns
$patterns = array(
    'patterns' => array(
        // PHP Code Execution
        array(
            'name' => 'PHP Shell Command Execution',
            'pattern' => '/(?:exec|shell_exec|system|passthru|`.*`)\s*\([^)]*\)/',
            'severity' => 'high'
        ),
        array(
            'name' => 'PHP Eval Usage',
            'pattern' => '/eval\s*\([^)]*\)/',
            'severity' => 'high'
        ),
        array(
            'name' => 'PHP Code Execution via create_function',
            'pattern' => '/create_function\s*\([^)]*\)/',
            'severity' => 'high'
        ),
        
        // File Operations
        array(
            'name' => 'Suspicious File Operations',
            'pattern' => '/(?:file_get_contents|file_put_contents|fopen|unlink)\s*\([^)]*(?:https?:|ftp:|php:|data:|\\\\\\\\|\.\.\/)+/',
            'severity' => 'high'
        ),
        array(
            'name' => 'Directory Traversal Attempt',
            'pattern' => '/(?:\.\.\/|\.\.\\\\){2,}/',
            'severity' => 'high'
        ),
        
        // Database
        array(
            'name' => 'Raw SQL Query',
            'pattern' => '/\$[^;]*(?:SELECT|INSERT|UPDATE|DELETE|DROP|ALTER)\s+(?:FROM|INTO|TABLE)\s+[^;]+/',
            'severity' => 'medium'
        ),
        
        // Obfuscation
        array(
            'name' => 'Base64 Encoded Code',
            'pattern' => '/base64_decode\s*\([^)]{20,}\)/',
            'severity' => 'medium'
        ),
        array(
            'name' => 'Hex Encoded String',
            'pattern' => '/(?:\\\\x[0-9a-fA-F]{2}){4,}/',
            'severity' => 'medium'
        ),
        
        // JavaScript Threats
        array(
            'name' => 'JavaScript Evil',
            'pattern' => '/(?:eval|setTimeout|setInterval)\s*\(\s*(?:atob|String\.fromCharCode)\s*\(/',
            'severity' => 'high'
        ),
        array(
            'name' => 'XSS Attack Vector',
            'pattern' => '/<script[^>]*>[^<]*(?:alert|prompt|confirm|document\.cookie|document\.write)\s*\(/',
            'severity' => 'high'
        ),
        
        // WordPress Specific
        array(
            'name' => 'WordPress Database Direct Access',
            'pattern' => '/\$wpdb->query\s*\(\s*(?!\$wpdb->prepare)/',
            'severity' => 'high'
        ),
        array(
            'name' => 'WordPress Plugin/Theme Editor',
            'pattern' => '/update_(?:plugin|theme)_editor_file\s*\(/',
            'severity' => 'medium'
        ),
        
        // Configuration Tampering
        array(
            'name' => 'WordPress Config Modification',
            'pattern' => '/define\s*\(\s*[\'"](?:DB_NAME|DB_USER|DB_PASSWORD|DB_HOST|AUTH_KEY|SECURE_AUTH_KEY)[\'"]/',
            'severity' => 'medium'
        ),
        
        // Common Malware Signatures
        array(
            'name' => 'PHP Backdoor',
            'pattern' => '/(?:\$_(?:GET|POST|REQUEST|COOKIE)\s*\[\s*[\'"][^\'"]+[\'"]\s*\]|\$(?:GET|POST|REQUEST|COOKIE)\[)(?:\s*\.\s*\$)?[^;]+(?:eval|assert|exec|system|create_function)\s*\(/',
            'severity' => 'high'
        ),
        array(
            'name' => 'Malicious iframe',
            'pattern' => '/<iframe[^>]*(?:display:\s*none|height:\s*0|width:\s*0)[^>]*>/',
            'severity' => 'high'
        )
    )
);

// Return JSON response
echo json_encode($patterns, JSON_PRETTY_PRINT); 