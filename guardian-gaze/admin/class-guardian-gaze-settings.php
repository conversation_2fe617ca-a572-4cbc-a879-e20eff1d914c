<?php
/**
 * The settings class that handles plugin settings.
 */
class Guardian_Gaze_Settings {
    /**
     * Initialize the settings
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_settings_page'));
        add_action('admin_init', array($this, 'register_settings'));
    }

    /**
     * Add settings page to admin menu
     */
    public function add_settings_page() {
        add_submenu_page(
            'guardian-gaze',
            'Guardian Gaze Settings',
            'Settings',
            'manage_options',
            'guardian-gaze-settings',
            array($this, 'render_settings_page')
        );
    }

    /**
     * Register plugin settings
     */
    public function register_settings() {
        register_setting('guardian_gaze_settings', 'guardian_gaze_excluded_extensions');
        register_setting('guardian_gaze_settings', 'guardian_gaze_excluded_folders');
        
        add_settings_section(
            'guardian_gaze_scanning_section',
            'Scanning Settings',
            array($this, 'scanning_section_callback'),
            'guardian-gaze-settings'
        );

        add_settings_field(
            'excluded_extensions',
            'Excluded File Extensions',
            array($this, 'excluded_extensions_callback'),
            'guardian-gaze-settings',
            'guardian_gaze_scanning_section'
        );

        add_settings_field(
            'excluded_folders',
            'Excluded Folders',
            array($this, 'excluded_folders_callback'),
            'guardian-gaze-settings',
            'guardian_gaze_scanning_section'
        );
    }

    /**
     * Render settings page
     */
    public function render_settings_page() {
        ?>
        <div class="wrap">
            <h1>Guardian Gaze Settings</h1>
            <form method="post" action="options.php">
                <?php
                settings_fields('guardian_gaze_settings');
                do_settings_sections('guardian-gaze-settings');
                submit_button();
                ?>
            </form>
        </div>
        <?php
    }

    /**
     * Scanning section description
     */
    public function scanning_section_callback() {
        echo '<p>Configure which file types and folders to exclude from scanning.</p>';
    }

    /**
     * Excluded extensions field callback
     */
    public function excluded_extensions_callback() {
        $excluded = get_option('guardian_gaze_excluded_extensions', 'png,jpg,jpeg,gif,bmp,tif,tiff,psd,svg,webp,doc,docx,otf,ttf,fla,flv,mov,mp3,pdf,css,pot,po,mo,so,exe,zip,7z,gz,rar');
        ?>
        <input type="text" name="guardian_gaze_excluded_extensions" value="<?php echo esc_attr($excluded); ?>" class="regular-text">
        <p class="description">Enter file extensions to exclude from scanning, separated by commas (e.g., jpg,jpeg,png,gif)</p>
        <?php
    }

    /**
     * Excluded folders field callback
     */
    public function excluded_folders_callback() {
        $excluded = get_option('guardian_gaze_excluded_folders', 'node_modules,vendor,uploads');
        ?>
        <input type="text" name="guardian_gaze_excluded_folders" value="<?php echo esc_attr($excluded); ?>" class="regular-text">
        <p class="description">Enter folder names to exclude from scanning, separated by commas (e.g., node_modules,vendor,uploads). These folders will be skipped in any directory.</p>
        <?php
    }
} 