.guardian-gaze-admin {
    margin: 20px;
}

.guardian-gaze-container {
    max-width: 1200px;
    margin: 0 auto;
}

.guardian-gaze-scan-options {
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    margin-bottom: 20px;
}

.guardian-gaze-select {
    min-width: 200px;
    margin-right: 10px;
}

.guardian-gaze-progress {
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    margin-bottom: 20px;
}

.guardian-gaze-progress-bar {
    height: 20px;
    background-color: #f1f1f1;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.guardian-gaze-progress-fill {
    height: 100%;
    background-color: #2271b1;
    width: 100%;
    animation: progress-animation 2s infinite linear;
}

.guardian-gaze-progress-text {
    text-align: center;
    color: #50575e;
}

.guardian-gaze-results {
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
}

.guardian-gaze-results table {
    width: 100%;
    border-collapse: collapse;
}

.guardian-gaze-results th {
    text-align: left;
    padding: 10px;
}

.guardian-gaze-results td {
    padding: 10px;
    word-break: break-word;
}

.guardian-gaze-error {
    margin: 20px 0;
}

/* Severity colors */
.severity-high {
    color: #dc3232;
    font-weight: bold;
}

.severity-medium {
    color: #ffb900;
    font-weight: bold;
}

.severity-low {
    color: #46b450;
}

.guardian-gaze-columns {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}
.guardian-gaze-column1 {
    flex: 1;
    min-width: 70%;
}
.guardian-gaze-column2 {
    flex: 1;
    min-width: 30%;
}
.guardian-gaze-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}
.guardian-gaze-card h2 {
    margin-top: 0;
    padding-bottom: 12px;
    border-bottom: 1px solid #eee;
}
.guardian-gaze-controls {
    margin: 20px 0;
}
.guardian-gaze-controls select {
    margin-right: 10px;
}
.guardian-gaze-progress-container {
    margin: 20px 0;
    display: none;
}
.progress-bar {
    height: 20px;
    background-color: #f0f0f1;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
    margin-bottom: 10px;
}
.progress-fill {
    height: 100%;
    background-color: #2271b1;
    width: 0;
    transition: width 0.2s ease-in-out;
}
.progress-text {
    text-align: center;
    font-weight: 500;
    color: #1d2327;
}
.guardian-gaze-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin: 20px 0;
}
.stat-box {
    flex: 1;
    min-width: 180px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 4px;
    text-align: center;
}
.stat-box.full-width {
    flex: 100%;
}
.stat-box h3 {
    margin: 0 0 10px 0;
    color: #1d2327;
    font-size: 14px;
}
.stat-box span {
    font-size: 1.2em;
    word-break: break-all;
}
.api-key-display {
    display: flex;
    align-items: center;
    gap: 10px;
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
}
.api-key-display code {
    flex: 1;
    padding: 8px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}
.copy-api-key {
    white-space: nowrap;
}
.severity-high {
    color: #dc2626;
    font-weight: bold;
}
.severity-medium {
    color: #d97706;
    font-weight: bold;
}
.severity-low {
    color: #0369a1;
    font-weight: bold;
}
@media screen and (max-width: 782px) {
    .guardian-gaze-columns {
        flex-direction: column;
    }
    .guardian-gaze-column {
        width: 100%;
    }
}

@keyframes progress-animation {
    0% {
        margin-left: -100%;
    }
    100% {
        margin-left: 100%;
    }
} 