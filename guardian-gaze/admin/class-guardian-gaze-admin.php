<?php
/**
 * The admin-specific functionality of the plugin.
 */
class Guardian_Gaze_Admin {
    /**
     * Initialize the admin class
     */
    public function __construct() {
        // Admin initialization if needed
    }

    /**
     * Register the stylesheets for the admin area.
     */
    public function enqueue_styles() {
        wp_enqueue_style(
            'guardian-gaze-admin',
            plugin_dir_url(__FILE__) . 'css/guardian-gaze-admin.css',
            array(),
            GUARDIAN_GAZE_VERSION,
            'all'
        );
    }

    /**
     * Register the JavaScript for the admin area.
     */
    public function enqueue_scripts() {
        wp_enqueue_script(
            'guardian-gaze-admin',
            plugin_dir_url(__FILE__) . 'js/guardian-gaze-admin.js',
            array('jquery'),
            GUARDIAN_GAZE_VERSION,
            false
        );
    }
} 