jQuery(document).ready(function($) {
    'use strict';

    const startScanButton = $('#start-scan');
    const scanTypeSelect = $('#scan-type');
    const progressDiv = $('#scan-progress');
    const resultsDiv = $('#guardian-gaze-results');
    const resultsCard = $('.guardian-gaze-card');
    const resultsBody = $('#guardian-gaze-results-body');
    const errorDiv = $('#guardian-gaze-error');
    const pauseButton = $('#gg-pause-scan');
    const progressContainer = $('.guardian-gaze-progress-container');
    const statsBlock = $('.guardian-gaze-stats');
    const progressBar = $('.progress-fill');
    const progressText = $('.progress-text');
    const currentFileDisplay = $('#gg-current-file');
    

    let scanInterval;
    let isPaused = false;
    let infectedFiles = 0;
    let isScanning = false;
    let latestStats = {};

    // Hide pause button and progress initially
    pauseButton.hide();
    progressContainer.hide();
    statsBlock.hide();
    let stats = {
        totalFiles: 0,
        scannedFiles: 0,
        totalFolders: 0,
        scannedFolders: 0,
        infectedFiles: 0,
        currentFile: ''
    };
    // Update scanning statistics
    function updateStats2(end = false) {
        $.post(ajaxurl, {
            action: 'guardian_gaze_get_stats',
            nonce: guardian_gaze.nonce
        }, function(response) {
            if (response.success) {
                console.log(response);
                stats = response.data;
                const totalFiles = parseInt(stats.total_files) || 0;
                const scannedFiles = parseInt(stats.scanned_files) || 0;
                const totalFolders = parseInt(stats.total_folders) || 0;
                const scannedFolders = parseInt(stats.scanned_folders) || 0;
                const infectedFiles = parseInt(stats.infected_files) || 0;
    
                $('#gg-total-files').text(totalFiles);
                $('#gg-scanned-files').text(scannedFiles);
                $('#gg-total-folders').text(totalFolders);
                $('#gg-scanned-folders').text(scannedFolders);
                $('#gg-infected-files').text(infectedFiles);
                if( end != 'end') {
                    if (isPaused) {
                        currentFileDisplay.html('Scan Paused ⏸️');
                    } else if (stats.current_file) {
                        currentFileDisplay.html(`Scanning: ${stats.current_file} 🔍`);
                    }
                }
    
                if (totalFiles > 0 || totalFolders > 0) {
                    const totalItems = totalFiles + totalFolders;
                    const scannedItems = scannedFiles + scannedFolders;
                    const progress = Math.round((scannedItems / totalItems) * 100);
                    progressBar.css('width', progress + '%');
                    progressText.text(progress + '%');
                }
    
                isPaused = stats.is_paused;
                pauseButton.text(isPaused ? 'Resume Scan' : 'Pause Scan');
            }
        });
    }
    

    startScanButton.on('click', function() {
        // Reset UI
        errorDiv.hide();
        resultsDiv.hide();
        resultsCard.show();
        resultsBody.empty();
        progressDiv.show();
        statsBlock.show();
        startScanButton.prop('disabled', true);
        pauseButton.show().text('Pause Scan');
        progressContainer.show();
        progressBar.css('width', '0%');
        progressText.text('0%');
        infectedFiles = 0;
        isScanning = true;

        // Reset stats display
        $('#gg-total-files').text('0');
        $('#gg-scanned-files').text('0');
        $('#gg-total-folders').text('0');
        $('#gg-scanned-folders').text('0');
        $('#gg-infected-files').text('0');
        currentFileDisplay.html('Starting scan... 🚀');
        
        // Start stats update interval
        scanInterval = setInterval(updateStats2, 1000);
        

        

        // Start scan
        $.post({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'guardian_gaze_scan',
                nonce: guardian_gaze.nonce,
                scan_type: scanTypeSelect.val()
            },
            success: function(response) {
                clearInterval(scanInterval);
                
                if (response.success) {
                    // console.log("succ",response);
                    displayResults(response.data.results);
                    // Show completion message with appropriate emoji based on results
                    if (infectedFiles > 0) {
                        currentFileDisplay.html(`Scan Complete - Found ${infectedFiles} infected files! ⚠️`);
                        updateStats2('end');
                    } else {
                        currentFileDisplay.html('Scan Complete - No threats found! ✅');
                        updateStats2('end');
                    }
                } else {
                    // console.log("err",response);
                    showError(response.data);
                    console.log('Scan error2:',response.data);
                    currentFileDisplay.html('Scan Failed ❌');
                }
            },
            error: function(xhr, status, error) {
                // console.error('Scan error:', error);
                // console.error('Scan error status:', status);
                // console.error('Scan error xhr:', xhr);
                clearInterval(scanInterval);
                showError('Failed to complete scan: ' + error);
                currentFileDisplay.html('Scan Failed ❌');
            },
            complete: function() {
                isScanning = false;
                progressDiv.hide();
                startScanButton.prop('disabled', false);
                pauseButton.hide();
                
            }
        });
    });

    // Handle pause button click
    pauseButton.on('click', function(e) {
        e.preventDefault();
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'guardian_gaze_toggle_pause',
                nonce: guardian_gaze.nonce
            },
            success: function(response) {
                if (response.success) {
                    isPaused = response.data.is_paused;
                    $(e.target).text(isPaused ? 'Resume Scan' : 'Pause Scan');
                    currentFileDisplay.html(isPaused ? 'Scan Paused ⏸️' : 'Resuming scan... 🔄');
                }
            }
        });
    });

    function displayResults(results) {
        if (!results || results.length === 0) {
            showError('No malicious patterns found.');
            return;
        }

        // Count infected files
        infectedFiles = results.length;
        $('#gg-infected-files').text(infectedFiles);

        results.forEach(function(fileResult) {
            fileResult.matches.forEach(function(match) {
                const row = $('<tr>');
                row.append($('<td>').text(fileResult.file));
                row.append($('<td>').text(match.line));
                row.append($('<td>').text(match.pattern_name));
                row.append($('<td>').text(match.match));
                
                const severityCell = $('<td>');
                const severityClass = 'severity-' + match.severity.toLowerCase();
                severityCell.addClass(severityClass).text(match.severity);
                row.append(severityCell);
                
                resultsBody.append(row);
            });
        });

        resultsDiv.show();
    }

    function showError(message) {
        errorDiv.html('<p>' + message + '</p>').show();
    }
}); 