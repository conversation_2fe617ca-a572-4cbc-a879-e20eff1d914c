<?php
// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

$api_key = get_option('guardian_gaze_api_key', '');
// $is_registered = get_option('guardian_gaze_site_registered', false);
// $registration_date = get_option('guardian_gaze_registration_date', '');
// print_r($is_registered);
// exit;
?>

<div class="wrap guardian-gaze-admin">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
    
    <div class="guardian-gaze-container">
        <div class="guardian-gaze-columns">
            <!-- Left Column - Scan Controls and Progress -->
            <div class="guardian-gaze-column1">
                <div class="guardian-gaze-card">
                    <h2>Scan Controls</h2>
                    <div class="guardian-gaze-controls">
                        <select id="scan-type" name="scan-type">
                            <option value="plugins">Scan plugins</option>
                            <option value="root">Complete Scan</option>
                            <option value="wp-content">Scan wp-content</option>
                            <option value="themes">Scan current theme</option>
                        </select>
                        <button id="start-scan" class="button button-primary">Start Scan</button>
                        <button id="gg-pause-scan" class="button">Pause Scan</button>
                    </div>

                    <div class="guardian-gaze-progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                        <div class="progress-text">0%</div>
                    </div>

                    <div class="guardian-gaze-stats">
                        <div class="stat-box">
                            <h3>Total Folders</h3>
                            <span id="gg-total-folders">0</span>
                        </div>
                        <div class="stat-box">
                            <h3>Scanned Folders</h3>
                            <span id="gg-scanned-folders">0</span>
                        </div>
                        <div class="stat-box">
                            <h3>Total Files</h3>
                            <span id="gg-total-files">0</span>
                        </div>
                        <div class="stat-box">
                            <h3>Scanned Files</h3>
                            <span id="gg-scanned-files">0</span>
                        </div>
                        <div class="stat-box">
                            <h3>Infected Files</h3>
                            <span id="gg-infected-files">0</span>
                        </div>
                        <div class="stat-box full-width">
                            <h3>Currently Scanning</h3>
                            <span id="gg-current-file">Not scanning</span>
                        </div>
                        
                    </div>

                    <div id="scan-progress" style="display: none;">
                        <div class="spinner is-active"></div>
                        <p>Scanning in progress...</p>
                    </div>
                </div>
            </div>

            <!-- Right Column - API Key and Registration -->
            <div class="guardian-gaze-column2">
                <div class="guardian-gaze-card">
                    <h2>API Information</h2>
                    <div class="api-key-section">
                        <p><strong>Your API Key:</strong></p>
                        <div class="api-key-display">
                            <code><?php echo esc_html($api_key); ?></code>
                        </div>
                        <p class="description">This API key is unique to your installation and is required for external integrations.</p>
                        
                        <div class="registration-section">
                            <?php if ($is_registered): ?>
                                <div class="registration-status registered">
                                    <span class="dashicons dashicons-yes-alt"></span>
                                    Site registered with definitions server
                                    <p class="registration-date">Registered on: <?php echo esc_html(date('F j, Y', strtotime($is_registered['created_at']))); ?></p>
                                </div>
                            <?php else: ?>
                                <div class="registration-status not-registered">
                                    <span class="dashicons dashicons-warning"></span>
                                    Site not registered with definitions server
                                </div>
                                <button id="register-site" class="button button-secondary">Register Site</button>
                            <?php endif; ?>
                            
                            <div id="registration-message"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Full Width Results Section -->
        <div class="guardian-gaze-card full-width" style="display: none;">
            <div id="guardian-gaze-results" class="guardian-gaze-results">
                <h2><?php _e('Scan Results', 'guardian-gaze'); ?></h2>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('File', 'guardian-gaze'); ?></th>
                            <th><?php _e('Line', 'guardian-gaze'); ?></th>
                            <th><?php _e('Pattern', 'guardian-gaze'); ?></th>
                            <th><?php _e('Match', 'guardian-gaze'); ?></th>
                            <th><?php _e('Severity', 'guardian-gaze'); ?></th>
                        </tr>
                    </thead>
                    <tbody id="guardian-gaze-results-body">
                    </tbody>
                </table>
            </div>

            <div id="guardian-gaze-error" class="guardian-gaze-error notice notice-error" style="display: none;">
            </div>
        </div>
    </div>
</div>

<style>
.registration-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}
.registration-status {
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 10px;
}
.registration-status.registered {
    background-color: #f0f6e9;
    color: #2c5a1e;
}
.registration-status.not-registered {
    background-color: #fef8ee;
    color: #975810;
}
.registration-date {
    font-size: 12px;
    margin: 5px 0 0 0;
    opacity: 0.8;
}
#registration-message {
    margin-top: 10px;
    display: none;
}
#registration-message.success {
    color: #2c5a1e;
    background: #f0f6e9;
    padding: 10px;
    border-radius: 4px;
}
#registration-message.error {
    color: #cc1818;
    background: #fef7f7;
    padding: 10px;
    border-radius: 4px;
}
.file-path {
    font-family: monospace;
    word-break: break-all;
    background: #f0f0f1;
    padding: 2px 6px;
    border-radius: 3px;
}
#gg-current-operation {
    color: #2271b1;
    font-weight: 500;
}
.stat-box.full-width {
    margin-bottom: 10px;
}
</style>

<script>
jQuery(document).ready(function($) {
    $('#register-site').on('click', function() {
        const $button = $(this);
        const $message = $('#registration-message');
        
        $button.prop('disabled', true).text('Processing...');
        $message.removeClass('success error').hide();
        
        $.ajax({
            url: ajaxurl,
            type: 'GET',
            data: {
                action: 'guardian_gaze_register_site',
                nonce: guardian_gaze.nonce
            },
            success: function(response) {
                if (response.success) {
                    $message.addClass('success')
                        .html('<span class="dashicons dashicons-yes"></span> Site successfully registered with definitions server!')
                        .show();
                    
                    // Update UI to show registered status
                    $('.registration-status')
                        .removeClass('not-registered')
                        .addClass('registered')
                        .html('<span class="dashicons dashicons-yes-alt"></span> Site registered with definitions server' +
                              '<p class="registration-date">Registered on: ' + new Date().toLocaleDateString() + '</p>');
                    
                    $button.text('Update Definitions');
                } else {
                    $message.addClass('error')
                        .html('<span class="dashicons dashicons-warning"></span> ' + (response.data || 'Registration failed'))
                        .show();
                }
            },
            error: function() {
                $message.addClass('error')
                    .html('<span class="dashicons dashicons-warning"></span> Failed to connect to server')
                    .show();
            },
            complete: function() {
                $button.prop('disabled', false);
            }
        });
    });
});
</script> 