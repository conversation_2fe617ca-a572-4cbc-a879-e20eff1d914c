<?php
// guardian-gaze-server/install.php
exit;
// Database configuration
$db_host = 'localhost';
$db_name = 'rafay';
$db_user = 'root';
$db_pass = '12345678';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Create patterns table
    $sql = "CREATE TABLE IF NOT EXISTS malware_patterns (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        pattern TEXT NOT NULL,
        description TEXT,
        severity ENUM('low', 'medium', 'high') DEFAULT 'medium',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
    
    $pdo->exec($sql);

    // Create registered_sites table
    $sql = "CREATE TABLE IF NOT EXISTS registered_sites (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(255) NOT NULL,
        site_url VARCHAR(255) NOT NULL,
        api_key VARCHAR(64) NOT NULL,
        last_fetch TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_email_site (email, site_url)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
    
    $pdo->exec($sql);

    echo "Tables created successfully!";
} catch(PDOException $e) {
    echo "Error: " . $e->getMessage();
}