{"regex": [{"id": "1", "name": "COOKIE preg_match function_exists", "regx": "/<\\?[ph\\s]+(function\\s+([a-z_0-9]+)[\\s\\(]+(\\$[a-z_0-9]+)*[\\s\\)\\{]+((\\$[a-z_0-9]+)\\s*=\\s*implode\\([^;]+|(array_[^;]+;\\s*)*srand\\(([a-z_0-9]+)\\(\\)\\));.+?(if[\\s\\(]+isset[\\s\\(]+\\$_COOKIE\\[.+?|return (implode\\([^;]+|\\5);[\\}\\s]+))*(if[\\s\\(\\@]+preg_match\\(.+?if\\[\\s\\(\\@]+preg_match\\(.+?if[\\s\\(\\@]+function_exists\\(.+?(\\s+\\}){3}|(echo|print|die)[\\s\\(]+\\2[\\s\\(]+array[\\s\\(0-9,\\)]+;\\s*|function\\s+\\7\\(.+?return[^;]*;[\\s\\}]+((header|echo)\\([^;]*;\\s*)+)($|\\?>)/is"}, {"id": "2", "name": "script googleblogcontainer eval", "regx": "/<script[^>]+(src=['\"htps:]+\\/\\/(propu|go|bodelen|dolohen)\\.[^\\?]+\\?(zone|id|p|z)+=\\d+['\"\\&][^>]*>|(>\\(function\\(\\)\\{var n,x,e=\\[|id=\"googleblogcontainer\").+eval[\\)\\s]*\\(.+)\\s*<\\/script>\\s*/i"}, {"id": "3", "name": "include php5.php alone", "regx": "/<\\?[ph\\s]+(if\\s*\\(is[^\\)]+[\\)\\s\\{]+|\\s*\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/)*[\\@\\s]*include[\\s\\('\"]+(.+?\\.i(c|\\\\143)o|'php5\\.php)[\"'\\)]+;\\s*(die[^;]*;|\\1)*[\\s\\}]*($|\\?>)/i"}, {"id": "4", "name": "document.write iframe small", "regx": "/(document\\.write\\(['\"])?<iframe src=['\"]http:\\/\\/(.+?)( (height|width)=['\"]?[0-5]['\"]?)+( style=['\"]visibility:[\\t ]*hidden[^>]*><\\/iframe>|><\\/iframe>['\"]\\));*/i"}, {"id": "5", "name": "document.write iframe .php5", "regx": "/<script[^>]*>[\\s\\<\\!\\-]*document\\.write[\\(\\s]+unescape[\\(\\s'\"]+[\\%0-9a-f]+['\"\\)\\s;]+[\\s\\-\\/\\>]*<\\/script>/i"}, {"id": "6", "name": "array eval", "regx": "/<\\?(?:php)?(?<A>.*?(?<C>\\s*+\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/)++.*+\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++|(?:function\\s++(\\w++)[^\\{]++[^\\}]++)?[\\}\\{\\s]++|(?:return|\\$\\w++\\s*+=\\s*+['\\d\"])[^;]*+;\\s*+)*+(?:(\\$[\\{\\$]*+[\\-\\>\\.\\w]++)[\\}\\s\\.]*+=\\s*+(?:(?:array(?:_map)?|\\$\\w++)*+\\s*+\\([^\\)]*+\\)[\\)\\s]*+(?:,[^\\)]*+\\)[\\)\\s]*+)*+);\\s*+)++\\@?eval(?&C)*+\\s*+\\(.*?\\4[^\\)]*+[^;]*+;\\s*+(?:(?&A)++|\\3\\s*+\\([^\\)]*+[^;]*+;\\s*+)*+(?:$|\\?>)/i"}, {"id": "7", "name": "document.write iframe .ru", "regx": "/(document\\.write|echo)[\\(\\s]+['\"]<iframe .+(left\\:\\s*-|.ru\\/).+<\\/iframe>['\"][\\s\\);]+/i"}, {"id": "8", "name": "eval hex", "regx": "/<\\?[ph\\s]*+(?:\\@*+\\$[\\{\"]*+([a-z\\\\_0-9]++)(?:[\"\\}\\s]*\\[[^\\]]*+\\])*[\\}\\s\\.]*=[a-z_0-9\\s\\(]*+[\\$\\{\\s]*+[\"'][^;]*?(?:\\\\(?:x[0-9a-f]{2}|\\d{2,3}))++.*)*eval\\s*\\([a-z_0-9\\s\\(]*+(?:[\\{\\$]+\\1|([\"'])[^;]*?(?:\\\\(?:x[0-9a-f]{2}|\\d{2,3}))++[^;]*\\2)[\\]\\}\\);\\s]++.+($|\\?>)/i"}, {"id": "9", "name": "function_exists emo", "regx": "/<\\?[ph\\s]*(if \\(\\!function_exists\\('emo'\\).+exit;\\}|wp_foots\\(\\);)\\s*\\?>/i"}, {"id": "10", "name": "function_exists base64_decode eval", "regx": "/<\\?[ph\\s]*(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/[^\\n]*\\s*)*(\\$[a-z_0-9]+\\s*=[^;]+[;\\s]+|(\\$[\\$\\{]*[a-z_0-9]+\\}*(\\s*\\[[^\\]]+\\])*)\\s*=\\s*array)*.*?(if\\s*\\(\\s*\\!function_exists\\s*\\(.+[\\)\\s]+[\\{\\s]*)?function\\s*[a-z_0-9]+\\(.+[\\)\\s]+[\\{\\s\\$a-z_0-9\\}\\s\\=\\@]*base64_decode\\s*\\(.+eval\\s*\\(.+[\\);\\s\\}]+($|\\?>)/i"}, {"id": "11", "name": "echo gzinflate base64_decode", "regx": "/(<\\?.*?|\\s*\\#[a-z_\\-=0-9]+\\#|\\s*\\$[a-z_0-9]+\\s*=[^;]+;|\\s*\\@?eval\\s*\\(.*?){2,}[\\s\\@]*+(echo[\\(\\s\\@]+)?gz[ui]n(compress|flate)[\\(\\s\\@]+base64_decode[\\(\\s]+.+[\\)\\s]+;\\s*(\\#\\/[a-z_\\-=0-9]+\\#\\s*|\\?>|$)+/i"}, {"id": "12", "name": "preg_replace /e alone", "regx": "/<\\?[ph\\s]*((\\@?error_reporting\\(|\\(\\$[a-z\\_0-9]+\\s*=\\s*\\$_(REQUES|GE|POS)T\\[)[^\\)]*[\\)\\&;\\s]+)?\\@?preg_replace[\\( \\t]+(['\"])([\\!\\/\\#\\|\\@\\%\\^\\*\\~]).+?\\5[imsx]*e[imsx]*\\4[ \\t]*,[^,]+,[^\\)]+[\\);\\s]*(\\?>|$)/i"}, {"id": "13", "name": "preg_replace /e hex", "regx": "/<\\?(?:php)?\\s*+(?:(?:(?<C>(?:\\#|\\/\\/)[^\\r\\n]*+\\s++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+)|(\\$\\w++)(?:\\s*+\\[[^\\]]*+\\]++)*+[\\s\\.]*+=[^;]++;\\s*+|\\@?(?:error_reporting|set_time_limit|ignore_user_abort)\\s*+\\([^\\)]*+\\)++;\\s*+|function\\s++(\\w++)[^\\{]++[\\{\\s]++.+\\s*+return[^;]*+[;\\s\\}]++|(\\$\\w++)\\s*+=\\s*+(?:\\3|range|array)\\s*+\\((?:[^\\)]*+\\)(?!;))*+[^\\)]*+\\);\\s*+|for(?:each)?[\\s\\(]++[^\\{]*?\\4[^\\{]++[\\s\\{]*+\\2[^\\}]*+[\\s\\}]*+|if[\\s\\(\\!\\@]++extension_loaded[^\\{]++[^\\}]++[\\}\\s;]++|echo[\\s\\('\"]++[^;]++;\\s*+)*+(?:\\$\\w++\\s*+=\\s*+)?\\@?(?:\\$\\w++(?:\\s*+\\[[^\\]]*+\\]++)*+\\s*+\\(\\s*+(?:[\"']{2}|\\$\\w++)*+|preg_replace\\s*+\\(\\s*+(['\"])([\\!\\/\\#\\|\\@\\%\\^\\*\\~]|\\\\[x\\d]{1,3}).*?\\6[imsx]*+(?:e|\\\\x65|\\\\145)[imsx]*+\\5)\\s*+,\\s*+(?:['\"]\\\\x[A-F\\d]{2}[^,]++|['\"]?\\$(?!cb, \\$encoded_value\\[\\$key\\]\\);)(?!repl\\.';)\\w++(?:\\s*+\\[[^\\]]*+\\]++)*+(?:[\\s\\.]*+\\$\\w++(?:\\s*+\\[[^\\]]*+\\]++)*+)*+\\s*+\\()[^\\)]++[^;]++[\\);\\s]*+(?:(?:die|exit)[^;]*+[\\);\\s\\}]*+)*+)++(?:$|\\?>\\s*+)/i"}, {"id": "14", "name": "preg_replace /e str_replace hex", "regx": "/\\@?preg_replace\\s*\\(\\s*['\"].+[\\/\\#\\|][is]*e[is]*['\"]\\s*,\\s*\\@?(\\$_(REQUES|GE|POS)T\\[|str_replace\\((['\"\\s,\\.\\$]*\\\\x[0-9A-F][0-9A-F])+).*\\)[\\s;]*/i"}, {"id": "15", "name": "eval fromCharCode", "regx": "/(<script[^>]*>\\s*(((var\\s*)?[a-z_0-9]+\\s*(;\\s*[a-z_0-9]+\\s*=\\s*[a-z_0-9][\\s\\.]*length|[\\.=]+\\s*([\"']).*?\\6\\s*|[,=]+\\s*\\[[^\\]]*\\]+\\s*)+;\\s*)+for[^\\{]+\\{\\s*[^\\}]+fromCharCode\\([^\\}]+[\\}\\s]+([a-z_0-9]+\\s*=[^;]+;\\s*)*document\\.write\\(|(document\\.write|eval)\\([^;]*fromCharCode\\()[^;]+;\\s*<\\/script>\\s*)+/i"}, {"id": "16", "name": "ini_restore base64_decode", "regx": "/<\\?[ph\\s]+ini_restore\\s*\\(.+\\s+.+base64_decode\\s*\\(.+\\s+.+php\\.ini.+\\s+.+fwrite\\s*\\([\\S\\s]+\\?>/i"}, {"id": "17", "name": "error_reporting variable-function", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|(ini_set|error_reporting|set_time_limit)\\([^\\)]*\\);\\s*)+if[\\(\\s]+\\!defined\\([^\\)]+[\\)\\s]+\\{\\s*define\\([^\\)]+[\\)\\s]+;\\s*if[\\s\\(]+\\!function_exists\\(([\"']).+?\\4\\)[\\)\\s\\{]*function\\s*(.+?)\\([^\\)]+\\)[\\)\\s\\{]+.+?\\5\\([^\\)]+\\);\\s*(\\$[^=\\(;]+)\\([^\\)]*\\)+;\\s*.+\\6\\([^\\)]*\\)+;\\s*return[^;]+;(.*$|\\s*\\?>(.*$)?)/i"}, {"id": "18", "name": "echo script iframe ", "regx": "/\\#[a-z_\\-=0-9]+\\#[\\s\\@]+echo.+<script.+\\.createElement[\\(\\s\"']+iframe.+\\.style\\.(left|top)=['\"\\\\]+-.+<\\/script>.+;\\s+\\#\\/[a-z_\\-=0-9]+\\#/i"}, {"id": "19", "name": "eval _REQUEST", "regx": "/<\\?(?:php)?(?:(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/|\\s++|(?:\\#|\\/\\/)[^\\n]*+\\s++)*+|\\@++)*+(?:ini_set|error_reporting|set_time_limit|ignore_user_abort)(?:(?:.+?\\$_(?<P>REQUEST|GET|POST|SERVER)\\[base64_decode){3}(?:.+?header\\(base64_decode|.+?fwrite\\(.+?exec\\()++|.+?base64_decode\\(.+?\\beval(?&C)*+\\((?&C)*+\\$_(?&P)\\[).++(?:[\\{\\s]*+echo[^;]++;)*+\\s*+(?:$|\\?>)/i"}, {"id": "20", "name": "foreach eval array", "regx": "/(?:<\\?[ph\\s]++|\\/\\*\\s++([\\w\\-]++)\\s*\\*\\/\\s*+)++function\\s++(\\w++)\\(\\s*+(\\$\\w++)[^\\)]*+[\\)\\s]++(?:(?:\\{++\\s*+|(?=function\\s++(\\w++)\\())(?:[^\\n]++\\s++)*?(?:(\\$\\w++)\\s*+=\\s*+(?:base64_decode|\\2)\\s*+\\([^;]++[;\\}\\s]++)?return\\s++(?:\\3|\\5);[;\\}\\s]++)++(?:(\\$\\w++)\\s*+=\\s*+\"[^\"]*+\";[;\\}\\s]*+)*+(\\$\\w++)\\s*+=\\s*+(?:\\2|\\4|\\6)\\s*+\\(\\s*+[^;]++;\\s*+(?:if[\\s\\(]++\\7[\\)\\s\\{]++)?\\@?eval[\\s\\(]++\\7[^;]++['\"\\)\\};\\s]++(?:\\/\\*\\s*+\\/\\1\\s*+\\*\\/|.*?($|\\?>))/i"}, {"id": "21", "name": "excesive spaces in hashed block", "regx": "/\\#\\s*([a-z0-9]+)\\s*\\#[\\s]{50}.+\\s+\\#\\/\\s*\\1\\s*\\#/i"}, {"id": "22", "name": "Javascript obscure eval array", "regx": "/(\\/\\*\\s*([0-9a-f]{32})\\s*\\*\\/\\s*|<(script)[^>]*>\\s*)*var\\s+[a-z_0-9]+\\s*=(\\s*[\\[,]\\s*(['\"])(\\\\?x[0-9a-f]{2})*\\5)+[\\s\\]]+;\\s*document\\s*((\\[[^\\]]+[\\]\\s]+)+(\\(([a-z_0-9]+(\\[[^\\]]+[\\]\\s]+)*)*)+\\)+[;\\s]*)+(\\/\\*[\\s\\/]*\\2\\s*\\*\\/\\s*|<\\/\\3[^>]*>\\s*)*/is"}, {"id": "23", "name": "JavaScript function xViewState", "regx": "/([\\s]{50}<script[^>]*>\\s*eval\\s*\\(\\s*function\\s*\\(|function\\s+[a-z0-9]+ViewState\\(\\))(.+\\s*)+?<\\/script>/i"}, {"id": "24", "name": "add-div-content Via<PERSON>", "regx": "/<\\!--start-add-div-content[0-9]*-->.+Viagra.+Cialis.+<\\!--end-add-div-content[0-9]*-->/i"}, {"id": "25", "name": "javascript array eval", "regx": "/<script[^>]*>[^<]*(((['\"])(\\\\x[a-f0-9]{2})+\\3\\]|['\"\\\\,0-9A-Fx]{200}|(var\\s+([a-z_0-9]+)\\s*=\\s*)?(['\\],\\[\"\\\\x]+[0-9A-F]+){200}.+?(String[\\[\\s]+\\4[\\[\\s\\]0-9]+|eval)\\s*\\()|(eval.+?[0-9\\s\\,]{300})).+?<\\/script>/i"}, {"id": "26", "name": "isset REQUEST eval alone", "regx": "/<\\?[ph\\s]+(\\$[_\\-\\>\\.a-z0-9]+\\s*=\\s*(['\"]).+?\\2;\\s*)*if[\\s\\(]+([a-z_0-9]+\\s*\\(\\s*)*\\$_(REQUES|GE|POS)T\\[.+(system|eval)\\(.+\\s*exit[^;]*[;\\s*\\}]+($|\\?>)/i"}, {"id": "27", "name": "isset HTTP_USER_AGENT header alone", "regx": "/if\\s*\\([^\\{\\n]*\\$_SERVER\\[['\"]HTTP_USER_AGENT['\"]\\][^\\}\\n]+(\\}\\s*else\\s*|\\{\\s*)*\\s*header\\(['\"]Location: .+?;[\\}\\s]+/i"}, {"id": "28", "name": "strrev Assert eval base64", "regx": "/[\\r\\n]++(?:[^\\r\\n\\(]++|\\((?!(?<E>[\"'](\\\\145|e)(\\\\166|v)(\\\\141|a)(\\\\154|l)(\\\\050|\\()(\\\\142|b)(\\\\141|a)(\\\\163|s)(\\\\145|e)(\\\\066|6)(\\\\064|4)(\\\\137|_)(\\\\144|d)(\\\\145|e)(\\\\143|c)(\\\\157|o)(\\\\144|d)(\\\\145|e)(\\\\050|\\())))++\\((?&E).+?\\\\051\\\\051\\\\073[\"\\\\'\\);]++(?<=\\);)/i"}, {"id": "29", "name": "Retry base64_decode Curl", "regx": "/<\\?[\\shp]*\\@?error_reporting\\([\\s0]+\\);\\s*((\\$[a-z_0-9]+\\s*=\\s*)?(urldecode[\\s\\(]+)?\\$_COOKIE\\[[^\\]]+\\]+[\\);\\s]+)+.+mail\\([^\\)]+\\)+[\\s\\{]+post_stats\\((.+?function\\s+(post_stats|_host2int|mch|smtp_lookup|post_mch)){5}.+socket_close\\([^\\)]+\\)+[;\\s\\}]+die\\(\\);[\\s\\}]*($|\\?>)/is"}, {"id": "30", "name": "preg_replace all hex", "regx": "/(\\$[a-z_0-9]+\\s*=)?[\\s\\@]*preg_replace\\s*\\(\\s*['\"](.).*?\\2([^\\)]*?\\\\x[0-9A-F]{2}){13,}.+?\\);/i"}, {"id": "31", "name": "iframe in head", "regx": "/\\<iframe .+\\<\\/iframe\\>\\s*(?=\\<\\/h(3ml|ead)\\>)/i"}, {"id": "32", "name": "Tagged script try document.body eval", "regx": "/<\\!--[a-z_0-9\\s]+-->\\s*<script .+?(bdv_ref_pid=([0-9]+);.+?<\\/script>\\s*<script .+?pid=\\2|try\\{document\\.body.+?eval).+?<\\/script>\\s*(<noscript.+<\\/noscript>\\s*)?<\\!--[\\/a-z_0-9\\s]+-->/i"}, {"id": "33", "name": "Tagged try document.body eval", "regx": "/\\/\\*\\s*([0-9a-f]+)\\s*\\*\\/\\s*.*?try\\{document\\.body.+?eval.+?\\s+\\/\\*[\\s\\/]+\\1\\s*\\*\\//i"}, {"id": "34", "name": "eval variable-function long-nb-string", "regx": "/(?:\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++|(?:\\$[\\w\\[\\]\\{\\}'\"]++\\s*+=|f(?:open|write|close)\\s*+\\()[^;]++;\\s*+)*\\@?(?:eval|assert)\\s*+\\(\\s*+(?:\\$[\\w\\[\\]\\{\\}'\"]++\\s*+\\(\\s*+)++['\"][\\w\\/\\-\\+\\=\\s]{200,}['\"]\\)++;\\s*+/i"}, {"id": "35", "name": "function ob_get_level ob_start add_action", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/[^\\n]*\\n\\s*|\\$[a-z_0-9]+\\s*=[^;]+;\\s*|error_reporting\\([^;]+;\\s*|define\\([^;]+;\\s*)*(((add_action|add_submenu_page)\\s*\\([\\s'\"a-z_0-9]+\\\\x[0-9a-f]{2}[^;]+[\\);\\s\\}]*)+(echo[\\(\\s'\\\\\"a-z_0-9]+\\\\x[0-9a-f]{2}[^;]+[\\);\\s\\}]*)*)+($|\\?>)/i"}, {"id": "36", "name": "head script document.write", "regx": "/(?<=\\<\\/head\\>)\\<script.+?document\\.write\\(.+?\\<\\/script\\>/is"}, {"id": "37", "name": "script http IP", "regx": "/(?<!['\"])(?:(?:<script(?:[^>]*+>\\s*+(?:var\\s++(?:popunder|pm_tag|pm_pid)\\s*+=[^;]++;[\\}\\s]*+)++<\\/script>\\s*+<script)?[^>]*?|importScripts\\s*+(?=(\\()\\s*+(['\"]).+(\\2\\s*+\\)\\s*+;\\s*+)))(?:(?:\\1\\s*+|src=)['\"fhtps\\:]++\\/(?:(?:[\\/|\\.]\\d++){4}|\\/(?:[\\w\\-]++\\.)?(?:[\\w\\-]+?prize\\d*+\\.life|is\\.gd|mockingcard\\.com|onlinekey\\.biz|betamedia\\.biz|lowerthenskyactive\\.ga|js\\.donatelloflowfirstly\\.ga|ofgogoatan\\.com|js\\.digestcolect\\.com|3vwp\\.org|hotopponents\\.site|onclasvr\\.com|pushsar\\.com|scripts\\.trasnaltemyrecords\\.com|examhome\\.net|uustoughtonma\\.org|voipnewswire\\.net|allyouwant\\.online|eeduelements\\.com|cdns\\.ws|adsptp\\.com|json\\.gdn|cloudflare\\.solutions|coin[\\-]?hive\\.com|freehtml5templates\\.com|i7wp\\.org|locationforexpert\\.com|stringengines\\.com|(?:wfcs|xms)\\.lol))\\/|\\/wp-includes\\/js\\/jcrop\\/jquery\\.js|>.*http:\\/\\/mbs-support\\.com\\/js\\/jquery\\.min\\.php.*document\\.write\\([\"']<script.*\\/script)(?:.*?\\3|[^>]*+>\\s*+(?:<\\/script>\\s*+<script[^>]*>\\s*+var\\s++(\\w++)\\s*+=\\s*+new\\s++CoinHive\\.Anonymous[\\('\"\\s]++)?.*?(?:\\s*+\\4\\.start\\(\\);[\\s\\}]*+)?<\\/script>))++/i"}, {"id": "38", "name": "script encode eval", "regx": "/(var\\s*([a-z_0-9]+)\\s*=\\s*function[^\\{]+\\{\\s*)?var\\s*[_\\-\\>\\.a-z0-9]+\\s*=\\s*(String)?\\[\\s*['\"](\\\\x[0-9A-F]{2}|[^\\]]*?fromCharCode)+[\"'][^\\]]*\\].+?(\\{a[0-9]*|\\2|eval)\\s*\\([^\\)]*[\\)\\s\\}]+;*/i"}, {"id": "39", "name": "Tagged base64_decode file_get_contents position iframe", "regx": "/((\\/\\*|\\#)\\s*([a-z_0-9]+\\s*(\\2|\\*\\/))\\s*.+?base64_decode.+?\\s*.+?file_get_contents.+?\\s*.+?position.+?\\s*.+?<\\/iframe>.+\\s*(\\/\\*|\\#)[\\/\\s]*\\3|if\\s*\\([^\\{]*((google|bot|yahoo|bing|HTTP_USER_AGENT)[^\\{]+){5,}\\{((\\$[a-z_0-9]+[\\s\\.\\+]*=\\s*)?(shuffle|array)\\([^;]+;\\s*)*foreach\\([^\\{]+\\{\\s*if\\s*\\(preg_match\\s*\\([^\\{]+\\{\\s*.+?([\\@\\~\\s]*(base64_decode|file_get_contents)\\s*\\(){3}.+?\\s*(\\}\\s*){3})/i"}, {"id": "40", "name": "script ajax POC", "regx": "/<script[^>]+(VBScript.+?CreateObject\\(['\"]Scripting\\.FileSystemObject['\"]\\).+?\\.CreateTextFile\\(.+?\\.Write.+?CreateObject\\(['\"]WScript\\.Shell['\"]\\).+?|ajax.php['\"]>['\"]POC['\"]|>\\s*(\\$[\\=\\~\\[\\]\\{\\}\\(\\)_\\:;\\+'\"\\!\\?\\.,\\|\\/\\\\]*){20,}(?<=\\)\\(\\);))\\s*<\\/script>/is"}, {"id": "41", "name": "targets array JAPluginDone", "regx": "/(\\/\\/files\\s+)?\\$targets\\s*=\\s*array\\(.+?echo[\\s\"']+JAPluginDone[\\s\"';]+/is"}, {"id": "42", "name": "include favicon", "regx": "/(?:\\/\\*\\s*+([a-z_\\-0-9\\=]{5,32}\\s*+\\*\\/)|(?:[\\n\\r]|(?<=;|<\\?php))(;))[\\s\\@]*+include(?:_once)?[\\s\\(]++([\"']).*(?<!\\.php)\\3[\\)\\s]*+(?:\\2|;\\s*+\\/\\*[\\/\\s]*+\\1)/i"}, {"id": "43", "name": "add_filter cred", "regx": "/add_filter\\('template_include','get_cred',1\\);\\s+add_filter\\('shutdown','cred',0\\);/i"}, {"id": "44", "name": "preg_replace strrev e", "regx": "/(\\$|var\\s+)[a-z_0-9\\s\\.]+=\\s*(['\"]e\\/\\*\\.\\/['\"];\\s*preg_replace\\(\\s*strrev\\(.*\\);|[\\[ary]+([\"\\s'\\,]*\\\\(x[0-9a-f]{2}|[0-9]{2,3}))+[\"\\s'\\];]*((\\[*[a-z_0-9]+\\[)+[0-9]+\\]+\\s*\\([^\\)]*\\)+[;\\s]*)+$)/i"}, {"id": "45", "name": "function_exists get file function curl_init file_get_contents fopen curl_exec", "regx": "/<\\?[ph\\s]+((ini_set|\\$[a-z_0-9]+\\s*=)[^;]+;\\s*)*function\\s+([a-z_0-9]+)[^\\{]+[\\s\\{]+(\\$[a-z_0-9]+\\s*=[^;]+;\\s*)*(foreach[^\\{]+[\\s\\{]+(\\$[a-z_0-9]+\\s*(\\[[^\\]]+\\]+\\s*)*=\\s*)?curl_init.+?return[^;]*;|(return\\s+)?curl_[^;]+;\\s*)+[\\}\\s]+((\\$[a-z_0-9]+\\s*=\\s*)?(\\$_SERVER\\[|\\3\\(|fopen\\(|fwrite\\(|fclose\\()[^;]+;\\s*)*(if[\\s\\(]+(file_exists\\(|\\$_(REQUES|GE|POS)T\\[).+?\\3\\(.+){2}/is"}, {"id": "46", "name": "error_reporting include wp-apps", "regx": "/((\\$[a-z_0-9]+[\\s=]+)?(error_reporting|ini_set|getenv|substr)\\([^\\)]*\\)+;\\s*)*\\@*(require|include)(_once)?[\\(\"'\\s]+[^;\\n]+wp-(includes\\/([int]+|(css|js|images)\\/[^;]+)|head|apps|text|admin\\/(css|js|images)\\/[^;]+)+\\.(php|js|css)[\"'][\\s\\)]*;\\s*(\\@(require|include)(_once)?[\\(\\s]+[\"'][^;]+;\\s*)*/i"}, {"id": "47", "name": "require cgi-local php comment alone", "regx": "/<\\?[ph\\s]+(\\/\\*.+?\\*\\/\\s*|\\@)*(require|include)(_once)?[\\(\\s]+(\\$_SERVER[\\[\\{][\"']DOCUMENT_ROOT['\"][\\]\\}][\\s\\.]+[\"'][\\.\\/]*wp-[^;]++|['\"]cgi-local\\/[^;]+?\\.php['\"][\\s\\)]*);\\s+(\\#.*\\s*)*\\?>/i"}, {"id": "48", "name": "ob_start gzinflate ob_get_contents ob_end_clean eval", "regx": "/<\\?[ph\\s]*([if\\(\\s\\!]*define(d\\s*\\([^\\)]+|\\s*\\([^,]+,\\s*([a-z_0-9\\(]+))[^\\)]*[\\);\\s\\{\\}]+)*(\\@|\\$[a-z_0-9]+[\\s\\.]*=\\s*)*ob_start\\s*\\((['\"\\s]+(.*?)['\"\\s]+\\);\\s*function\\s+\\6\\(.+?function\\s+\\3.+return\\s*(['\"])[^\\7]*\\7|gzinflate[\\(\\s]+ob_get_contents[\\(\\);\\s]+ob_end_clean[\\(\\);\\s]+eval\\([^\\)]+[\\)\\s]*);[\\s\\}]*($|\\?>\\s*)/is"}, {"id": "49", "name": "tagged iframe 1px", "regx": "/<\\!-- .+? -->\\s*<iframe width=\"1px\" height=\"1px\" src=\"http:\\/\\/[^>]+>\\s*<\\/iframe>\\s*<\\!-- .+? -->/i"}, {"id": "50", "name": "script after closing body tag", "regx": "/(^|(?<=\\<\\/(body|head)\\>))(\\s*<(script|a[^>]*)>\\s*+(?!function copyText\\()(?!\\/\\/[^<\\n]*\\s*<\\/\\4>).+?<\\/\\4>\\s*)+(?=\\<(body|(\\/|\\!DOCTYPE\\s*)?html))/is"}, {"id": "51", "name": "var R function pYMuS window", "regx": "/<script[^>]*>\\s*(var\\s+)?([a-z_0-9]+)\\s*=\\s*\\[.+?(([a-z_0-9]+)\\s*(\\[[^\\]]+[\\]\\s]*)+=\\s*\\2\\[[^\\]]+[\\]\\s]+\\+.+window[\\[\\s]+\\2[\\[\\s]+[^\\]]+[\\]\\s]*(=\\4|,\\2[^\\]]+[\\]\\s\\)]+)[\\};\\s]+|function pYMuS\\(.+?\\)\\(window\\))<\\/script>/i"}, {"id": "52", "name": "Tagged echo script eval HexHex_", "regx": "/\\#(\\w++)\\#\\s++echo[\\s\\('\"]++<script.+?eval.+?(?:[a-z\\d]{2}\\_){100}.+?<\\/script>['\"\\s\\);]++\\#\\/\\1\\#/is"}, {"id": "53", "name": "variable create_function strrev", "regx": "/<\\?(?:php)?\\s*+(?:(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++)*+(?:(?:(\\$\\w++)\\s*+=)?.*function\\s*+(\\w*+)\\s*+\\([^\\{]++\\{\\s*+(?:[^r]++|r(?!eturn))*+(?:return[^;]*+;\\s*+(?:\\}\\s*+)++)++|class\\s*+(\\w*+)\\s*+\\{\\s*+(?:const\\s++[^;]++;\\s*+)*+)++)++(?:(?&C)|(?:(\\$\\w++)\\s*+=\\s*+)?(?:hex2bin|file_..t_contents|\\4\\s*+(?:::|->)\\w++)\\s*+\\([^;]++;\\s*+)*+(?:eval\\((?:\\3\\s*+\\(|\\5)|base64_decode\\(|\\2\\s*+\\(\\s*+strrev\\().*?(?:\\)\\s*+)++;\\s*+(?:eval\\(.+?(?:\\)\\s*+){2,};\\s*+|\\$\\w++\\s*+=[^;]++;\\s*+|\\1\\s*+)*+(?:$|\\?>\\s*+)/i"}, {"id": "54", "name": "html embed object html", "regx": "/(<(html|head|title)>\\s*)+(hack[3e]d.by[^\\n]*\\s*(<\\/[a-z]+>\\s*)+.+)+<(script|object|embed|iframe).+<\\/(script|object|embed|iframe)>\\s*(<\\/(html|body|center)>\\s*)+/is"}, {"id": "55", "name": "require new SAPE_client return_links", "regx": "/(\\$[a-z_0-9]+)\\s*=\\s*new\\s*SAPE_client\\(.+?\\1->return_links\\(\\);/s"}, {"id": "56", "name": "if function_exists _php_cache_speedup_func_optimizer_ register_shutdown_function ob_end_flush", "regx": "/[;\\s]*if\\s*\\(\\!function_exists\\([' \"]+_php_cache_speedup_func_optimizer_[' \"]+\\)\\).+?register_shutdown_function\\([' \"]+ob_end_flush[' \"]+\\)[;\\s]*\\}/s"}, {"id": "57", "name": "error_reporting ini_set if count POST return", "regx": "/if[\\s\\(\\!]+(\\@*(DEFINE[d]*\\((['\"]).*?\\3|(\\$[a-z_0-9]+)\\s*(\\[[^\\]]*\\]\\s*)*=\\s*(['\"]).*?\\6|error_reporting\\()([\\)\\s\\{]+|[^;]*;[\\s\\}]*)){3,}.+?((\\$[a-z_]+)[0-9]*\\s*(\\[[^\\]]*\\]\\s*)*=\\s*['\"htps\\:]+\\/\\/[^;]*;[\\s\\}]*)+(.+?(file_get_contents|curl_exec|setcookie)\\(\\9[^;]*[;\\s]+){3,}((echo[\\s\\(\"]+\\9|exit)[^;]*;[\\}\\s]*)+(.+\\}[\\s\\}]*\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*|(\\@*(error_reporting|ini_set)\\([^;]+[;\\s]+){2}if[\\s\\(]+[a-z]+[\\s\\(]+\\$_POST.+return \\$[a-z0-9]+[;\\s\\}]+/i"}, {"id": "58", "name": "div <PERSON><PERSON> C<PERSON>is script style.display", "regx": "/<div id=['\"]([^>]*)['\"]>.*Viagra.+Cialis.*<\\/div>[\\r\\n \\t]*<script[^>]*>.*document\\.getElementById\\([\"']\\1[\"']\\)\\.style\\.display.*<\\/script>/i"}, {"id": "59", "name": "php variable array base64_decode function_exists numeric-named function", "regx": "/(\\/\\*([^\\*]*\\*[^\\/])*[^\\*]*\\*\\/\\s*|\\/\\/[^\\n]*\\s+)*\\$[a-z_0-9'\"\\[\\]\\s]+=\\s*array\\(.*?base64_decode\\(.+?\\)+;\\s*((if\\s*\\(\\!function_exists\\([\"']_[0-9]+[\"'][\\)\\s]+\\{\\s*|\\?>\\s*<\\?[ph\\s]*)*function ([a-z_0-9]+)\\(.+?\\}[\\}\\s]+((\\?>\\s*<\\?[ph\\s]*)*\\@?\\$GLOBALS\\s*(\\[[^\\]]*\\]+\\s*|\\{[^\\}]*\\}+\\s*)+\\([^;]+;\\s*)+.+?((\\$[a-z_0-9]+\\s*=\\s*)?(eval|\\$GLOBALS(\\s*\\[[^\\]]*\\]+\\s*|\\{[^\\}]*\\}+)+)\\s*\\(([^;]+)[\\);\\}\\s]+)+)+.+(\\1|\\5\\s*\\([^;]+;[\\);\\}\\s]*)/i"}, {"id": "60", "name": "Tagged if empty script eval echo", "regx": "/(?:\\#(\\w++)\\#\\s*+if[\\s\\(]++empty\\((\\$\\w++)[\\)\\s\\{]++\\2[\\s'\"=]++)?(?<S><script[^>]*?(?:>\\s*+\\{_0x\\w*+\\s*+=\\s*+function\\(_0x\\w*+[\\)\\s\\{]++return\\s++_0x\\w*+\\.toString\\(|>\\s*+eval[\\(\\s]++function\\(|src=[\\\\'\"]++http[s]?\\:\\/\\/\\w++\\.transandfiestas\\.ga\\/\\w++\\.js\\?\\w++=).+\\s*<\\/script>)[\\s'\";]*+(?:echo\\s*+\\2[\\s;\\}]++\\#\\/\\1\\#|(?&S)++|$)/i"}, {"id": "61", "name": "var HTTP_USER_AGENT if match string var else", "regx": "/(\\$\\w++)\\s*+=\\s*+\\$_SERVER\\[[\"']HTTP_USER_AGENT['\"]\\];\\s*+(?:.++\\s*+)*?if\\s*+\\([\\$\\w\\s]+\\([^\\)]*?\\1[^\\{]++\\{\\s*+(?:(?:header|exit|die|(\\$\\w++)\\s*+=[^;]++;\\s*+curl_\\w++\\s*+\\([^\\)]*?\\2[^\\)]*+|curl_\\w++\\s*+\\([^\\)]++)[^;]*+;\\s*+)++(?:\\}\\s*+else\\s*+\\{[^\\}]*+)?\\}/i"}, {"id": "62", "name": "div php error_reporting fopen http", "regx": "/<div [^>]*>\\s*<\\?[ph\\s]+error_reporting\\(.+?fopen\\([\"']http:\\/\\/.+?\\?>\\s*<\\/div>/is"}, {"id": "63", "name": "DOCUMENT_ROOT if file_exists file_get_contents gzinflate preg_replace", "regx": "/(\\$([a-z_0-9]+)\\s*=[^;]*;\\s*|echo[^;]*;\\s*|\\@*([a-z_0-9]+)\\s*\\(\\s*)+\\$_SERVER[\\s\\[\\{]+([\"'])(DOCUMENT_ROOT|SCRIPT_NAME)\\4[\\s\\]\\}]+.+?(function\\s+\\3\\s*\\([^\\{]+\\{\\s*|if[\\s\\(]+file_exists\\s*\\(.+?)\\$([a-z_0-9]+)\\s*=[\\s\\@]*(file_get_contents\\s*\\(\\s*\\$\\2.+?\\$([a-z_0-9]+)\\s*=[\\s\\@]*gzinflate\\s*\\(\\s*\\$\\7.+?preg_replace.+?\\);[\\}\\s]+|scandir\\s*\\([^;]+;\\s*foreach\\s*\\(\\$\\7.+(fwrite\\s*\\([^;]+;\\s*fclose|file_put_contents)\\s*\\([^;]+(;[\\}\\s]+(unlink\\s*\\(\\s*\\$\\2\\)|(\\$[a-z_0-9]+\\s*=[^;]*;\\s*)*(if[\\s\\(]+[^\\{]+\\{\\s*)*echo[^;]*))+;\\s*)/is"}, {"id": "64", "name": "function fourofour add_filter all_plugins fourofour_pp", "regx": "/<\\?(?=.+?add_filter[\\s\\(]++[\"']all_plugins[,\\s\"']++(?!eos_dp_plugins_in_list|wpematico_showhide_addons)([a-z_0-9]++)[\"']).+function\\s++\\1[\\s\\(]++(\\$[a-z_0-9]++).+?unset[\\s\\(]++\\2.+?return[\\s\\(]++\\2.+/is"}, {"id": "65", "name": "p payday loans", "regx": "/<p[^>]*>\\s*.+?payday loan.+?[\\r\\n]+\\s*<\\/p>/i"}, {"id": "66", "name": "script src earnmoneydo.com", "regx": "/(<(script|a)[^>]+(href=['\"][fhtpsl:]*\\/\\/(secure\\.payza)[^>]+>\\s*<img[^>]+)?src=['\"][fhtpsl:]*\\/\\/(\\4|stat\\.uustoughtonma|cdn\\.scriptsplatform||cdn\\.allyouwant|cdn\\.eeduelements|online-sale24|earnmoneydo|gccanada|g00)\\.[co].+?\\s*<\\/\\2>\\s*)+/i"}, {"id": "67", "name": "php var array var text if function_exists function foreach chr return variable function text", "regx": "/<\\?[ph\\s]++(?:\\/\\/[^\\n]*\\n\\s*|\\/\\*[^\\*]*(?:\\*[^\\*\\/]*)+\\/\\s*|(?:\\$[a-z_0-9]++(?:\\s*\\[[^\\]]++\\]++)*+[\\s\\.\\+\\-]*+=\\s*(?:array\\(|\\$\\{)?(?:(['\"]).*?\\1|[0-9\\,]+|null|[^;]{4321,};|create_function\\([^\\)]++|\\$[a-z_0-9]+(\\s*\\[[^\\]]+\\]+|\\s*\\(\\s*)*)['\\.\\,\\s\\)\\}]*)+;\\s*+|\\@*(error_reporting|ignore_user_abort|set_time_limit|header)\\s*\\([^\\)]*+[\\)\\s]++[^;]*+;\\s*|(?:if\\s*+\\([^\\{]++\\{\\s*+)?function[^\\{]++\\{.*?return[^;]*+;['\"\\);\\s\\}]++)+((\\$[a-z_0-9]++(\\s*+\\[[^\\]]++\\]++)*+[\\s\\.\\+\\-]*+=\\s*+)?\\$[a-z_0-9]++(\\s*+\\[[^\\]]++\\]++)*+\\s*+\\([^\\)]*+[\\)\\s]++(?!->)[^;]*+;[\\s\\}]*+)++((if\\s*+\\([^\\{]++\\{\\s*+)?(echo|print|die|for(each)?\\s*+\\([^\\{]++)[^;]*+;[\\};\\s]++)*+($|\\?>)/is"}, {"id": "68", "name": "Tagged error_reporting base64_decode", "regx": "/(\\/\\*.+?\\*\\/|<\\!--.+?-->)\\s*(if[\\( \\!]+defined\\([^\\)]+[\\) \\{]+.*?define\\([^\\)]+\\)+;[\\s\\}]*)*((\\@|\\$[a-z_0-9]+\\s*[\\.=]+)*(error_reporting|ini_set|ob_start)\\(.+?)+base64_decode\\(.+?\\1/is"}, {"id": "69", "name": "Tagged createElement script src appendChild", "regx": "/((<script[^>]*>|\\/\\*\\s*[0-9a-z]+\\s*\\*\\/|Element\\.prototype\\.appendAfter\\s*=\\s*function.*function[\\(\\)\\s]*\\{\\s*|var)\\s*)+([a-z_0-9]+)[\\s=]+document\\.createElement[\\(\\s]+(String\\.fromCharCode\\(.+?\\3\\.src[\\s=]+|['\"]script['\"].+?\\3\\.src[\\s=]+String\\.fromCharCode\\([^\\)]+\\)+;\\s*|var\\s*([a-z_0-9]+)[\\s=]+document\\.getElementsByTagName\\(['\"]script['\"]\\);\\s*var\\s*([a-z_0-9]+)[\\s=]+true;\\s*((for[\\(\\s]+(var\\s*)?([a-z_0-9]+)[\\s=]+\\5[^\\{]+\\{\\s*|if[\\(\\s]+|[\\}\\s]*else\\s*(\\{[^\\}]*[\\}\\s]+)?)*(\\5\\[\\10\\]\\.src|\\3\\.src|\\6)[\\s=]+(\\5\\[\\10\\]\\.src|\\3\\.src|true|false)[\\)\\s\\{;]+)+)+.+?\\.appendChild\\(\\3\\)[^;]*;\\s*([\\}\\(\\);]+\\s*|\\/\\*\\s*[0-9a-z]+\\s*\\*\\/\\s*|$|<\\/script>)+/i"}, {"id": "70", "name": "PHP Vars Concat Variable Function END", "regx": "/<\\?[ph\\s]+(?:(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|^\\s*+\\*\\/\\s*+)|\\@?error_reporting[\\(0\\)\\s]++;\\s*+|function\\s++\\w++\\([^\\{]++[\\{\\s]++)*+(?:\\$(?<V>\\w++(?:\\s*+\\[[^\\]]++\\]++)*+)[\\s\\.]*+=\\s*+(?:(['\"]).*?\\3|Array\\s*+\\(|\\$(?!this)(?&V))[^;]*+;\\s*+|if[\\s\\(]+isset[\\s\\(]++\\$_[^\\{]++\\{\\s*+)+?(?:echo|(?:\\$(?&V)\\s*+=)?[\\@\\s]*+(?:\\$(?&V)|str_replace|create_function)\\s*+\\([^\\)]*+).*?((\\$(?&V)\\s*+=)?[\\@\\s]*+(?<!\")\\$(?&V)\\s*+\\((?:.+;|[^;]*+;)\\s*+)+(?:(?&C)|exit;\\s*+|\\}\\s*+)*+(?:$|\\?>(?:\\s*<form[^>]*+(?:>\\s*+<input[^>]*+)++>\\s*+<\\/form>)?)/i"}, {"id": "71", "name": "div script document getElementById visibility hidden display none", "regx": "/<div id=['\"]([a-z\\_0-9]+)['\"].+?<\\/div>\\s*<script[^>]*>\\s*((function\\s(?!hidemessage)([a-z\\_0-9]+)|if)[\\s\\(]+[^\\)]*[\\)\\s\\{]*)?(document\\.getElementById\\([\"']\\1[\"']\\)\\.style\\.(visibility|display)\\s*=\\s*[\"'](hidden|none)[\"'];\\s*)+[\\s\\}]*<\\/script>/i"}, {"id": "72", "name": "add_action wp_footer serve example_admin_notice", "regx": "/(add_action\\(\\s*['\"](wp_footer|init|admin_notices)['\"][,\\s]+(\\@?create_function[\\s\\(]+)?['\"](.+?base64_decode.+?|example_admin_notice|serve)['\"][\\s\\)]+;\\s*){2,}/i"}, {"id": "73", "name": "PHP error_reporting if !isset variable function END", "regx": "/(?:(?:error_reporting\\s*+\\(|(\\$\\w++)(?:\\s*+\\[[^\\]]*+\\]++)*+\\s*+=)[^;]+;\\s*+)*+if\\s*+\\(.+?\\)+\\s*+\\{\\s*+(?:(\\$\\w++)(?:\\s*+\\[[^\\]]*+\\]++)*+\\s*+=[^;]++;\\s*+)+?(?:(\\$\\w++)(?:\\s*+\\[[^\\]]*+\\]++)*+\\s*+=[^;]++;\\s*+)*?(?:(\\$\\w++)(?:\\s*+\\[[^\\]]*+\\]++)*+\\s*+=++\\s*+)?(?:\\1|\\2|\\3)(?:\\s*+\\[[^\\]]*+\\]++)*+\\s*+\\([^;]*?(?:(?:(\\$\\w++)(?:\\s*+\\[[^\\]]*+\\]++)*+\\s*+=\\s*+|;\\s*+)*?\\4(?:\\s*+\\[[^\\]]*+\\]++)*+\\s*+\\([^\\)]*+\\)[^;]*+|;\\s*+(?:require|include)(?:_once)?[\\s\\(\"]++(?:\\1|\\2|\\3|\\4|\\5)[^;]*+)++;\\s*+\\}\\s*?(?:\\n|$)/i"}, {"id": "74", "name": "script if navigator userAgent match document write script src http", "regx": "/<script[^>]*>((\\/\\*\\s*[0-9a-z]+\\s*\\*\\/|var|const|\\s)*((_0x[a-z_0-9]+)[\\s=]+(\\[[^\\]]+[\\],\\s]+)+;[\\}\\s]*)+(((_0x[a-z_0-9]+)?\\(\\s*|(var\\s*)?\\s*(_0x[a-z_0-9]+)[\\s=]*function[^\\{]+\\{+\\s*)+[^;]*[;\\s\\}]+)+(var\\s*)?\\s*\\8[\\s=]+\\4.+\\10[^;]+[;\\s\\}\\(this\\)]+|if[\\s\\(]+navigator\\.userAgent\\.match\\(.+?\\{\\s*document\\.write\\([\"']<scr.+? src=['\"]http.+?\\)[;\\}\\s]+)<\\/script>/i"}, {"id": "75", "name": "php function Array return base64_decode php Variable function", "regx": "/^<\\?[ph\\s]+function base64[^\\{]+\\{\\s*return base64[^\\}]+\\}\\s*if[\\s\\(]+isset[\\s\\(]+\\$_(REQUES|GE|POS)T.+<body onload=[\\s'\"\\\\]+location[^>]+[^<]+<\\/body>\\s*<\\/html>\\s*<\\?[ph\\s]*\\}+\\s*($|\\?>)/is"}, {"id": "76", "name": "include_once rss-info.php", "regx": "/((\\$[a-z_0-9]+)\\s*=.+?[a-z_0-9]\\.(js|png|gif)['\"];\\s*)*(if[\\s\\(]+(is_|file|_exists){2,}[\\(\\s]+[^\\)]+([\\)\\s]+\\.[^\\)]+)*[\\)\\s\\{]+)?\\@?(include|require)(_once)?\\s*\\((dirname\\(__FILE|ABSPATH)?[_\\)\\.'\"\\/\\s]*(\\2.*?|((wp-content\\/uploads\\/.+?|wp-vcd|wp-content\\/plugins\\/contact-form-7\\/contact-form-7-sample-options)|class\\.plugin-modules|rss-info)\\.php['\"])[\\);\\s\\}]+(else)?/i"}, {"id": "77", "name": "is_bot __via_content)", "regx": "/(var\\s+([a-z_0-9]+)\\s*=[^;]+;\\s*)*(function\\s+([^\\(]*cookie[^\\(]*|[a-z0-9]{41})\\([^\\{]*\\{.*?((document.cookie[^;]+(;\\s*['\"])?|return|if\\s*\\(document\\.referrer[^\\{]+\\{\\s*\\2\\s*=)[^;]*;\\s*(\\}\\s*)+)+){5,}(\\(*function\\s*([\\(\\)]+|[a-z0-9]{41}\\([^\\{]*)\\s*\\{.*?\\);\\s*\\}[\\)\\s]*\\10\\s*;\\s*){2,}/is"}, {"id": "78", "name": "set var str_replace var variable function", "regx": "/((\\$[a-z\\_0-9]+(\\s*\\[[^\\]]+\\])*)\\s*=\\s*\\@?\\$[a-z\\_0-9]+(\\s*\\[[^\\]]+\\]+)*\\s*\\(\\@?\\$_(REQUEST|GET|POST|COOKIE)(\\s*\\[[^\\]]+\\]+)*\\);\\s*)+(\\$[a-z\\_0-9]+|preg_replace)\\s*\\(\\s*(['\"])([\\!\\/\\#\\|\\@\\%\\^\\*\\~]).+?\\9[imsx]*e[imsx]*\\8\\s*,\\s*\\2\\s*,(([\\$a-z\\_0-9]+(\\s*\\[[^\\]]+\\])*|'[^']*'|\"[^\"]*\")[\\.\\s]*)+\\)+;\\s*(die\\([^\\)]*\\)+;)?/i"}, {"id": "79", "name": "Tagged error_reporting curl_init file_get_contents fwrite script", "regx": "/<\\?[ph\\s]+([\\@\\/\\#\\s]*(error_reporting|ini_set|set_time_limit|header)\\s*\\([^\\)]*[\\);\\s]+)*((([;\\s]*(\\$[a-z_0-9]+\\s*=[^;]+;\\s*|else[\\s\\{]*))*if\\s*\\([^;]+)+((file_get_contents\\s*\\(|mkdir\\s*\\(|curl_[a-z]+\\s*\\(|die\\s*\\(|(echo|print)[^;]+;\\s*(return|exit))[^\\}]+[\\}\\s]*)+)+(else[\\s\\{]*)?(if[\\s\\(]+[^\\)]+[\\)\\s\\{]*)?(\\$[a-z_0-9]+\\s*=\\s*file_get_contents\\([^;]+;[\\}\\s]*)*((if\\([^\\)]+|echo[^;]+)[\\)\\{\\s;]+)*(\\/*(\\$[a-z_0-9]+\\s*=)?[\\@\\s]*(fopen|fwrite|fclose)\\s*\\([^;]+;[\\}\\s]*){3,}((header|echo|print|if\\s*\\([^\\{]+[\\{\\s]*chmod\\s*\\()[^;]*;[\\}\\s]+)+($|\\?>)/i"}, {"id": "80", "name": "file_exists curl_init file_get_contents file_put_contents include_once", "regx": "/<\\?[ph\\s]+(\\$[a-z_0-9]+)\\s*=\\s*.+?(curl_init|file_get_contents\\([\\s'\"]+http[s\\:\\/]+).+?file_put_contents\\(\\1.+?(include_once|(\\$[a-z_0-9]+)\\s*=\\s*new\\s+[a-z_0-9]+)\\(\\1.*?\\);\\s*(.*\\4[^;]*;\\s*|\\}\\s*|else\\s*|\\{\\s*|die[\\s\\('\"]+[^\\)]*\\)+;\\s*)*($|\\?>)/is"}, {"id": "81", "name": "long string var eval variable function", "regx": "/<(\\?|script language=)[ph\\s'\">]+(\\$[a-z_0-9]+(\\s*\\[[^\\]]+\\]+)*[\\s\\.]*=\\s*(\"([^\"]*(?<=\\\\)\")*[^\"]*\"|'([^']*(?<=\\\\)')*[^']*'|\\d+|(\\$[a-z_0-9]+[\\[\\]\\d\\.]*)+);\\s*|\\/\\/.*\\s*|\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*\\s*|\\@?error_reporting[\\(0\\)\\s]+;\\s*|for(each)?\\s*\\(([^\\{]+\\{[^\\}]+\\}+[\\}\\s]+|([^;]*;\\s*){3})|\\?>\\s*<\\?[ph\\s]+)+eval\\s*\\(((\\$[\\$\\{]*[a-z_0-9]+)[\\}\\s\\)]*(\\[[^\\]]+\\]+[\\.\\s\\}]*|=+\\s*(\\$[a-z_0-9]+|create_function))*[\\)\\s\\{;\\}]*)+(\\(.*?\\)[\\)\\s\\};]+)+(([\\$\\{]*(\\15|\\13|\\4)[\\s\\}]*\\(|echo)[^;]+;[\\}\\s]*|\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*(\\?>(.+$)?|$|<\\/script>)/i"}, {"id": "82", "name": "php var explode numbers Variable function", "regx": "/<\\?[ph\\s]+(if\\s*\\(([^\\{]+[\\{\\s]*\\$GLOBALS[\\{\\[]['\"]\\\\x[^;]+;[\\}\\s]*)+(\\?>\\s*<\\?[ph\\s]+))?\\$[a-z_0-9]+\\s*=\\s*('.+?'|\".+?\");\\s*.*?\\s*\\$[a-z_0-9]+\\s*=\\s*explode\\([^,]+[,\"'\\.0-9\\s]+\\);\\s*.*?\\$[a-z_0-9]+\\s*\\(.*?\\?>(.+$)?/i"}, {"id": "83", "name": "function X if function_exists curl_init spamcheckr.com curl_exec curl_close echo add_action X", "regx": "/(?:\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\/\\/|\\#)[^\\r\\n]*+\\s++|\\?>\\s*+<\\?(?:php)?\\s*+)*+if[\\s\\(\\!]+function_exists\\(\\s*['\"](\\w++|(?<X>(?:\\\\(?:x[a-f\\d]{2}|\\d++))+))['\"][\\s\\)]++\\{\\s*+(?:(?:function\\s+(\\w++)\\()?.+?(?:spamcheckr\\.com|javaterm1\\.pw|['\"]jquery\\.|karanbit\\.com|function_exists[\\(\\s]++['\"](?&X)['\"][\\s\\)]+\\{).+?curl_init.+?curl_exec.+?curl_close|function\\s+\\1\\s*\\([^\\)]*[\\)\\s]+\\{\\s*(?:\\$\\w++\\s*+=[^;]++[;\\s]+)*(?:if[\\s\\(]++\\@?(?:\\$|fopen[\\s\\(]++)[^\\{]++[\\{\\s]*+)?echo\\(wp_remote_retrieve_body\\(wp_remote_get\\()(?:.*?(\\$\\w++)\\s*+=\\s*+\\3[\\(\\s'\"]++(?&X)['\"])?[^;]++(?:;[\\}\\s]*+echo[^;]++)*+;[\\}\\s]*+(?:(?:(?:if[\\s\\(]++|else)[^\\{]*+[\\s\\{]++(?=[^\\}]*+(\\})))?(?:add_action[^\\,]++\\,\\s*+['\"](?:\\1|\\3|wordpress_api_debug)|eval[\\s\\(]++\\4\\[['\"](?&X))['\"][^;]*+[\\s\\);]++)\\5?\\s*+\\}\\s*+(?:\\?>\\s*+<\\?(?:php)?)?(?:\\s*\\/\\*[^\\n]*?\\*\\/|\\s*\\#[^\\n]*\\n)?/is"}, {"id": "84", "name": "if function_exists function error_reporting Variable xFF H* if file_exists error_reporting endif", "regx": "/if[ \\(\\!]+function_exists\\([ '\"]+(.+?)[ '\"]+[\\) \\t]+\\:.+?function \\1\\(\\) \\{.+?error_reporting\\(0.+?(\\$([a-z0-9\\_]+)[ =\\t]+\"(\\\\x[0-f]{2})+\";[\\t \\r\\n]+(\\$([a-z0-9\\_]+)[ =\\t]+\\$([a-z0-9\\_]+)\\(\"H\\*\"\\,.+?;[\\t \\r\\n]+)+)+if[ \\(\\!]+file_exists.+?error_reporting\\(\\$.+?endif;/is"}, {"id": "85", "name": "include ImageFile", "regx": "/(?<!\\/\\/\\s{8})\\@?(?:include|require)(?:_once)?[\\(\\s]++[a-z_0-9,\\.'\\s\"\\/\\-\\(\\)]+?(?<!GD_SYSTEM_PLUGIN_DIR \\. '\\/images\\/404)(?<!get_template_directory\\(\\) . '\\/changes)(\\.(?:gif|jpg|png|txt|cbu|[\\s\"']+\\/wp-includes\\/init\\.php|[\\s\"']+\\/wp-includes\\/js\\/utilities\\.js|[\\s\"']+\\/wp-admin\\/includes\\/class-wp-iternal-upgrade\\.php)|wp-java\\.php|notification\\.inc\\.php)[\"'\\s\\)]+;/i"}, {"id": "86", "name": "/function array Variable Function if eval", "regx": "/(\\/\\*[^\\*]*(\\*[^\\*\\/]*)+\\/\\s*)*((\\$[_\\->\\.a-z0-9]+)\\s*=[^;]+;\\s*([a-z_0-9]+)\\([^\\)]*\\4[^\\)]*[\\)\\s;]+\\s*function\\s*\\5)\\s*\\([^\\{]+\\{\\s*(\\$[_\\->\\.a-z0-9]+)\\s*(\\[[^\\]]+\\]+\\s*)*=\\s*array(_map)?\\s*\\(.+?\\);\\s*(\\$[a-z_0-9]+(\\s*\\[[^\\]]*\\]+)*[\\.\\s]*\\(.+?if[\\s\\(]*\\$[_\\.a-z0-9\\['\"\\]\\s]+\\)\\s*\\{)+\\s*(return\\s*)?eval\\s*\\([^\\)]+[\\)\\s;\\}]+/is"}, {"id": "87", "name": "Tagged error_reporting HTTP_USER_AGENT curl", "regx": "/(error_reporting\\s*\\([^;]+;\\s*|\\$[a-z_0-9]+\\s*=\\s*(array\\s*\\([^\\)]+\\)+[;\\)\\s]*|[\"'.\\s]*mb_strtolower\\(\\$_SERVER[\\{\\['\"]+)|if\\s*\\([^\\)]+)+HTTP_USER_AGENT(.+?http:\\/\\/|.+?curl_init){2,}.+?(\\$[a-z_0-9]+)?[\\s=]*curl_exec.+?(print|die|echo)['\"\\s\\(]+\\4['\"\\s\\);\\}]+\\s*/is"}, {"id": "88", "name": "header Location http space.php", "regx": "/header\\(['\"]Location: http:\\/\\/[^\\/]+\\/space\\.php\\?[^\\)]+\\);/i"}, {"id": "89", "name": "Copyright function getCookie document.write iframe", "regx": "/(\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/)\\s*+function\\s++(\\w++)\\(.+?navigator\\.userAgent.+?<iframe .+?\\2\\(.+?\\1/is"}, {"id": "90", "name": "Copyright function setCookie function", "regx": "/\\/\\*\\s*Copyright.+\\s*\\*\\/\\s*(function [sg]etCookie\\(.+?return[^;]*[;\\}\\s]+)*function ([a-z0-9\\_]+)\\(\\)[\\{\\s]+([a-z0-9\\s\\=]+navigator\\.userAgent.+?|function\\s+)[sg]etCookie\\(.+?\\2\\([^;]+[;\\}\\s]+/is"}, {"id": "91", "name": "php hex-encoded-lines", "regx": "/<\\?[ph\\s]++(?:[^;]*?\\\\x[0-9a-f]{2}[^;]*+[;\\s]++){9,}.*+\\s*+($|\\?>)/i"}, {"id": "92", "name": "php array http mt_rand meta refresh", "regx": "/<\\?[ph\\s]+(\\$[a-z_0-9]+[\\s=]+array[\\(\\s]+(['\"]http.+[,\\);\\s]+)+\\$[a-z_0-9]+[\\s=]+mt_rand\\(.+[\\);\\s]+(\\$[a-z_0-9]+[\\s=]+.+\\s*)+|if[\\(\\s]+[^\\{]+\\{\\s*header\\([^\\}]+\\}\\s*)\\?>\\s*(<head>\\s*)?<meta.*\\s*($|\\?>)/i"}, {"id": "93", "name": "php array function return base64_decode eval", "regx": "/<\\?[ph\\s]+\\$[_\\-\\>\\.a-z0-9\\{\\['\"\\]\\}]+\\s*=\\s*array\\(.+?function\\s+([a-z_0-9]+)\\((.+?;\\s*)+((\\$[a-z_0-9]+\\s*=\\s*)?(eval|\\$[a-z_0-9]+(\\s*\\[[^\\]]+\\])*)\\s*\\(\\s*(\\$_(REQUES|GE|POS)T\\[)?[^\\)]+[\\);\\s]+(exit|die)[^;]*;\\s*)+($|\\?>)/i"}, {"id": "94", "name": "php if isset GLOBALS strtolower SERVER if strstr GLOBALS", "regx": "/if[\\s\\(\\!]+isset[\\s\\(]+\\$GLOBALS\\[\"\\\\x[^\\]]+[\\]\\)\\s]+\\{\\s*(\\$[a-z_0-9]+)[\\s=]+strtolower[\\(\\s]+\\$_SERVER\\[\"\\\\x[^\\]]+[\\]\\);\\s]+((if|and)[\\(\\s\\!]+strstr[\\(\\s]+\\1[\\s,\"']+\\\\x[^\\)]+[\\)\\s\\{]+)+\\$GLOBALS\\[\"\\\\x[^\\]]+\\][^;]*;\\s*\\}/i"}, {"id": "95", "name": "function return function Variable function", "regx": "/(function[a-z_0-9\\s]+\\([^\\)]*[\\)\\s]+\\{\\s*return[a-z_0-9\\s]+\\([^\\)]*[\\)\\s]+;*\\}+\\s*)+(\\$[a-z_0-9]+[=\\s]+([a-z_0-9\\s]+\\([^\\)]*[\\)\\s]+|['\"][^;]*);+\\s*)+(\\$[a-z_0-9]+)[=\\s]+[^;]+['\"\\);]+\\s*\\4\\s*\\(+.*?\\);/i"}, {"id": "96", "name": "eval chr REPEATED", "regx": "/(<\\?[ph]*|\\/\\*[^\\*]*\\*\\/)\\s*((\\$[a-z_0-9]+)\\s*=\\s*')?(.+?\\.\\s*chr\\([0-9]+\\)\\s*\\.){20}.+\\s*(\\3\\s*=\\s*str_replace\\('\\#[',\\s]+\\3\\);\\s*)?((\\$[a-z_0-9]+)\\s*=\\s*create_function\\(['\\s,]*\\3\\);\\s*\\7\\(\\);)?(\\?>\\s*|$|\\1)/i"}, {"id": "97", "name": "garbage around eval VeriableFunction", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*(\\$[a-z_0-9]+[\\s\\.]*=([\\s\\.\\(]*(([\"']).*?(?<!_e)\\6|[\\$\\{a-z_0-9]+[\\}\\s]*(\\[[^\\]]+\\]+)*;|\\([^\\)]*\\);+|\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*(?=\\/))+[\\/\\.;\\^\\&\\|\\~\\)]*(?!\\*)\\s*|\\#.+\\n)+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*)+((\\@\\$[a-z_0-9]+|eval|if)\\s*\\(|\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/)*\\$[\\$\\{]*[a-z_0-9]+[\\}\\s]*(\\[[^\\]]+\\][\\s]*)*\\(.*?(.+'[\\.;\\)]+)?(\\s*'.+'[\\.;\\)]+)*\\s*(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*(\\?>|$)/i"}, {"id": "98", "name": "if defined define function global eval Variable Function", "regx": "/if[\\(\\s\\!]+defined\\([^\\)]+[\\)\\s\\{]+define\\([^\\)]+[\\)\\s]+;\\s*(\\$[a-z_0-9]+[\\s\\.]*=[^;]*;\\s*)*function\\s+([^\\(]*)\\([^\\)]*[\\)\\s]+\\{\\s*(global (\\$[^;]+);\\s*|(\\$[a-z_0-9]+[\\s\\.]*=[^;]*;\\s*)*for(each)?\\s*\\()(.+[\\r\\n]+)+?eval(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*\\((\\2|\\4)\\([^\\)]*[\\)\\s]+;(\\s*return[^;]*;\\s*\\})?\\s*\\}\\s*(\\/\\/.*\\s*)*/i"}, {"id": "99", "name": "iframe small height|width", "regx": "/(<script[^>]+src=['\"]?http\\:\\/\\/([a-z\\.\\-0-9]+)[^>]*><\\/script>)?<iframe.*?(\\s*(height|width|src)=['\"]?([0-5]['\"\\s]|http\\:\\/\\/[^>]+?)){3}[^>]*><\\/iframe>/i"}, {"id": "100", "name": "php global array function_exists return for unset", "regx": "/global (\\$[a-z0-9\\_]+);\\s*\\1[\\s=]+array\\(.+?function_exists\\([^\\)]+[\\)\\s\\&]+\\!function_exists\\(['\"]([a-z0-9\\_]+)['\"][\\)\\s\\{]+function\\s+\\2\\([^\\)]+[\\)\\s\\{]+global \\1;.+?return[^;]*[;\\s\\}]+for\\s*\\([^\\)]*[\\);\\}]+(\\{\\s*[\\$a-z0-9\\_]+\\([^\\)]+[\\);\\}]+)?unset\\(\\1\\);/i"}, {"id": "101", "name": "eval pack Hex", "regx": "/<\\?(?:php)?\\s*+(?:\\$[\\w\\['\"\\]\\.\\s]++=[^;]++;\\s*+|(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)(?:[^\\n\\?]++|\\?(?!\\>))*+(?:\\s|\\?>)++)++|\\@?error_reporting[\\(0\\)\\s]++;\\s*+)*+(?:if[\\s\\(\\!]++function_exists[^\\{]++\\{\\s*+)?(?:function\\s++(\\w++)\\([^\\)]*+[\\)\\s]++\\{[^\\}]+?(\\$\\w++)[\\.\\s]*+=(?:[\\^\\s]*+(?:(?:chr|ord)\\s*+\\(|pack\\(['\"](?:H\\*['\",\\s\\.\\dA-F]+|C['\"\\s,]+hexdec\\())[^\\)]*+[\\)\\s]++)++[;\\s\\}]++return\\s++\\3[;\\s]++\\}[;\\s]++)?(?:\\$\\w++\\s*+=\\s*+(?!eval)\\w++[^;]*+;\\s*+|if[\\s\\(\\!]++[^\\{]++\\{\\s*+)*+eval\\s*+\\((?:\\2\\s*+\\(|\\2\\s*+\\(|pack\\s*+\\(['\"]H\\*['\",\\s\\.\\dA-F]++)[^;]*+['\"\\);\\s\\}]++(?&C)*+(?:$|\\?>)/is"}, {"id": "102", "name": "php HTTP_USER_AGENT if header Location http .ru", "regx": "/<\\?[ph\\s]*(\\$[a-z_0-9]+)\\s*=\\s*(array\\([^\\)]+\\.ru['\"]\\)+;\\s*(\\$[a-z_0-9]+)\\s*=\\s*\\1\\[.+;\\s*(\\$[a-z_0-9]+)\\s*=[\\s\\(]*preg_match\\s*|\\$_SERVER\\[[\"']HTTP_USER_AGENT['\"]\\];\\s*if[\\s\\(]+[\\$a-z_0-9]+)\\(.*(\\3|\\1).*[\\)\\s\\{]+header\\(['\"]Location:\\s*(http:\\/\\/.+\\.ru\\/.*|['\"\\.\\s]+|\\1|\\3|\\4)+\\);[\\sdie\\(\\);\\}]*(\\?>\\s*|$)/i"}, {"id": "103", "name": "require_once wp-update.php REMOTE_ADDR HTTP_USER_AGENT require_once wp-class.php die", "regx": "/<\\?.+?require_once[\\s\\(\"']+wp-update\\.php[\"'\\);\\s]+\\$ip = \\$_SERVER\\[[\"']REMOTE_ADDR[\"']\\];.+?\\$_SERVER\\[[\"']HTTP_USER_AGENT[\"']\\].+?require_once[\\s\\(\"']+wp-class\\.php[\"'\\);\\s]+die\\(.+?($|\\?>)/is"}, {"id": "104", "name": "eval decodeURIComponent Encoded-text", "regx": "/(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|error_reporting\\([0\\);\\s]+|\\$[a-z_0-9\\[\\]\\{\\}'\"]+\\s*=[^;]+;\\s*|\\/\\/[^\\n]*\\n\\s*)*eval(\\s*\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/)*([\\s\\(]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*)+(gzuncompress|rawurldecode|decodeURIComponent\\((['\"]).*\\9)(\\s*\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/)*\\s*(\\(|\\))[^;]*;\\s*(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/.+\\s*)*/i"}, {"id": "105", "name": "functions BDN SVB SCk GCk if cookieEnabled GCk else SCk if loaded SVB addEventListener", "regx": "/function BDN\\(.+?function SVB\\(.+?function SCk\\(.+?function GCk\\(.+?return unescape\\(document\\.cookie\\.substring\\([^\\)]+[\\);\\s\\}]+if[\\s\\(]+navigator\\.cookieEnabled[\\)\\s\\{]+if[\\s\\(\\!]+GCk\\([^\\{]+\\{[\\}else\\{\\s]*SCk\\([^\\)]+[\\);\\s]+if[\\s\\(]+document\\.loaded[\\)\\{\\s]+SVB\\([^\\)]*[\\);\\s\\}]+else[\\{\\s]+if[\\s\\(]+window\\.addEventListener[\\)\\{\\s]+window\\.addEventListener\\([^\\)]+[\\);\\s\\}]+else[\\{\\s]+window\\.attachEvent\\([^\\)]+[\\);\\s\\}]+/is"}, {"id": "106", "name": "div style opacity0", "regx": "/<(div|font|a)\\s+(?=[^\\>]*style=['\"][^\\>]*(height|width|opacity|font-size)\\s*.\\s*0([^\\.]|\\.[0-1]))[^>]*((>[a-z\\&\\#\\!0-9;\\s]*<a\\s+)?(?=[^\\>]*href=['\"]http(?!s\\:\\/\\/premio\\.io\\/))[^<]*<\\/(a|\\1)?)+(>[a-z\\&\\#\\!0-9;\\s]*<\\/)?\\1>/is"}, {"id": "107", "name": "php error_reporting Long mail print_r SERVER", "regx": "/<\\?[ph\\s]*(error_reporting\\(.{9999,}|\\/\\/[^\\n]*\\n\\s*|\\$[a-z_0-9]+[\\s\\.]*=[^;]+;\\s*)+(if\\s*\\([^\\)]+[\\)\\s\\{]+die[\\s\\(]+[^;]+;[\\s\\}]*)*(if\\s*\\([^\\)]+[\\)\\s\\{]+)*(foreach|while)\\s*\\([^\\)]+[\\)\\s\\{]+(mail\\s*\\(.+\\s*)+([\\s\\}]*header[\\s\\('\"]+Location.+|\\s*print_r\\(\\$_SERVER.+|\\s*echo.+\\s*\\$[a-z_0-9\\+;]+)+[\\}\\s]*(\\?>((\\s*[\\[<]html[\\]>]){2}[^$]+<\\/html>)?|$)/i"}, {"id": "108", "name": "if !function_exists function curl return function include functions", "regx": "/if\\s*\\(\\s*\\!function_exists\\([\"']([a-z_0-9]+)['\"]\\)[\\)\\s\\{]*function\\s*\\1\\([^\\)]+\\)[\\)\\s\\{]+([^\\n]*curl_[^\\n]+\\s+)+return[^\\n]+[\\s+\\}]+function\\s*[a-z_0-9]+\\([^\\)]*\\)[\\)\\s\\{]+((\\$[a-z_0-9]+)\\s*=\\s*([a-z_0-9]+)\\([^\\n]+\\s+)+include[\\(\\s]+\\4.+?function\\s+\\5.+?($|(?=function )|(?=\\?\\>))/is"}, {"id": "109", "name": "if !current_user_can add_filter function a href http return", "regx": "/if\\s*\\(\\s*\\!current_user_can\\([^\\)]+[\\)\\s\\{]*add_filter[\\s*\\(]+[^,]+,\\s*[\"']([a-z_0-9]+)['\"]\\)[\\);\\s\\}]+function\\s*\\1\\(.+?add_filter[\\s*\\(]+[^,]+,\\s*[\"']([a-z_0-9]+)['\"]\\)[\\);\\s\\}]+.+return[^;]*[;\\s\\}]+function\\s*\\2\\(.+?<a href=['\"]http:\\/\\/.+return[^;]*[;\\s\\}]+/i"}, {"id": "110", "name": "php Array function return base64_decode eval Function Array", "regx": "/<\\?(?:php)?(?<C>(?:\\#|\\/\\/)[^\\r\\n]*+[\\r\\n]++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/|\\s++)*+(?:\\$\\w++(?&C)*+=\\s*+(?!array)[^;]++;++(?&C)*+|define[^;]++;++(?&C)*+|(\\$[\\$\\{\"']*+\\w++['\"\\}]*+)(?<A>(?&C)*+\\[[^\\]]++\\]++)*+(?&C)*+=(?&C)*+array(?&C)*+\\(.*?['\"][\\w\\/\\+]*+)*+(?:(?:['\"](?&C)*+,(?&C)*+['\"][\\w\\/\\+]++)++=*+['\"](?&C)*+(?:\\)(?&C)*+)++;(?&C)*+(\\$\\w++)(?&A)*+(?&C)*+=(?&C)*+create_function(?&C)*+\\((?:(?&C)*+['\"](?:\\\\x[\\dA-F]{2}|[\\*\\/]{2})++['\"](?&C)*+[,\\)\\.])++(?&C)*+;(?&C)*+|.*?function(?&C)++(\\w{11})\\w*+(?&C)*+\\(.+?return\\s++base64_decode.+?)(eval|\\$\\w++)(?&A)*+(?&C)*+(?:=(?&C)*+array_walk(?&C)*+\\((?&C)*+\\2(?&C)*+,|\\()(?&C)*+(?:(?:\\2|\\4|\\5|\\6)[^;]*+[;\\s\\}\\?><ph]++|.+(?:else[\\s\\{]+|add_action\\([^,]++,[\\s'\"]++)\\5[^;]++;(?:(?&C)*+\\})*+)++(?&C)*+(?:$|\\?>\\s*+)/is"}, {"id": "111", "name": "php Var Array Concat Variable Function", "regx": "/<\\?(?:php)?\\s*+(?:(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++)|(?:if[\\s\\(]++isset\\(|function\\s++\\w++\\([^\\{]++[\\{\\s]++.+?return)[^;]++;[\\s\\}]++)*((?:(?:(?&C)|if\\s*+)++\\(isset[\\s\\(]++[\\$\\{'\"_]++[^\\{]++[\\{\\s]*+)?(?:(?&C)|\\$\\w++\\s*+(?<A>\\[[^\\]]*+\\]++\\s*+)*+)++=\\s*+(?!require)[^;]*+;\\s*+|for(?:each)?\\s*+\\([^\\)]++[\\)\\s]++\\{[^\\}]++[\\}\\s]*+)+((?:function\\s++\\w++\\([^\\{]++[\\{\\s]++.*?|\\$\\w++\\s*+(?&A)*+=\\s*+)?\\$[\\$\\{]*+\\w++[\\}\\s]*+(?:(?&C)|(?&A))*+\\(.*;[\\}\\s]*+)++(?:$|\\?>)/i"}, {"id": "112", "name": "php array implode function Var Hex return VariableFunction eval", "regx": "/<\\?[ph\\s]+((\\$[a-z_0-9]+)\\s*=[^;]+[;\\s]+|function ([a-z_0-9]+)\\([^\\)]+[\\)\\{\\s]+.*\\s*((\\$[a-z_0-9]+)\\s*=[\\s'\"]+(\\\\(x[0-9a-f]{2}|[0-9]+))+['\"];\\s*)+)+.*\\s*eval\\s*\\((\\s*(\\3|\\2|\\5)\\s*\\(){2,}[^;]+[;\\s]+.+?($|\\?>)/i"}, {"id": "113", "name": "php array if SERVER if isbot file_get_contents header http", "regx": "/<\\?[ph\\s]+((error_reporting|ini_set)\\s*\\([^;]*;\\s*)*(\\$[a-z\\_0-9]+\\s*=[^;]+[;\\s]+)+(if[^;]+\\$isbot[^;]+[;\\}\\s]+)+(.+?(\\$[a-z_0-9]+)\\s*=\\s*(file_get_contents|curl_exec)\\s*\\([^\\}]+[\\}\\s]+)+((\\$[a-z\\_0-9]+)\\s*=[^;]*\\6[^;]*[;\\s]+)*.+((\\6|\\9)[^;]+[;\\}\\s]+((header|echo|print)[^;]+[;\\}\\s]+)+)+($|\\?>)/is"}, {"id": "114", "name": "php REQUEST array REQUEST array_filter exit", "regx": "/<\\?[ph\\s]+(\\$[a-z\\_0-9]+)\\s*=\\s*\\$_(REQUES|GE|POS)T\\[[^;]+[;\\s]+(\\$[a-z\\_0-9]+)\\s*=\\s*array\\(\\$_(REQUES|GE|POS)T\\[[^;]+[;\\s]+\\$[a-z\\_0-9]+\\s*=\\s*array_filter\\(\\3[,\\s]*\\1\\)[dexit\\(\\);\\s]*($|\\?>)/i"}, {"id": "115", "name": "php base64_decode create_function VariableFinction", "regx": "/<\\?.+?(\\$[a-z_0-9]+)\\s*=\\s*base64_decode\\(.+?((\\$[a-z_0-9]+)\\s*=\\s*(\\@?(gzinflate|strrev)\\()+\\1.+?)?(\\$[a-z_0-9]+)\\s*=\\s*create_function\\([^,]+[,\\s]+(\\1|\\3)[^;]+[;\\s]+\\6\\([^;]+[;\\s\\}]+(else[\\{\\s]+[^\\}]+[;\\s\\}]+|echo[\\s\\(]*(['\"]).+?\\9[;\\s\\}]+)*($|\\?>)/is"}, {"id": "116", "name": "php function wp_enqueue_script json2 add_action", "regx": "/(?:\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++)*+(?:if[\\s\\(\\!]++function_exists\\(\\s*+['\"](\\w++)['\"][\\s\\)]++\\{\\s*+)?function\\s+(\\w++)\\s*\\([^\\{]++\\{\\s*+(?:(?:echo|print|\\?>)[\\('\"\\s]*+(?:<script[^>]+?src=\\\\?['\"](?:data\\:|http[s]?\\:\\/\\/)[^>]++>\\s*+<\\/script>\\s*+)++(?:['\"][\\);\\s]*+|<\\?(?:php)?)[\\}\\s]*+|(?:if[\\s\\(\\!]++isset[\\(\\s]++[^\\)]++[\\s\\)]++[\\{\\s]*+)?(?:\\$\\w++\\s*+=[^;]++;\\s*+)*(?:\\$[a-z_0-9]++\\s*+=\\s*+)?(?:eval|\\$\\w++)\\s*+\\(.*?base64_decode\\(.+?|(?:(\\$\\w++)\\s*+=[\"\\s'\\(]++s[\"\\s'\\.]*+c[\"\\s'\\.]*+r[\"\\s'\\.]*+i[\"\\s'\\.]*+p[\"\\s'\\.]*+t[\"\\s'\\.\\)]++;\\s*+)?(?:echo|print)[\\s\\('\"]++<[\"\\.'\\s]*+(?:script|\\3).+?(?:chr|fromCharCode)\\(.+?(?:chr|document\\.write)\\(.+?)add_action\\s*+\\([^,]++['\"\\,\\s]++(?:\\1|\\2)['\"\\);\\s]++/is"}, {"id": "117", "name": "php if function_exists function return Variable Function eval", "regx": "/<\\?(?:php)?(?:(?<C>[\\s\\.\\)]++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/)*+(?:(?<F>function(?&C)++\\w++(?&C)*+\\((?&C)*+(\\$\\w++)?[^\\{]*+\\{(?&C)*+(?:(\\$\\w++)(?&C)*+=(?:(?&C)*+(?:\"[^\"]*+\"|'[^']*+'|\\w++(?<!pack)(?&C)*+\\([^\\)\\^]*+[^;\\^]++))++(?<E>[;\\}]++(?&C)*+)++)*+(?:\\8(?&C)*+\\([^\\)]*+(?&C)++(?&E)++|for[^\\{]++\\{[^\\}]++(?&E)++|(?:(\\$\\w++)(?&C)*+=|return)(?&C)*+(?:(?:(?<N>\\w++(?<!pack)(?&C)*+\\((?&C)*+)*+\\$\\w++(?&C)*+\\^(?&C)*+(?&N)*+\\$\\w++[^;]*+|\\@?pack(?&C)*+\\([^\\)]*+[^;]++|(?&N)*+\\3[^;]*+|\\4|\\6[^;]*+)(?&C)*+(?&E)++)++)++)|(?:if[\\s\\(]++\\!function_exists\\([^\\{]++[\\{\\s]++)?function(?&C)*+(\\w++)[^\\{]++\\{(?&C)*+|if(?&C)*+\\([^\\{]++\\{(?&C)*+|(\\$\\w++)(?&C)*+=(?&C)*+(?:\\w++(?&C)*+\\((?&C)*+)?\\$\\w++(?&C)*+\\([^\\)]*+[^;]++(?&E)++|for[^\\{]++\\{[^\\}]++(?&E)++|(?!\\$license = \\$this->license\\(\\))(?<V>\\$\\w++(?:\\s*+\\[[^\\]]++\\]++)*+(?&C)*+=[^;]++(?&E)++)++)*+(?:(?:return(?&C)++\\$\\w++|eval)(?&C)*+\\(|return(?&C)*+\\9[;\\}\\s]++(?:(?&F)++(?&V)*+)++eval(?&C)*+\\([^\\)]*+)[^;]*+(?&E)++(?&V)*+(?:(?:die(?&C)*+\\([^\\)]*+[^;]++(?&E)++|(?&F)++|\\/\\/.+\\s*+)*+(?&V)*+)++)++(?:$|\\?>)/i"}, {"id": "118", "name": "Leftover Header if GLOBALS Hex SERVER Hex/i", "regx": "/<\\?[ph\\s]+if\\s*\\([^\\{]+\\$GLOBALS[\\{\\[]['\"]\\\\x[^\\{]+[\\{\\s]+\\$[a-z_0-9]+(\\[[^\\]]+[\\]\\s\\}]+)*=[^;]+\\$_SERVER[\\{\\[]['\"]\\\\x[^;]+[;\\s]+if\\s*\\([^\\{]+\\$GLOBALS[\\{\\[]['\"]\\\\x[^;]+;[\\s\\}]*($|\\?>)/i"}, {"id": "119", "name": "if HTTP_USER_AGENT add_action wp_footer function echo", "regx": "/(?:(?:(?:\\5\\6\\7)?(?=((?:((?:\\$[a-z_0-9]+(?:\\s*\\[[^\\]]+[\\]]+)*\\s*=\\s*get_option\\([^;]++[;\\s\\}]++|if\\s*\\([^\\{]+HTTP_USER_AGENT[^\\{]+[\\{\\s]+)*add_action\\(['\"\\s]+wp_footer['\"\\s,]+)([a-z_0-9]+)(['\"\\s]+[^;]+[;\\s\\}]+))+))|(?:\\1\\s*)*+(?=(function\\s+)([a-z_0-9]+)([^\\{]+[\\{\\s]+if\\s*\\([^\\{]+HTTP_USER_AGENT[^\\{]+[\\{\\s]+(?:(\\$[a-z_0-9]+)(?:\\s*\\[[^\\]]+[\\]]+)*\\s*=(?:[\\.\\s]*(\"[^\"]++\"|'[^']++'|[0-9\\*\\+\\-\\/\\(\\)]++))++[;\\s\\}]++)+echo[\\s\\('\"]+(\\$[a-z_0-9]+)+[^\\}]++[\\}\\s]++))){2}|(?:(?:\\15\\16\\17)?(?=((?:((?:\\$[a-z_0-9]+(?:\\s*\\[[^\\]]+[\\]]+)*\\s*=\\s*get_option\\([^;]++[;\\s\\}]++)*if\\s*\\([^\\{]+HTTP_USER_AGENT[^\\{]+[\\{\\s]+add_action\\(['\"\\s]+wp_footer['\"\\s,]+)([a-z_0-9]+)(['\"\\s]+[^;]+[;\\s\\}]+))+))|(?:\\11\\s*)*+(?=(function\\s+)([a-z_0-9]+)([^\\{]+[\\{\\s]+(?:(\\$[a-z_0-9]+)(?:\\s*\\[[^\\]]+[\\]]+)*\\s*=(?:[\\.\\s]*(\"[^\"]++\"|'[^']++'|[0-9\\*\\+\\-\\/\\(\\)]++))++[;\\s\\}]++|if\\s*\\([^\\{]+HTTP_USER_AGENT[^\\{]+[\\{\\s]+)+echo[\\s\\('\"]+(\\$[a-z_0-9]+)+[^\\}]++[\\}\\s]++))){2})(\\5\\3\\7|\\2\\6\\4|\\15\\13\\17|\\12\\16\\14)/i"}, {"id": "120", "name": "div display none href http buy", "regx": "/<(?:(div)|(a))\\s+(?=[^>]+?display[\\s\\:]+none)[^>]*?(?:(?:(?!\\2)[a]?>\\s*<(a)\\s+[^>]*?)?href[='\"]+http[^>]++>[^<]*?(escort|buy)[^<]*+<\\/(?=a))+(\\3>\\s*<\\/\\1|\\2)>\\s*/i"}, {"id": "121", "name": "Copyright function setCookie return function userAgent setCookie write iframe top Neg", "regx": "/(<(script)[^>]*>\\s*)(\\/\\*\\s*Copyright.+\\s*\\*\\/\\s*|(\\$|var\\s+)?[a-z_0-9\\s]+=[^;]+;\\s*|setTimeout\\([^;]+;\\s*)*(function [sg]etCookie\\(.+?((document\\.cookie=|return)[^\\}]+[;\\}\\s]+)+)+.+?[sg]etCookie\\(.+?document\\.write\\(([\"'])<(script|iframe)[^>]+(src=['\"\\+\\shtps\\:]+\\/\\/|top:\\s*\\-)((.+?document\\.cookie|.+?\\.toUTCString){2}[\\(\\)\\}]+|.+?encodeURIComponent\\(document\\.referrer\\))?.+?\\8\\/\\9>\\8[^;]+[\\);\\}\\s]+(<\\/\\2>|$)/is"}, {"id": "122", "name": "php Lots of Hex", "regx": "/<\\?(?:php)?+\\s*+(?<C>(?:\\#|\\/\\/)[^\\r\\n]*+\\s++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+)*+(?!return[^;]*+;\\s*+(?:$|\\?>))(?:(?:[^\\\\]*+\\\\[^x\\d])*+[^\\\\]*+\\\\[x\\d][\\dA-F]){257,}[^\\r\\n]*+\\s*+(?:(?:(?:(?!\\$\\w++\\s*+\\()[^;]*+;++\\s*+)*+\\$\\w++\\s*+\\([^\\)]*+[^;]++;++\\s*+)++(?:(?&C)|\\$\\w++\\s*+=.++\\s*+)*+)?+(?:$|\\?>\\s*+)/i"}, {"id": "123", "name": "script document write div anchor script document write ENDdiv", "regx": "/<script[^>]*>\\s*document\\.write\\([\"']([\\<div][\"' \\+]*){4}[^>]*>[\"']\\);\\s*<\\/script>\\s*(<a .+?<\\/a>\\s*)+<script[^>]*>\\s*document\\.write\\([\"']([\\<\\/div][\"' \\+]*){5}>[\"']\\);\\s*<\\/script>/i"}, {"id": "124", "name": "echo div position absolute negative anchors", "regx": "/(?<!['\"])<(html>)?[\\s<]*(((script)[^>]*>\\s*document\\.write[\\(\\s'\"]+<)?(?<!\\\\n\\s{4}<)div|(style)[^>]*>\\s*[\\.\\#]([a-z_\\-0-9]+)[\\s\\{]+)([^\\}\\>]*(?:(?:height|width)\\:\\s*0p|(?:left|position|top|opacity|filter|display|text-indent)\\:\\s*(?:alpha\\(opacity=0|0?\\.|\\-[0-9\\.]{3,}|none|absolute))){3,}[^>]*>['\"\\);\\s]*(?:<\\/\\5>)?.*?<(?:(?:([b-s][a-z0-9]*)[^>]*>[.\\s]*<)*a .+?<\\/a(?:>[^<]*<(?:\\/(?:[^d][a-z0-9]*|\\8))?)*)+>(?:[^<]*+<\\/(?:\\2|div)>)*/i"}, {"id": "125", "name": "PHP Garbage Around eval Variable Function", "regx": "/<\\?[ph\\s]+\\$[a-z\\_0-9]+\\s*=(\\s*'.+'[\\)\\.]+)+(\\s*'.+eval\\s*\\(\\$[\\$\\{]*[a-z\\_0-9]+[\\} \\t]*(\\[[^\\]]+\\][ \\t]*)*\\(.+'[\\)\\.]+)(\\s*'.+'[\\.;\\)]+)+\\s*(\\?>|$)/i"}, {"id": "126", "name": "stripslashes REQUEST if echo return fopen fwrite fclose echo function return", "regx": "/(\\\\xEF\\\\xBB\\\\xBF)?<\\?[ph\\s]+(\\$[a-z_0-9]+\\s*=\\s*(stripslashes[\\(\\s]+)?\\$_(REQUES|GE|POS)T\\[[\"'][^;]*[;\\s]+)+(if[\\(\\s]+\\$[^\\{]*[\\{\\s]+echo\\s*[\"'][^;]+[;\\s]+return[^;]*;[\\s\\}]+)+.+?fopen\\(.+?fwrite\\(.+?fclose\\([^;]*[;\\s]+echo\\s*[\"'][^;]+[;\\s]+(function.+?[rmk]{2}dir\\([^;]+;[\\s\\}]+(return[^;]*;[\\s\\}]+)*)+($|\\?>)/is"}, {"id": "127", "name": "Var Hex Variable Function", "regx": "/<\\?(?:php)?\\s*+(?:(?:(?:(?:(?:\\$\\w++(?:\\s*\\[[^\\]]+\\])*+[\\s\\.]*+=[\\s\\(]*+(?:[\\@\\{\\[\\$\\-\\w\\\\]|\"(?:[^\"]*+(?<=\\\\)\")*+[^\"]*+(?<!\\\\)\"|'(?:[^']*+(?<=\\\\)')*+[^']*+(?<!\\\\)')|echo[\\s\\('\"\\$]++|retur|die|for[\\s\\(][^;]++;[^;]++;[^\\)]++\\)[\\)\\s\\{]*+)[^;]*+['\\);\\}\\s\\\\]++|(?:function|if)[\\s\\(][^\\)]++[^\\{]++[\\{\\s]++)*(\\$(?!k_opad)[\\w\\{\"\\}\\\\]++).*?\\\\(?:x[a-f\\d]{2}|[0-9]{2,3})[^;]*+[;\\}\\s]++)+)+(?:if\\s*+\\([^\\{]++\\{\\s*+)?(?:(?:(\\$\\w++)\\s*+=\\s*+|return\\s*+|echo['\"\\s\\(]*+)?\\@?(?:\\$[\\w\\{\"\\}\\\\]++(?:\\s*+\\[[^\\]]++\\]++)*+|eval)\\s*+\\((?:[^;]++;(?=\\)|'))*+[^;]++;\\s*+)++[\\}\\s]*+)++(?:(?:if|for|\\1|\\2|\\/\\/)[^\\n\\?]++(?:(?!\\?>).[^\\n\\?]++)*+\\s*+|(?:echo|exit|die)[\\s\\(]*+(?:\"(?:[^\"]*+(?<=\\\\)\")*+[^\"]*+(?<!\\\\)\"|'(?:[^']*+(?<=\\\\)')*+[^']*+(?<!\\\\)')?[\\);\\s\\}]++|else[\\s\\{]++[^\\}]*+[\\};\\s]++)*+(?:$|\\?>\\s*+)/i"}, {"id": "128", "name": "ini_set if isset array base64_decode function return base64_decode eval Variable Function", "regx": "/<\\?[ph\\s]+([if\\s\\(\\!\\@]*(ini_set\\s*\\(|error_reporting\\s*\\(|set_time_limit\\s*\\(|isset\\s*\\(|define|global|\\$[\\{\\$a-z_0-9\\}\\[\\\\'\"\\]\\s]+=)[^;]+;[\\}\\s]*|foreach[\\s\\(]+[^\\{]*?\\$_COOKIE[^\\{]+\\{[^\\}]+[\\s\\}]+)+(function\\s*([a-z_0-9]+)\\s*\\(.+?(return[\\s\\(_]*(\\4|base64_decode)|\\$[a-z_0-9]+\\s*(\\[[^\\]]*\\]\\s*)*)\\(.+?)+(?<!\\/\\/,function\\(response\\) \\{ )eval\\(.+?\\)+;[\\s\\};]+(.*function[\\s\\(_]+\\6[^\\{]+[\\{\\s]+(\\$[a-z_0-9]+)\\s*=.+?return[^;]*\\9[^;]*[;\\s\\}]+)?(\\?>\\s*(\\#\\![\\/a-z_\\-0-9\\+=\\s]{200,}$)?|$)/is"}, {"id": "129", "name": "class const pack H* function include new Class", "regx": "/class\\s+([a-z_0-9]+)\\s*\\{((?=.+\\$qString[\\s=]+\\$this-\\>([a-z_0-9]+)\\()|.+?const\\s+([a-z_0-9]+)\\s*=.+?self\\:\\:([a-z_0-9]+)\\(pack\\('H\\*'[,\\s]+self\\:\\:\\4).+?function\\s+(\\3|\\5)\\((\\$[a-z_0-9]+).+?(\\$[a-z_0-9]+|\\@?(base64_decode|include|require)(_once)?)\\s*\\([^;]*\\7.+new\\s+\\1[^;]*;/is"}, {"id": "130", "name": "eval str_rot13", "regx": "/(\\/\\/.+)?\\s*((ini_set\\(|error_reporting\\(|\\$[a-z_0-9]+[\\s\\.]*=)[^;]*;\\s*)*\\@?(eval|assert|\\$[a-z\\_0-9]+(\\s*\\[[^\\]]+\\])*)\\s*\\(\\s*str_rot13\\('.*'\\)\\);(\\s*\\1)?/i"}, {"id": "131", "name": "php GLOBALS Hex function if eval", "regx": "/<\\?[ph\\s]*(\\$[a-z_0-9]+\\s*=[^;]+;\\s*)*(\\s*\\$(GLOBALS|[\\{\\$'\"]+[0-9_\\-a-z\\\\'\"\\.]+[\\}\\s]+)[\\s\\[\\{'\"]+([a-z_0-9]+)[\"'\\}\\]\\s]+([^;]*;\\s*global\\s*\\$\\4[^;]*;\\s*\\$\\4\\s*=\\s*\\$GLOBALS[^;]*;\\s*\\$(\\4|\\{[^\\}]*\\}+)\\s*(\\[[^\\]]+[\\]\\s]+|\\{[^\\}]+[\\}\\s]+)+)?=\\s*['\"](\\\\x[0-9a-f]{1,2})+[\"'];\\s*((echo|print)?[\\s\\@]*\\$(GLOBAL|\\4)[^;]+;\\s*|\\$[a-z_0-9]+\\s*([\\.=]+\\s*\\@?(NULL|(array[\\(\\s]+)?\\$(GLOBAL|\\4)[^;]*|\\$[a-z_0-9]+(\\[[^\\]]+[\\]\\s]+|\\{[^\\}]+[\\}\\s]+)*|[\"']+))+;\\s*|global\\s*\\$[^;]+;\\s*|function\\s+[0-9_a-z]+\\s*\\([^\\{]+|\\{\\s*|\\}\\s*|(for(each)?|(else\\s*)?if)\\s*\\(.+?\\)+\\s*|return[^;]*;[\\s\\}]+){30,})*eval(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*\\((?!\" \\?>\".\\$GLOBALS\\['Oxygen_VSB_Current_Comments_Class).+?\\)+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*;[\\s\\}]+(exit[^;]*;[\\s\\}]+|\\?>\\s*<\\?[ph\\s]+\\$[a-z_0-9]+\\s*=\\s*array\\([^\\)]*\\)+.+;\\s*)*($|\\?>)/i"}, {"id": "132", "name": "php if isset POST base64_decode mail", "regx": "/<\\?[ph\\s]*((\\$([0-9_a-z]+)\\s*=\\s*explode[^,]+,\\s*base64_decode\\([^;]+;\\s*|(error_reporting|ini_set|session_start)\\([^;]+;\\s*|((if|isset|foreach)[\\s\\(]+)+\\$_(REQUEST|GET|POST|SERVER).[^\\{]+\\{\\s*)?((\\$[a-z_0-9]+)[\\s\\.]*=[a-z_0-9\\s\\(]*[\\$\"f'](\\3|_REQUEST|_GET|_POST|_SERVER|REMOTE_ADDR|http[s]?\\:\\/\\/|ile_get_contents[\\s\\(]+)([^;]*[\\);\\}\\s]+else[\\{\\s]+|echo|print)*[^;]*[;\\s]+(\\s*exit;)?|\\/\\/.+\\n+|\\?>\\s*<\\?[ph\\s]*)*[\\}\\s]*)+((\\$[0-9_a-z]+)[\\s\\.]*=[^;]+;\\s*|fwrite\\([^;]+;\\s*)*((\\$[a-z_0-9]+\\s*=\\s*|if[\\s\\(]+|else[^\\{]*[\\{\\s]+)*(mail\\s*\\(|file_get_contents[\\s\\(]+['\"]http)[^;]+[\\);\\s\\}]*)+(((if[\\(\\s]+|else)[^\\{]*[\\{\\s]+)?((header|echo|exit)[^;]*[;\\s\\}]+)+)*($|\\?>)/i"}, {"id": "133", "name": "wp_enqueue_script SWEETCAPTCHA", "regx": "/wp_enqueue_script\\([^,]+,\\s*['\"]http.+?SWEETCAPTCHA[^;]+;/i"}, {"id": "134", "name": "php class Variable Functions new CLASS", "regx": "/<\\?[ph\\s]*+(?:\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++)*+(?:(?:ini_set|error_reporting|set_time_limit)\\([^\\)]*+\\)++;\\s*+|class\\s++.+?(\\$\\w++)\\s*+=[\\s\\@]*+file_get_contents\\(.+?return\\s++\\1[;\\}\\s]*+|class B\\s*+\\{.*?return 'UN'[^;]*+[;\\}\\s]++|function[^\\(]+dolly[^\\{]++\\{.*?(?:return|dbDelta)[^;]*+[;\\}\\s]*+|if[\\s\\(\\!]++class_exists[\\s\\('\"]++(\\w++)['\"\\)\\s\\{]++|(?=.*return myfuncgood\\()|(?=.*?(\\$\\w++)\\s*+=\\s*+\\$\\w++\\s*+\\([^;]++;\\s*+\\3\\s*+\\()|(\\$\\w++)\\s*+=\\s*+class_exists[\\s\\('\"]++(\\w++)['\"\\)\\s]++;\\s*+(?:(\\$\\w++)\\s*+=\\s*+\\4\\s*+;\\s*+)?if[\\s\\(\\!]++(?:\\4|\\6)[\\)\\s\\{]++)++class\\s++(\\w++)\\s*+\\{.+(?:(?<!\"|\"\\{)\\{?\\$[\\w\\{\\}]++(?:\\s*+\\[[^\\]]++\\]++)*+|file_put_contents\\s*+\\(\\s*+(\\$[\\w>]++).+(?:touch|(\\$[\\w>]++)\\s*+=\\s*+fopen\\s*+\\(\\s*+\\8.+?fwrite\\s*+\\(\\s*+\\9.+?fclose)|eval)\\s*+\\(.+(\\$\\w++|new)[\\=\\s]++(?:\\1|\\5|\\7)(?:\\:\\:getInstance)?\\([^\\)]*+[\\);\\s]++(?:(?:echo|else)*+[\\s\\{]*+[\\$\\w\\{\\}]++(?:\\:\\:|->|\\s*+=\\s*+)(?:NULL|[\\w\\{\\}]++\\s*+\\()[^;]*+;[\\s\\}]*+)*(?:.*?exit\\(\\);\\s*+)?(?:$|\\?>)/is"}, {"id": "135", "name": "visitorTracker", "regx": "/((<\\!--|\\/\\*)visitorTracker(\\*\\/|-->)|if[\\s\\(\\!]+(loadStats)[\\)\\s\\{]+function \\4[\\(\\)\\s\\{]+)\\s*(<\\?[ph\\s]+.+?base64_decode\\s*\\(.+?\\?>|.+?document\\.createElement\\(['\"]script[\"']\\);\\s*.+?\\.src\\s*=[\\s'\"]+[htps\\:]*\\/\\/.+?)\\s*(\\1|\\4[\\(\\)\\};\\s]+)/is"}, {"id": "136", "name": "fsockopen fwrite while feof fclose preg_match gzuncompress", "regx": "/<\\?[ph\\s]*((\\$[a-z_0-9]+)\\s*=[^;]+;\\s*|if[\\s\\(]+|file_exists[\\s\\(]+[^\\{]+\\{\\s*)*(\\$[a-z_0-9]+)\\s*=\\s*\\@?fsockopen\\([^;]+;\\s*.*?while[\\s\\(\\!]+feof\\(\\3[\\)\\s]+(\\$[a-z_0-9]+)[\\s\\.]*=.+?fclose\\(\\3\\);\\s*(preg_match\\([^,]+,|\\4\\s*=\\s*end\\(explode)[^,]+,\\s*(\\$[a-z_0-9]+)\\)+;\\s*(echo[\\s\\(]+\\4[^;]*;\\s*)?if[\\(\\s]+[^\\{]*\\6.+?(file_put_contents|gzuncompress)\\([^;]+;[\\}\\s]+(\\@?ini_set\\([^;]+;[\\}\\s]*)*($|\\?>)/is"}, {"id": "137", "name": "Tagged eval function", "regx": "/(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/)\\s*([\\sa-z_0-9\\=]+[;\\s\\(]+)*window[\\.\\[\"']+\\\\x[^;]+;\\s*eval[\\(\\s]+[^\\)]+['\"\\s\\(\\);\\}]+\\1/is"}, {"id": "138", "name": "script var http if document write script src", "regx": "/(<script[^>]*>\\s*((var\\s*)?[a-z_0-9]+\\s*=['\";\\s]*(setTimeout|encodeURIComponent)\\([^\\)]*['\"\\);\\s]{2,})+([var\\s]*([a-z_0-9]+)\\s*=\\s*['\"][hft]+tp[s]*:\\/\\/[^;]+;\\s*((var\\s*)?([a-z_0-9]+)\\s*=\\s*\\6[^;]+;\\s*)+)?if[^\\{]+\\{\\s*document\\.write\\([\"']<[script\\s'\"\\+]{7,}[^\\}]*src=['\"\\s\\+]+(\\9|[fht]+tp[s]*:\\/\\/.+jquery\\.(min\\.)*php)([^;]+[\\};\\s]+)+?<\\/script>)+/i"}, {"id": "139", "name": "function unset wp_list_table items add_action", "regx": "/<\\?[ph\\s]*+(?:\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/|(?:\\#|\\/\\/)[^\\r\\n]++[\\r\\n]++|\\$[^;=]++=[^;]++;|\\$\\w++(?:\\s*+\\[[^\\]]*+\\])*+\\s*+\\([^;]++;|(?:for(?:each)?|if)\\s*+\\([^\\{]++\\{|[\\:\\w]++\\s*+(?:\\([^;]++)?;|else[\\s\\{]*+|\\@|[\\}\\s]++|ini_set\\([^;]++;|function\\s++\\w++\\s*+\\([^\\}]++)*function\\s++(\\w++)\\s*+\\([^\\}]+(?:(?:update_option[\\('\"\\s]++active_plugins|pack\\(['\"]H\\*['\",\\s\\.0-9A-F\\)]++;\\s*+if[\\s\\(\\@is_]++file[_a-z]*+[\\(\\s]++(\\$\\w++)).+?file_get_contents[\\s\\(]++(\\$\\w++).+?file_put_contents[\\s\\(]++(?:\\2|\\3)|unset\\(\\$wp_list_table->items\\[|file_get_contents\\(__FILE__.+?fputs\\(.+?scandir\\().+?add_action\\([^,]++[,\\s0-9\\)'\"]++\\1.++/is"}, {"id": "140", "name": "php error_reporting function detect_cms", "regx": "/<\\?[ph\\s]+(\\@?(error_reporting|set_time_limit|ini_set)\\(.*?0\\);\\s*)+.*?function (detect_cms\\().+\\3[^;]+;\\s*((if\\s*\\([^\\)]+|else)[^\\{]*[^\\}]+[\\}\\s]+)+($|\\?>)/is"}, {"id": "141", "name": "function glues_it sanitize_key call_user_func_array", "regx": "/(if[\\s\\(\\!]+function_exists\\(\\s*['\"][a-z_0-9]+['\"][\\s\\)]+\\{\\s*)?function\\s+([a-z_0-9]+)\\s*\\([^\\)]+[\\)\\s]+\\{\\s*(\\$[a-z_0-9]+)\\s*=\\s*sanitize_key\\([^;]+;\\s*(\\$[a-z_0-9]+)\\s*=\\s*call_user_func_array\\(\\s*\\3[^;]+;\\s*return\\s*\\4.+\\2\\s*\\([^;]+;\\s*.+call_user_func_array\\([^;]+;[\\}\\s]+($|(?=\\?>))/is"}, {"id": "142", "name": "php eval <PERSON>ar <PERSON>", "regx": "/<\\?[ph\\s]+(@?eval\\s*\\(\\s*)+(['\"])[\\s\\\\]*\\$[a-z\\_0-9\\[\\]\\{\\}\\s'\"]+=[\\s\\\\'\"]*(\\\\x[0-9]{2})+.+\\2(\\s*\\))+;\\s*($|\\?>)/i"}, {"id": "143", "name": "if strpos REQUEST_URI include", "regx": "/if([\\s\\(]+str[a-z_0-9]+){2,}[\\s\\(]+\\$_SERVER\\[[\"']REQUEST_URI['\"]\\]\\s*\\)+[^\\)]+[\\)\\s]+\\{[\\s\\@]*(include|require)(_once)?[^;]+[;\\s]+(exit[\\s;]+)?\\}/i"}, {"id": "144", "name": "error_reporting ini_set set_time_limit ignore_user_abort elseif require_once", "regx": "/<\\?[ph\\s]+(\\/\\*[^\\*]*(\\*[^\\*\\/]*)+\\/\\s*|\\/\\/[^\\n]*\\s+)*(\\@?(error_reporting|ini_set|set_time_limit|ignore_user_abort)\\s*\\([^;]+[;\\s]+){3,}((else\\s*|if\\s*\\(+[^\\)]+[\\s\\)]+)+(.*?(assert|eval|file_put_contents|fwrite\\(.*?fclose)\\([^\\)]+[\\);\\}\\s]*)*\\@?(require|include)(_once)?[^;]+[;\\}\\s]+)+($|\\?>)/i"}, {"id": "145", "name": "php if function_exists function curl_init return function trim return base64_decode", "regx": "/(?:(?:(\\$\\w++)\\s*+=[^;]++;\\s*+)?if[\\s\\(\\!]++function_exists[\\s\\(]++[\"'](\\w++)['\"][\\)\\s\\{]++function\\s++\\2\\s*+\\([^\\)]*+[\\)\\s\\{]++(?:(?<I>if\\s*+\\([^\\{]++\\{\\s*+)(?:(\\$\\w++)[\\s=]++(?<P>\\$_(?:GE|POS|REQUES)T[\\[\\s]++['\"])log['\"][\\]\\s]++;\\s*+|(\\$\\w++)[\\s=]++(?&P)pwd['\"][\\]\\s]++;\\s*+)++(\\$\\w++)\\s*+=\\s*+(?:\\([^\\)]++\\))?[\\s\\@]*+wp_authenticate[\\s\\(]++(?:\\4|(?&P)log['\"][\\]\\s]++)[\\s,]++(?:\\6|(?&P)pwd['\"][\\]\\s]++)[\\s\\)]++;\\s*+(?&I)(\\$\\w++)\\s*+=[^;]*+;\\s*+(?:\\$\\w++\\s*+=\\s*+)?wp_remote_get[\\s\\(]++\\8[\\s\\)]++;\\s*+(?<E>\\}\\s*+){2}|(?:(\\$\\w++)[\\s\\.]*+=\\s*+(?:'[^']*?<\\/?script[^']*+'|\"[^\"]*?<\\/?script[^\"]*+\")[;\\s\\.]++)++echo[\\s\\(\"]++\\10[\"\\);\\s]++)(?&E)add_action\\s*+\\([^,]++[,\\s'\"]++\\2['\"][^;]++;\\s*+(?&E)){2}/i"}, {"id": "146", "name": "PHP reversed", "regx": "/^(\\s*\\>\\?\\s*)?;.+(ohce|\\$)([ph\\s]*\\?\\<)?\\s*$/is"}, {"id": "147", "name": "if isset REQUEST Variable Function", "regx": "/(?<=<\\?php|\\*\\/)\\s*+(?:(?:\\@\\s*'[^']*+[^;]*[;\\s]*|\\$[\\w\\{\\}]++(?:\\s*+\\[[^\\]]*+\\]++)*+\\s*+=[^;]++[;\\s]++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/|\\/\\/[^\\r\\n]++\\s++)+.*)?(?:if[\\s\\(\\!]++(?:isset|empty)[\\s\\(]++\\$_(REQUEST|GET|POST|COOKIE|SESSION)[^\\{]++\\{\\s*+)++(?:\\$[\\w\\{\\}]++\\s*+=\\s*+(?:'[^']*+'|\"[^\"]*+\"|\\$|extract[\\s\\(]++\\$_\\1)[^;]*['\";\\)\\s]*)*.*((?<!-\\>)\\$\\w++)(?:\\s*+\\[[^\\]]*+\\])*+\\s*+\\(.*?\\)++\\s*+;[^\\}]*+\\}++(?:[;\\s]*+(?:if[\\s\\(]++[^\\{]++|else\\s*+)\\{[^\\}]++\\}++)*[^\\?\\n]*+(?:\\n|$|(?=\\?>))/i"}, {"id": "148", "name": "eval base64_decode", "regx": "/<\\?(?:php)?(?:(?<C>(?:\\#|\\/\\/)[^\\r\\n]*+[\\r\\n]++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/|\\s++)|null;|=|\\@|(?:(?:error_reporting|eval|set_time_limit|ini_(?:set|restore))\\s*+\\([^\\)]*+\\)++|\\$\\w++\\s*+=)[^;]*+;[\\s;\"'\\)]*+|\\?>\\s*+<\\?(?:php)?(?&C)*+)*(?:if[\\(\\s]++(?:[issetmd5]++\\()*+\\$_(?:POS|GE|REQUES)T\\[.+[\\)\\s\\{]+|echo\\s*+)*(?:(\\$\\w++)\\s*+=\\s*+\\@?(?:base64_decode|strrev)[\\s\\(]++[^;]++;\\s*+\\@?(?:eval[\\s\\(]++.*\\2|\\2\\s*+\\(\\s*+\\$_(?:POS|GE|REQUES)T)|\\@?eval(?&C)*+\\([^;]*?(?:file_get_contents|base64_decode)(?&C)*+\\()[^\\)]*+\\)++(?<Q>;[^\\?\\r\\n]*+(?:\\?(?!>)[^\\?\\r\\n]*+)*+[\\s\\}]*+)(?:return[^;]*+(?&Q))*+(?:$|\\?>\\s*+)/i"}, {"id": "149", "name": "php if md5 REQUEST eval REQUEST", "regx": "/<\\?(?:php)?(?<C>\\s++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/|(?:\\#|\\/\\/)[^\\n]++\\s++)*+(?:(\\$\\w++)(?&C)*+=[^;]+;(?&C)*+|if(?&C)*+\\((?&C)*+(?:isset|md5)(?&C)*+\\((?&C)*+\\$(?<R>_REQUEST|_POST|_GET|_COOKIE|\\{[\"'][^\\}]+\\}+)\\s*+\\[[^\\)]++\\)++(?:(?&C)|\\{)*+)*?(?:\\$\\w++(?&C)*+=(?&C)*+)?(?:assert|eval|\\2|\\$\\3(?<A>\\s*+\\[[^\\]]*+\\]++)++)(?&C)*+\\((?&C)*+\\$(?&R)(?&A)++(?&C)*+[^;]++;(?&C)*+(?:(?:die|exit|echo|print)[^;]*+;(?&C)*+|\\}(?&C)*+)*+(?:$|\\?>\\s*+(?:<form[^>]*+(?:>\\s*+<input[^>]*+)++>\\s*+<\\/form>\\s*+)?)/i"}, {"id": "150", "name": "if isset eval Variable function", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/.*\\s*|\\@?touch\\(.*\\)+;\\s*|foreach[^\\{]++[\\s\\{]+|\\$[a-z_0-9\\{\\$\\}]++(\\[[^\\]]++\\]|[\\.\\s]++|\\/\\*[a-z\\s]++\\*\\/)*+=[^;]++[;\\}\\s]++)*if([\\s\\(\\!]++|\\/\\*[a-z\\s]++\\*\\/)++(isset|\\$[a-z_0-9\\$\\{\\}]+(\\[[^\\]]+\\]|\\s++|\\/\\*[a-z\\s]++\\*\\/)*+)\\([^\\{]++[\\s\\{]++.*?\\s*(\\$[a-z_0-9\\{\\$\\}]+(\\s*\\[[^\\]]+\\])*\\s*=\\s*)?\\@?eval([\\s\\(]++|\\/\\*[a-z\\s]++\\*\\/)++.*?\\$[a-z_0-9\\{\\$\\}]+(\\[[^\\]]+\\]|\\s++|\\/\\*[a-z\\s]++\\*\\/)*+\\(.*?[\\s\\)]+;([;\\}\\s]++|\\/\\*[a-z\\s]++\\*\\/)++(exit[^;]++([;\\}\\s]++|\\/\\*[a-z\\s]++\\*\\/)++)?($|\\?>)/i"}, {"id": "151", "name": "error_reporting exec system passthru fopen REQUEST fwrite", "regx": "/<\\?.+?(set_time_limit\\(0|error_reporting\\(0|explode\\(['\"]wp-content)(?=.+?passthru\\s*\\()(?=.+?system\\s*\\()(?=.+?exec\\s*\\()((?=.+?curl_init\\s*\\()(?=.+?file_get_contents\\s*\\()|(?=.+?readdir\\s*\\()(?=.+?unlink\\s*\\()|(?=.+?fopen\\s*\\((\\$[a-z_0-9]+))(?=.+?\\3\\s*=\\s*\\$_POST))(?=.+?fwrite\\s*\\().+/is"}, {"id": "152", "name": "/function x eval x", "regx": "/(?:if\\s*+\\([^\\{]++\\{\\s*+)?function\\s++(\\w++)\\s*+\\([^\\{]++\\{\\s*+(?:(?:(?:\\$\\w++\\s*+=\\s*+['\"0-9\\-][^;]++|(\\$\\w++)\\s*+=\\s*+\\$(?:\\3|_COOKIE|_REQUEST|_GET|_POST))|(?:(?:\\2|isset\\(\\2[^\\)]*+)[\\)\\s]*+[\\&\\?]++[\\s\\(]*+)*+\\$(\\w++)\\s*+=\\s*+\\2)[^\\&;]*+[\\&;\\s\\(]++)*+\\@?(?:assert|eval)\\((?:(?:\\$\\3|\\2).+?\\1\\(|.+?\\1\\(\\$_(REQUES|GE|POS)T\\[).*?\\);[\\s\\}]*+/is"}, {"id": "153", "name": "GET_dl safe_mode end", "regx": "/^<\\?(?:php)?\\s*+(?:(?:\\@*+(?:session_start|error_reporting|set_time_limit)\\s*+\\([^;]++;\\s*+|\\?>\\s*+<\\?(?:php)?\\s*+|if[\\s\\(\\!]++function_exists[\\s\\(]++[^\\)]*+[\\)\\s\\{]++)*+(?:(?:if[\\s\\(\\!\\@]++(?:(?:isset|stripos)[\\s\\(]++)*+\\$_(?:GET|POST|REQUEST|SESSION|COOKIE|SERVER\\[[\"']HTTP_USER_AGENT)(?:['\"]\\]|\\s*+\\[['\"]\\w{2}['\"]\\])[^\\)]*+[^\\{]++[\\{\\s]++)++(?:die\\s*+\\([^;]++[;\\s\\}]++|.*?safe_mode))++)++.+?(?:\\?>(?:\\s*+<\\/div>\\s*+<\\/body>\\s*+<\\/html>)?|<\\/body>\\s*+<\\/html>['\";\\s\\}]*+(?:\\?>\\s*+)?)$/is"}, {"id": "154", "name": "GLOBALS 0 eval", "regx": "/(^my\\s+\\$([a-z_0-9]+)[O_0][\\s\\='\"]+;(?=.+?while\\(<([a-z_0-9]+)>\\))|<\\?[ph\\s]*(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/[^\\n]*\\n\\s*)*)(\\$(GLOBALS|\\2)?[\\[\\{'\"]*[O_0]+['\"\\]\\}\\s]*[\\=\\.\\s]+[^;]+;\\s*)+.*((eval|[\\$\\{\"']+(\\\\x[a-f0-9]{2}|[li1_])+[\\}'\"]+(\\s*\\[[^\\]]+\\])*)\\s*\\(.+\\)[\\);\\s]+)+(return[^;]*;|\\_++\\3\\_++\\s+(\\S{61}\\s+)+\\S*\\s*)*($|\\?>\\s*(\\~[a-z\\-\\+\\/0-9=]+\\s*$)?)/i"}, {"id": "155", "name": "keyspat viagra cialis", "regx": "/error_reporting\\(0\\);\\s*\\$keyspat[=\\s]+array[\\(\\s]+((['\"])(viagra|amoxicillin|cialis)\\2[\\s,]+){2}.+/is"}, {"id": "156", "name": "session_start error_reporting set_time_limit footer", "regx": "/<\\?[ph\\s]+(\\@*(session_start|error_reporting|set_time_limit)\\([^;]+;\\s*){2,}(echo[\\(\\s'\"]+<(\\!DOCTYPE )?HTML>.+<\\/HTML>['\";\\s]+function perms\\((\\$[a-z_0-9]+)[\\)\\s\\{]+\\$[a-z_0-9]+\\s*=\\s*fileperms\\(\\5\\);.+return[^;]+;[\\}\\s]+|.+?<\\?\\s*echo[\\(\\s]*\\$footer[\\);\\s]+)($|\\?>)/is"}, {"id": "157", "name": "md5tagged eval variable functions", "regx": "/(?:(?<C>\\@\\s*+|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++)*+(?:ini_set|error_reporting)\\s*+(?&C)*+\\([^;]*+;\\s*+)*+(?:(?&C)*+(?<V>(\\$\\w++)(?:\\s*+\\[[^\\]]*+\\]++)*+)[\\s\\.]*+(?&C)*+=(?:[\\s\\(]*+(?&C)*+(?:(?<S>\"(?:[^\"]*+(?<=\\\\)\")*+[^\"]*+(?<!\\\\)\"|'(?:[^']*+(?<=\\\\)')*+[^']*+(?<!\\\\)')|\\d++|(?&V)|strrev\\()(?&C)*+[\\s\\)]*+(?&C)*+[\\s\\.\\^\\-]*+(?:\\/(?!\\*)(?!\\/))?)++;\\s*+)+(?:(?:(?&C)*+(\\$\\w++)[\\s\\.]*+(?&C)*+=\\s*+)?(?&C)*+(?:eval|\\$\\w++)\\s*+(?&C)*+\\((?:(?:[\"']{2}|[,\\s]++|(?&C))*+\\$\\w++\\s*+(?&C)*+\\((?:[\\.\\s]*+(?:(?&V)|(?&S)|(?&C)))++[^\\)]*+[^;]++;\\s*+|[^\\)]*+[^;]*+;\\s*+(?&C)*+\\5\\s*+(?&C)*+\\([^;]++;\\s*+))++(?:(?&C)|\\$\\w++\\s*+(?:;|\\([^;]++;)\\s*+)*+/i"}, {"id": "158", "name": "if isset POST file_get_contents fopen fwrite fclose exit", "regx": "/(?:(?<=^<\\?php)|(?<=^<\\?))(?:\\s*+\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/|\\s*+(?:\\#|\\/\\/)[^\\n]++|\\s*+\\$\\w++\\s*+=\\s*+(?!\\$_(?:REQ|GET|POS|SER|SES))[^;]++;|\\s*+(\\$\\w++)\\s*+=\\s*+(?<G>\\$_(?:REQUEST|GET|POST|SERVER|SESSION)\\[)[^;]++;|[\\s\\@]*+(ignore_user_abort|ini_set|ob_start|ob_end_clean|session_start|set_time_limit|error_reporting)\\s*+\\([^\\)]*+\\)[^;]*+;)*+\\s*+if[\\(\\s\\!]++(?:(?:isset|empty)[\\(\\s]++((?&G))|[^\\)]*?\\1).+?(\\$\\w++)\\s*+=\\s*+\\@?f(?:ile_get_contents|open\\(.+?fgets)\\(\\s*+(?:\\w+?decode[\\(\\s]++)*+(?:\\2|\\5|\\4|['\"][htps:]+\\/\\/|__FILE__).+(?:file_put_contents\\(|fopen\\(.+?fwrite\\(.+?fclose\\()[^;]*+;[\\);\\}\\s]++(?:(?=\\?\\>)|[^\\?]+(?:(?!\\?\\>)\\?|return|extract\\(|die|echo|exit)[^;]*+;[\\s\\}]*+)++(?:(?=\\?\\>)|(?:\\/\\/[^\\n]*+\\n\\s*+){3,})/is"}, {"id": "159", "name": "function Array print exit", "regx": "/<\\?[ph\\s]+(\\$[_\\-\\>\\.a-z0-9\\{\\['\"\\]\\}]+\\s*=[^;]+;\\s*)*((function\\s+[a-z_0-9]+\\(.*?\\)\\s*\\{(?! \\};)(\\$[^=]+=\\s*['\"]{2})?|for[\\s(]+[^\\{]+\\{[^\\}]+\\}.*?|(return|global)\\s+(\\$\\{[^\\}]+\\}+)?[^;]*;|\\$\\{.+?\\}\\s*\\(.*?\\)+;|\\$\\{.+?\\}\\s*=\\s*array\\((([a-z_0-9]+\\(.*?\\)+,\\s*)+[a-z_0-9]+\\(.*?)?\\)+;|if\\s*\\(.*?\\)+\\s*\\{|else[\\s\\{]*|\\$[_\\-\\>\\.a-z0-9\\{\\['\"\\]\\}]+[\\s\\.]*=(\\s*chr\\(.*?\\)+|(.+?\\^){5,}|[^;]+\\^\\s*\\d+)[^;]*;|exit\\(.*?\\)+;|(\\$(\\{[a-z_0-9]+\\(.+?\\)\\}(\\s*\\[[^\\]]*\\]+)*|content|url)[\\s\\+\\.;\\}<>\\.]*)+=\\s*([\\@\\$\\{]*[a-z_0-9\\}]+(\\(.+?\\)|\\[.+?\\]))\\}*;|foreach.+?\\s+as\\s+[^\\)]+[^\\{]+\\{)[;\\s\\}]*){50,}(\\$(\\{[a-z_0-9]+\\(.+?\\)\\}(\\s*\\[[^\\]]*\\]+)*|content|url)[\\s\\+\\.;\\}<>\\.]*)+=\\s*([\\@\\$\\{]*[a-z_0-9\\}]+(\\(.+?\\)|\\[.+?\\]))[;\\}]*($|\\?>)/i"}, {"id": "160", "name": "php if isset GET echo if POST copy FILES", "regx": "/(?<=\\<\\?php)\\s*(echo[^;]*;\\s*|error_reporting\\(0\\);\\s*|\\/\\*.*?\\*\\/\\s*|\\$[a-z_0-9]+\\s*=[^;]+;\\s*|(\\/\\/|\\#)[^\\n]*\\n\\s*)*if[\\s\\(]+isset\\(\\$_(REQUES|GE|POS)T[^\\)]+[\\)\\s\\{]+((echo|print|\\$[a-z_0-9]+\\s*=)[^;]+;\\s*)+if[\\s\\(]+\\$_(REQUES|GE|POS)T[^\\)]+[\\)\\s\\{]+if[\\s\\(]+\\@?copy\\(([\\s\\,]*\\$_FILES(\\[[^\\]]+\\])+)+[\\)\\s]+\\{[^\\}]+((\\s*\\})+(\\s*else[\\s\\{]+[^\\}]+)?)+(\\s*(\\/\\/|\\#)[^\\n]*|\\s*\\/\\*.*?\\*\\/)*(\\s*error_reporting\\(0\\);\\s*function\\s+([a-z_0-9]+)\\s*\\(.+\\15[\\(\\s]+\\$_(REQUES|GE|POS)T.+echo[\\s\\(]*(['\"])<script.+?<\\/script>\\17;[\\s*\\}]+(?=\\/\\*|$|\\?>))?/is"}, {"id": "161", "name": "auth_pass FilesMan", "regx": "/<\\?(?:php)?+\\s*+(?=.*?\\$auth_pass)(?=.*?FilesMan)(?=.*?=[^;]{2000}).++$/is"}, {"id": "162", "name": "php set_time_limit file_get_contents REQUEST file_get_contents FILES fopen REQUESTfwrite", "regx": "/<\\?[ph\\s]+.+?((error_reporting|set_time_limit|date_default_timezone_set)\\([^;]+;\\s*|(\\$[o_0]+)\\s*=[^;]+;\\s*|(\\$[a-z_0-9]+)\\s*=[\\@\\s]*\\$_(POST|GET|REQUEST|SERVER)\\[[\"'][^;]+;\\s*)+((?=.+file_get_contents\\((\\$.+?)\\))(?=.+function (.+?)\\((\\3[,\\s]+\\7)\\))(?=.+\\$[a-z_0-9]+\\s*=\\s*([a-z_0-9]+\\()*\\8\\(\\3[,\\s]+(\\$.+?)\\))(?=.+\\11\\s*=[^;]*\\4)(?=.+fopen\\((\\$.+?),)(?=.+function (.+?)\\(\\3[,\\s]+\\12)(?=.+if[\\(\\s]+\\13\\(\\3[,\\s]+(\\$.+?),)(?=.+\\14\\s*=\\s*\\$_(REQUES|GE|POS)T\\[)|.+?file_get_contents\\(\\$_(REQUES|GE|POS)T.+?file_get_contents\\(\\$_FILES\\[).+?fopen\\((\\$_(REQUES|GE|POS)T|\\12,).+?fwrite\\(.+?(\\?>)/is"}, {"id": "163", "name": "clearstatcache here die", "regx": "/<\\?[ph\\s]*(error_reporting\\(0\\);\\s*)*(if[\\s\\(\\!]+isset[\\s\\(]+\\$_(REQUES|POS|GE)T\\[['\"][0-9_a-z]+['\"]\\][\\)\\s\\{]+((echo|require|\\$[0-9_a-z]+\\s*=\\s*(\\$|(['\"]).*?\\7))[^;]+;[\\s\\}]+)*)+(\\$[0-9_a-z]+\\s*=\\s*)?(\\$wpdb\\->query\\(\\s*|mysql_connect[\\s\\(]+\\$_(REQUES|POS|GE)T\\[['\"][0-9_a-z]+['\"]\\].+?)\"INSERT\\s+INTO[\\s\\`\"\\.\\$0-9_a-z\\-\\>]+users[\\s\\`]+\\(.+((mysql_close\\([^\\)]*\\)|clearstatcache.+here;\\s+die|(\\$[0-9_a-z]+)\\s*=\\s*\\@file_get_contents\\(.+\\13[\\)\\s]*);[\\}\\s]+)+($|\\?>)/is"}, {"id": "164", "name": "unset self", "regx": "/<\\?[ph\\s]*(\\@?chmod\\([^\\)]+\\);\\s*)*if[\\s\\(\\!]+(isset|empty)[\\s\\(]+\\$_(REQUEST|GET|POST|COOKIE)[^\\{]+[\\{\\s]*(\\$[a-z_0-9\\{\\}]+)\\s*=.+?mkdir[\\s\\(\"'\\.\\/]+\\4[\\);\\s]+foreach.+?\\{[^\\}]+\\}\\s*(if[\\s\\(\\!]+is_dir[^\\)]+\\)+[\\{\\s\\@]*mkdir[\\s\\(]+[^\\)]+\\)+;[\\s\\}]*)+.*?(unlink\\([^\\)]+\\);\\s*)+($|\\?>)/is"}, {"id": "165", "name": "if isset REQUEST touch move_uploaded_file", "regx": "/(?=(?:(?<=\\}|;|<\\?|<\\?php|\\*\\/)\\s*+\\{\\s*+)?(?:if[\\(\\s\\!]++(?:(?:isset|empty)[\\s\\(]++\\$_(?:REQUEST|GET|POST|FILES))[^\\{]++\\{\\s*+(?:(?:(?<V>\\$\\w++(?:\\s*+\\[[^\\]]*+\\]++)*+)[\\.\\s]*+=(?<S>[\\s\\.]*+(?:\"(?:[^\"]*+(?<=\\\\)\")*+[^\"]*+(?<!\\\\)\"|'(?:[^']*+(?<=\\\\)')*+[^']*+(?<!\\\\)'|[0-9]++))++[;\\s\\}]++)*+(?:(?:((?&V))[\\.\\s]*+=[^;]*?(?:\\3|\\4|\\5)|((?&V))[\\.\\s]*+=[^;]*?(\\$_FILES))[^;]*+;[\\s\\}]*+)++)++(?<A>touch\\s*+\\(\\s*+(?:\\3|\\4|\\5)[^;]*+;[\\s\\}]*+|((?&V))[\\.\\s]*+=(?&S)+[;\\s]++|(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/\\s*+|(?:\\#|\\/\\/)[^\\n]*+\\s++)|if[\\(\\s\\!]++(?:file_exists[\\s\\(]++)?(?:\\1|\\3|\\4|\\5|\\7)[^\\{]++\\{\\s*|(?:\\}\\s*+|else[\\s\\{]++)++|echo[\\(\\s'\"]+[^;]*+;[\\s\\}]*+)*+)++(?:\\@|if[\\(\\s]++)*+move_uploaded_file\\s*+\\([^\\;]++;(?:[\\}\\s]*+)++)[^\\{]*+(?<B>\\{(?:[^{}'\"\\/\\#]++|\\/[^\\*\\/]|(?&C)|\"(?:\\\\\"|[^\"])*+(?<!\\\\)\"|'(?:\\\\'|[^'])*+(?<!\\\\)'|(?&B))*\\})/i"}, {"id": "166", "name": "php if isset REQUEST eval file_put_contents include unlink", "regx": "/<\\?[ph]*+(?:(?:(?:\\@?(?:ignore_user_abort|set_time_limit|ini_set|error_reporting|DEFINE|\\4)\\s*+\\([^\\)]*+\\)[\\s\\)]*+|(?<C>[\\s\\}]++|\\/\\/[^\\n]*+|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/)|global(?&C)++(\\$\\w++)|(\\$\\w++)(?&C)*+=|print(?:_r)?(?&C)*+\\(|echo\\s*+[\\('\"])[^;]*+;(?&C)*+)++|(?:try\\s*+|function(?&C)++(\\w++)[^\\{]*+)\\{(?&C)*+)*+(?:(?:(?:for|while)(?&C)*+\\([^\\)]++|if[\\s\\(\\!]++(?:(?:empty|strlen|isset)\\(\\$_(?:REQUES|GE|POS)T|[\\$\\w]*+[\\s\\=]*+[scanoperd]+dir[\\(\\s]++(\\$\\w++)))[^\\{]++\\{(?&C)*+)++.*?(\\$\\w++)(?&C)*+[\\.\\s]*+=.*?(?:curl_[^\\(]+|eval|file_get_contents|\\2)(?&C)*+\\((?&C)*+(?:\\3|\\6|\\5|ord(?&C)*+\\().+?file_..t_contents[\\( ]++([\\$\\w\\['\"\\]\\.]+).+?(?:echo[\\s\\(]+\\3|include(?:_once)?[\\(\\s]++\\7.+?(?:(\\$\\w++)(?&C)*+=(?&C)*+\\7)?unlink[\\(\\s]++(?:\\7|\\8)|curl_init\\(.+?curl_setopt\\([^,]++,\\s*+CURLOPT_URL[,\\s]++\\7|\\}\\s*+catch\\s*+\\(\\s*+Exception\\s*+\\$\\w++[\\)\\s]*+\\{[^\\}]*+\\}\\s*+|file_get_contents[\\s\\(\"']++http[^;]++;[\\s\\/\\@]*+file_put_contents\\(.+?error_reporting[\\(\\s0\\)]++;\\s*+).*+/is"}, {"id": "167", "name": "php chdir REQUEST getcwd move_uploaded_file", "regx": "/<\\?.+?(chdir[\\(\\s]+\\$_(REQUES|POS|GE)T\\[|error_reporting[\\(\\s]+0).+?(\\$[a-z_0-9]+)[=\\s\\@]+getcwd[\\s\\(]+.+?((\\$[a-z_0-9]+)[=\\s\\@]+\\$_(REQUES|POS|GE)T\\[[^;]+;\\s*if[\\s\\(]+)?(copy|move_uploaded_file)[\\s\\(]+(.+?unlink[\\s\\(]+.+?scandir[\\s\\(]+(\\3|\\5)|\\$_FILES\\[[^,]+[,\\s]+(\\$_\\2|\\3|\\5))(.+changepass\\()?(.+?\\?>\\s*<\\?[ph\\s]*\\})*.+?($|\\?>(?!\")(\\s*<\\/(body|html)>)*)/is"}, {"id": "168", "name": "error_reporting ini_set unlink FILE", "regx": "/\\@?error_reporting\\(0\\);\\s*(ini_set\\(.+\\@?unlink\\(__FILE__\\);|(if[\\s\\(\\!]+(isset\\(|file_exists\\()?\\s*\\$_(SERVER|REQUEST|GET|POST|FILES)\\[[^\\]]+\\]+[^\\)]*\\)[\\)\\s\\{]*(echo[^;]+;\\s*)*)+\\@?copy\\(([\\s\\,]*\\$_FILES(\\[[^\\]]+\\]+)+)+[\\)\\s]+;[\\s\\}]+\\?>\\s*(<(form|input)[^>]+>\\s*)+<\\/form>\\s*(<\\?[ph\\s]+\\}+)?)/is"}, {"id": "169", "name": "include GET", "regx": "/(?:(?:\\s*+(\\$\\w++)\\s*+=\\s*+([\"'])(?:(?:[^\\\\'\"]*+|\\\\\\\\|\\\\\\2)*+(?:\\\\(?:x[a-f\\d]{2}|\\d)|(?<=\\.css)(?=\\2)))++[^\"']*+\\2;|\\s*+(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/)|\\s*+(\\$\\w++)\\s*+=\\s*+(?:substr\\()?\\1[^;]*+[;\\s]++|\\s*+strpos\\s*+\\([^;]++;++|\\s*+\\$\\w++\\s*+=[\\s\"']*+[\\w\\s]*+['\";\\s]++)*+\\s*+(?:if[\\s\\(\\!]++(?:(?:is|file)\\w++[\\s\\(]++(?:\\1|\\4)|\\d[^\\)]*+)[\\)\\s\\{]++(?:\\w++[\\s\\('\"]++\\1[^;]*+;\\s*+(?:[\\}\\s]*+else[\\s\\{]*+)?)*+)?\\@?(?:include|require)(?:_once)?\\s*+(?&C)*+[\\(\\s]*+(?:base64_decode\\s*+\\([^\\)]++|\"(?:[\\w\\/_\\-\\.]*+\\\\(?:x[a-f0-9]{2}|[0-9]{2,3}))++[^\"]*+\"|\\1|\\4|\\$_(?!REQUEST\\['target'\\];)(?:POS|REQUES|GE)T(?:\\s*+\\[[^\\]]++\\]|\\s*+\\{[^\\}]++\\})++\\s*+)++[\\s\\)]*+;(?:\\s*+(?:\\3|\\}))*+)++/i"}, {"id": "170", "name": "php function file_get_contents fwrite unlink __FILE__", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|[a-z_0-9]+\\([^)]*[^;]+;\\s*|\\$[a-z_0-9]+\\s*=[^;]+;\\s*|\\/\\/[^\\n]*\\n\\s*)*function\\s+([a-z_0-9]+)\\(.+((\\$[a-z_0-9]+)\\s*=[\\@\\s]*file_get_contents\\((.+(\\$[a-z_0-9]+)\\s*=[\\@\\s]*(base64_decode|file_get_contents)\\(\\5)*.+file_put_contents\\([^,]+,\\s*(\\5|\\7)[^;]+;[\\}\\s]*(\\$[a-z_0-9]+\\s*=[^;]+;\\s*)*|(file_..t_contents|mkdir)\\(.+fopen\\(.+fwrite\\(.+fclose\\((.+?function\\s+([a-z_0-9]+)\\()*.+unlink\\(\\s*(\\$_SERVER[\\s'\"\\[\\{]+SCRIPT_FILENAME|__FILE__)[^\\)]*[\\);\\s\\}]*)((\\3\\([^;]+|\\13\\([^;]+|echo[^;]*|exit[^;]*)[\\);\\s\\}]*)+($|\\?>)/is"}, {"id": "171", "name": "auth_pass FilesMan safe_mode eval", "regx": "/<\\?(?=.*\\$auth_pass)(?=.*FilesMan)(?=.*safe_mode)(?=.*(eval|netstat)\\().+/s"}, {"id": "172", "name": "php error_reporting ini_set set_time_limit if isset REQUEST file_get_contents file_put_contents unlink ie", "regx": "/<\\?[ph\\s]+(\\/\\*[^\\*]*(\\*[^\\*\\/]*)+\\/\\s*|\\/\\/[^\\n]*\\s+)*(ignore_user_abort\\s*\\([^;]*;\\s*|\\$[a-z_0-9]+\\s*=[^;]*[;\\s]+)+((else[\\s\\{]*|if[\\s\\(]+[^;]+|\\$[a-z_0-9]+\\s*=\\s*)*(error_reporting|ini_set||set_time_limit|while(?=\\(1\\)))\\s*\\([^;]+;[\\}\\s]+)+((if[\\s\\(]+(is_writable|isset|file_exists)[\\s\\(]+\\$(_REQUEST|_GET|_POST|path)[^;]+)?(\\@?(file_get_contents|file_put_contents|unlink|chmod)\\s*\\([^;]+;([\\}\\s]*(if[\\s\\(]+[^\\)]+\\)+|else)?[\\{\\s]*(die|print|sleep|echo)[^;]*;)*[\\}\\s]*)+[\\}\\s]+){3,}.*?($|\\?>)/is"}, {"id": "173", "name": "php create_function Variable function", "regx": "/<\\?[ph\\s]*(\\/\\*([^\\*]*\\*[^\\/])*[^\\*]*\\*\\/\\s*|\\/\\/.*\\s*)*((\\$[a-z_\\-0-9]+)\\s*=.+;\\s*|if\\s*\\([^\\{]+\\{\\s*)+((\\$[a-z_\\-0-9]+)\\s*=\\s*)?create_function\\([^,]+,\\s*(\\4[^;]+;[\\s\\@]*\\6\\(|\"(\\\\([0-9]{2,3}|x[0-9a-f]{2}))+\")[^\\)]*[\\);\\s\\}]+($|\\?>)/i"}, {"id": "174", "name": "if isset REQUEST eval or file_put_contents", "regx": "/<\\?[ph\\s]+(function ([a-z_0-9]+)\\([^\\{]+[\\{\\s]+.+return[^;]*;[}\\s]+)?if[\\s\\(]+isset[\\s\\(]+\\$_(REQUES|GE|POS)T(\\s*\\[[^\\]]+\\])+[\\)\\s\\{]+(\\$[a-z_\\-0-9]+\\s*=\\s*.+?;\\s*)*(((header|unlink)\\([^;]+[;\\s]+){2,}[\\}\\s]*else[\\{\\s]+(\\$[a-z_\\-0-9]+\\s*=\\s*.+?;\\s*)*((\\$[a-z_\\-0-9]+\\s*=\\s*)?\\@?(eval|\\2)\\(.+?\\);\\s*){2,}|file_put_contents\\([^,]+,\\s*base64_decode\\(.+?\\);)[\\}\\s]*(\\?>|$)/is"}, {"id": "175", "name": "php echo passthru _REQUEST", "regx": "/(?<=<\\?php\\s)[\\s\\@]*+(?:echo|print)[\\s\\(]*+(?:(['\"]).*?\\1[\\)\\s]*+;\\s*+)?(?:(\\$\\w++)\\s*+=\\s*+)?(?:\\@?(?:passthru|exec|system|\\$\\w++)\\s*+\\(|\\`)\\$_(?:REQUES|GE|POS)T[\\s\\[]++[^\\]]++[\\]\\s\\)\\`]++;(?:[\\s\\@]*+(?:echo|print)[\\s\\(]*+[^;]*?\\2[^;]*+;)?/i"}, {"id": "176", "name": "php if is_dir file_get_contents if file_put_contents echo", "regx": "/<\\?[ph\\s]+(\\/\\/[^\\n]*[\\r\\n]+|\\$[a-z_0-9]+\\s*=[^;]*;\\s*|error_reporting\\([^;]+;\\s*)+.+?(include[^;]*wp-class-headers.php.+?|if[\\(\\s]+is_dir[^\\{]+\\{(\\s*\\$[a-z_0-9]+\\s*=file_|[^\\}]+\\}\\s*)get_(all_dirs\\(.+?|contents\\([^;]*;\\s*\\$[a-z_0-9]+\\s*=[^;]*;\\s*(if[\\(\\s]+)?))file_put_contents\\(.+?((touch\\(.+|echo[^;]*;\\s*|\\}(\\s*else[\\s\\{]+)?)\\s*)+($|\\?>)/is"}, {"id": "177", "name": "move_uploaded_file _FILES __FILE__", "regx": "/<\\?(?:php)?\\s*+(?:(\\$\\w++)\\s*+=\\s*+\\$_FILES\\[[^;]++;\\s*+(\\$\\w++)\\s*+=[^;]++;\\s*+if[\\s\\(]++file_exists[\\s\\('\"\\.\\/]++\\2[\\s\\('\"\\)\\s]++unlink[\\s\\('\"\\.\\/\\)]++\\2)?.*?(?:move_uploaded_file[\\s\\(]++(?:\\$_FILES\\[[^,]++,\\s*+__FILE__|\\1)|system\\s*+\\(\\s*+['\"]mv ['\"]\\s*+\\.\\s*+\\$_FILES\\[).*?(?:$|\\?>)/is"}, {"id": "178", "name": "php function die setcookie if empty if isset form move_uploaded_file", "regx": "/<\\?[ph\\s]+((\\$[a-z_0-9]+)\\s*=[^;]+;\\s*)*(if[\\(\\s]+(\\2|\\!empty[\\(\\s]+)[^\\{]+\\{\\s*((((\\$[a-z_0-9]+)\\s*=\\s*)?(\\2\\(.*?\\)+|\\$_(REQUES|GE|POS)T[^;]+);\\s*|if[\\(\\s\\!]+(isset[\\(\\s]+.+?\\)+\\s+[a-z_0-9]+|\\8[^\\}]*)))+[\\}\\s]*|function [a-z_0-9]+\\([^\\{]+\\{\\s*(die\\(.+?\\);\\s*|(\\$_COOKIE.+?;\\s*|setcookie\\(.+?\\);\\s*){2})\\}\\s*)+([\\s\\{]*(\\$[a-z_0-9]+)\\s*=\\s*\\$_(REQUEST|GET|POST|FILE)[^;]+;[\\s\\}]*)*(\\?>\\s*|echo[\\s\\(]*(['\"]))(<(form|input)[^>]*>[a-z\\:0-9\\s]*)+<\\/form>(\\s*<\\?[ph\\s]+|['\"];\\s*)(\\$[a-z_0-9]+\\s*=[^;]+;\\s*|if[\\s\\(]+)*(move_uploaded_file\\([^\\;]+|(\\$[a-z_0-9]+)\\s*=\\s*\\$[a-z_0-9]+\\s*\\(.*?\\));[\\s\\}]*($|\\?>)/i"}, {"id": "179", "name": "php if REQUEST add_action function global if get_option return ob_start wp_rewrite->flush_rules wp_cache_flush", "regx": "/<\\?[ph\\s]+(if\\s*\\([^\\)]*\\$_(POS|REQUES|GE)T[^\\{]+[\\{\\s]+|add_action\\s*\\([^,]+,\\s*(['\"])([a-z_0-9]+)['\"\\);\\s\\}]+|function\\s+\\4[^\\{]+[\\{\\s]+)*(((include|require)(_once)?\\([^;]+;\\s*|if[\\s\\(\\!]+username_exists\\([^\\{]+[\\{\\s]+)*(\\$[a-z_0-9]+)\\s*=\\s*wp_create_user\\(.+set_role[\\(\\s'\"]+administrator|(global[^;]*;\\s*)?if[\\s\\(\\!]+get_option\\([^\\)]+[\\)\\s\\{]+return[^;]*;[\\s\\}]+ob_start\\(\\);\\s*\\?>.+\\$wp_rewrite->flush_rules\\(\\);\\s*wp_cache_flush\\()+[\"'\\s\\);\\s\\}]+($|\\?>)/is"}, {"id": "180", "name": "if isset REQUEST FILE stripslashes REQUEST", "regx": "/if[\\s\\(]+isset[\\s\\(]+\\$_(GE|POS|REQUES)T(\\s*\\[[^\\]]*\\]+|\\s*\\{[^\\}]*\\}+)+[\\s\\)\\{]+((\\$[a-z_0-9]+)\\s*=\\s*\\$_(GE|POS|REQUES)T(\\s*\\[[^\\]]*\\]+|\\s*\\{[^\\}]*\\}+)+[^;]*[;\\s]*)*(assert|eval|\\$[a-z_0-9]+)\\s*\\(+stripslashes\\(\\$_(GE|POS|REQUES)T(\\s*\\[[^\\]]*\\]+|\\s*\\{[^\\}]*\\}+)+[\\s\\)]+;[\\}\\s]*/i"}, {"id": "181", "name": "error_reporting function error_404 ttp_request_custom getIp getUseragent convertIpToString http_build_query file_get_contents fwrite long2ip", "regx": "/<\\?.+?error_reporting\\(((?=.+?get[_]*Ip\\()(?=.+?function error_404\\()|(?=.+?function _[a-z0-9]+\\(){11,})((?=.+?stream_context_create\\()|(?=.+?request_custom\\())(?=.+?http_build_query\\()(?=.+?file_get_contents\\()((?=.+?file_get_contents\\(){4,}|(?=.+?header\\())(?=.+?long2ip\\().+/is"}, {"id": "182", "name": "if isset REQUEST foreach array eval", "regx": "/if[\\s\\(]+([a-z_0-9]+\\s*\\(\\s*)*\\$_(REQUES|GE|POS)T\\[[^\\{]+\\{\\s*(\\$[a-z_0-9]+\\s*=[^;]+;\\s*|.*?foreach\\(array.*?)*[\\s\\@]*(system|eval)\\(.+\\s*(exit[^;]*;\\s*)*\\}/i"}, {"id": "183", "name": "if empty SERVER HTTP_USER_AGENT set_time_limit move_uploaded_file return", "regx": "/(?:<\\?(?:php)?\\s*+|if[\\s\\(\\!]++(?:file_exists\\s*+\\(|isset[\\s\\(]++\\$_(?:REQUES|GE|POS)T\\[[^\\]]++\\]++)[^\\{]++\\{[^\\}]++[\\s\\}]++|\\?>\\s*+|<[^\\?][^\\n]*>\\s*+){2,}(?:(?:print|echo)[^;]*+;\\s*+|(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/\\s*+|(?:\\#|\\/\\/)[^\\n]*+\\s++))*+if[\\s\\(\\!]+(?:\\@?is_uploaded_file|empty[\\s\\(]++\\$_SERVER\\[[\"']HTTP_USER_AGENT[\"']\\][\\)\\s]++.+?set_time_limit).+?move_uploaded_file\\s*+\\(.+(?:return\\s*+\\$\\w++|echo[\\(\\s'\"]++[^;]++|touch\\s*+(?&C)*+\\([^;]++);[\\s\\}]++(?:$|\\?>\\s*+(?:<\\/(?:body|html)>\\s*+)*+)/is"}, {"id": "184", "name": "fwrite unlink eval chmod POST phpinfo move_uploaded_file exec system passthru", "regx": "/<\\?.+?error_reporting\\(0\\)(?=.+?php(_uname|info)\\()(?=.+?eval\\().+?(chmod|getcwd)\\((\\$_POST|[^\\{]++\\{\\s*\\$[a-z_0-9]+\\s*=\\s*\\$_FILES)\\[.+?move_uploaded_file\\((.+?((\\$[a-z_0-9]+)\\s*=['\"htpsf\\:\\/\\.\\s]*\\$_(REQUES|GE|POS)T(?=.+?function\\s+([a-z_0-9]+)\\s*\\((\\$[a-z_0-9]+))|=\\s*\\8\\s*\\(\\6|CURLOPT_URL[,\\s]+\\9|exec\\(|system\\(|passthru\\()){3}.+/is"}, {"id": "185", "name": "if function_exists function variable function", "regx": "/<\\?(?:php)?\\s*+(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)(?:[^\\n\\?]++|\\?(?!\\>))*+(?:\\s|\\?>)++)*+(?:(?<V>\\$\\w++(?:\\s*+\\[[^\\]]++\\]++)*+)[\\.\\s]*+=(?:[\\s\\.]*+(?:(?&V)|\"(?:[^\"]*+(?<=\\\\)\")*+[^\"]*+(?<!\\\\)\"|'(?:[^']*+(?<=\\\\)')*+[^']*+(?<!\\\\)'))++;\\s*+|if[\\(\\s\\!]++function_exists[\\(\\s]++['\"][^\\{]++\\{\\s*+)*+function\\s++(\\w++)\\s*+\\([^\\}]++(?:.+?\\3\\s*+\\([^;]++[;'\"\\)\\}\\s]++)++(?:(?:(\\$\\w++)\\s*+=\\s*+)?(?&V)\\s*+\\([^;]++[\"'\\);\\}\\s]++|eval[\\s\\(\"]++\\4[\"\\);\\s\\}]++)++(?&C)*+(?:$|\\?>)/is"}, {"id": "186", "name": "require_once wp-config ini_set mysql_query UPDATE users SET user_pass", "regx": "/^.*<\\?[ph\\s]*(include|require)(_once)?\\(.+wp-config\\.php[\"'\\s\\);]+(\\@?ini_set\\([^\\)]+[\\);\\s]+)+.+?(mysql_query\\(['\"]UPDATE[\\s\\{'\".]+\\$table_prefix[\\s\\}'\".]+users\\s+SET\\s+user_pass.+){2,}$/is"}, {"id": "187", "name": "if rename or file_put_contents touch", "regx": "/<\\?[ph\\s]+if[\\s\\(]+isset[\\s\\(]+\\$_(REQUES|GE|POS)T(\\s*\\[[^\\]]+\\])+[\\)\\s\\{]+[^;]+;\\s*[^\\}]*\\}[\\}\\s]*(\\$[a-z_0-9]+)[\\s*\\.=]+base64_decode\\(.+?file_put_contents\\(([^,]+),\\s*\\3.+?unlink\\(.+?\\(\\4[\\);\\s]+.+/is"}, {"id": "188", "name": "eval REQUEST alone", "regx": "/<\\?[ph\\s]+((\\$[a-z_0-9]+)\\s*=[^;]+;\\s*)?(\\@?(eval|system|echo\\s+base64_decode)([\\(\\s*\\@]+stripslashes)?[\\(\\s*\\@]+(\\2|\\$_(REQUEST|GET|POST|SERVER|HEADERS)\\s*(\\[[^\\]]+\\]\\s*)+)[\\);\\s]+)+($|\\?>)/i"}, {"id": "189", "name": "FilesMan preg_replace .", "regx": "/<\\?[ph\\s]*(\\/\\*.+?\\*\\/\\s*)*(\\$[a-z_0-9]+\\s*=[^;]+;\\s*)*\\$[a-z_0-9]+\\s*=[\\s\"']*F[\"'\\.\\s]*i[\"'\\.\\s]*l[\"'\\.\\s]*e[\"'\\.\\s]*s[\"'\\.\\s]*M[\"'\\.\\s]*a[\"'\\.\\s]*n[\"'\\s]*;(\\s*\\$[a-z_0-9]+\\s*=[^;]+;)*\\s*(\\$[a-z_0-9]+|preg_replace)\\([^\\)]*[\\);\\s]+($|\\?>)/is"}, {"id": "190", "name": "html head unzip FILES unlink form", "regx": "/^(?:<(?:\\!DOCTYPE\\s++)?html[^>]*+>\\s*+)++<head>\\s*+(?:<script.+?Hacked\\s++by.+?<iframe .+?<audio.+?|.+?(?:fileperms\\s*+\\((?:(?=.+?(\\$\\w++)\\s*+=\\s*+fopen\\s*+\\(\\s*+(\\$\\w++)[^\\)]*+[\\);\\s\\{\\}if\\(\\@]++(?<=f)write\\s*+\\(\\s*+\\1)?+.+?\\2\\s*+=[^;]*?\\$_(?:REQUES|GE|POS)T|(?:.+?posix_get[pwugr]+id\\s*+\\(){2,}).+?move_uploaded_file\\s*+\\(.+?unlink\\s*\\(.+?(?:exec\\s*+\\(.+?\\breaddir\\(.+?\\?|\\?>\\s*+<script[^>]+?src=['\"][htps:]*+\\/\\/[^>]++>\\s*+<\\/script)|unzip[\\s\\(]++\\$_FILES\\[.+?unlink[\\s\\(]++\\$.+\\?>\\s*+<\\/form)>\\s*+)<\\/body>\\s*+<\\/html>\\s*+$/is"}, {"id": "191", "name": "if array_keys GET file_get_contents if unlink fopen fwrite fclose", "regx": "/<\\?[ph\\s]+(\\/\\/[^\\n]*\\n\\s*|\\/\\*[^\\*]*(\\*[^\\*\\/]*)+\\/\\s*|\\@?(ignore_user_abort|ini_set|set_time_limit|error_reporting)\\(.*?\\);\\s*|\\$[a-z_0-9]+\\s*=[^;]+;\\s*)*(((if\\s*\\(|else)[^\\{]*\\{\\s*)?function\\s+.+?return[^;]*;[\\s\\}]+)+((\\$[a-z_0-9]+\\s*=\\s*)?\\$[a-z_0-9]+\\([^;]+[\"'\\);\\}\\s]*)+.+?(f(open|write|close)\\(.+?){3,}include\\(.*?\\);\\s*unlink\\(.*?\\);\\s*(\\$[a-z_0-9]+\\s*=[^;]+;[\\}\\s]*)*($|\\?>)/is"}, {"id": "192", "name": "display_errors file_get_contents _REQUEST filename update_code fwrite unlink", "regx": "/(?:(?<=<\\?php\\s)|(?<=<\\?\\s))(?:[\\s\\@]*+(?:error_reporting\\(0[^;]++|display_errors[^;]++|ini_set\\([^;]++|global\\s++\\$\\w++|for(?:each)?[\\s\\(]++\\$\\w++[^\\{]++|function\\s++(?:\\w++)[\\s\\(]++\\$(\\w++)[^\\{]++|if[\\s\\(]++[^\\)]++[\\)\\s]++|\\$(\\w++)\\s*+=\\s*+(?!urldecode|file_get_)[^;]++)(?:\\{|;))++\\s*+(\\$\\w++)\\s*+=\\s*+(?:file_get_contents\\([^;]*|urldecode\\()\\$(?:\\1|\\2|_POST|_GET|_REQUEST).+?(?:\\$\\w++\\s*+\\(\\s*+([^\\s,\\)]++).+?include(?:_once)?|fwrite)\\s*+\\(\\s*+([^\\s,\\)]++).+?unlink\\(\\s*+(?:__FILE__|\\5|\\4)[^;]*+[\\);\\}\\s]++(?:(?:die|echo|exit|\\$\\w++\\s*+=)[^;]*+[\\);\\}\\s]++)*+(?:(?=\\?\\>)|(?=\\/[\\*\\/])|(?=require|include)|$)/is"}, {"id": "193", "name": "php Vars Array base64_decode function Variable Function", "regx": "/(\\$[\\$\\{]*[a-z_0-9]+\\}*(\\s*\\[[^\\]]+\\]|\\s*\\{[^\\}]+\\})*)\\s*=\\s*(chr|array[\\(\\s]+base64_decode)\\(([^;]+;\\s*if\\s*\\([\\s\\!\\@]*|.+?function ([a-z_0-9]+)\\s*\\(.+?)\\1\\s*(\\[[^\\]]+\\]\\s*)*\\((.+(\\$[\\$\\{]*[a-z_0-9]+\\}*(\\s*\\[[^\\]]+\\]|\\s*\\{[^\\}]+\\})*)\\s*=\\5\\()?.+?(echo|die|print)[\\s\\(]*(\\3|\\8)[^;]*;[\\s\\}]*/is"}, {"id": "194", "name": "exec system passthru fwrite Variable Function REQUEST", "regx": "/<\\?(?=.*passthru\\(|.*exec\\(|.*system\\().*?(((\\$[a-z_0-9]+)\\s*=\\s*([a-z_0-9]+\\(\\s*)*\\$_(REQUEST|GET|POST|SERVER|COOKIE)\\[[^;]+;[\\s\\}]*(else\\s*\\{[^\\}]+\\}[\\s\\}]*)?)+.*(?<!new )\\$[a-z_0-9]+(?!\\( \\$key \\) \\);)\\([\\s\"]*\\3|\\$[a-z_0-9]+\\(\\$_(REQUES|GE|POS)T).+?($|\\?>)/is"}, {"id": "195", "name": "php if function_exists file_put_contents fopen chmod system unlink", "regx": "/<\\?(.+?(touch|header)\\().+?if[\\s\\(\\!]+function_exists\\([\\s'\"]+(file_put_contents)[\"'\\s\\)\\{]+function\\s+\\3\\(.+?fopen\\(.+?\\3\\(.+?chmod\\(.+?system\\(.+(unlink|exit).+/is"}, {"id": "196", "name": "base64_decode file_put_contents chmod touch fsockopen curl_exec ob_start", "regx": "/\\/\\/\\s*istart.+?(\\$[a-z_0-9]+)[\\s*\\.=]+base64_decode\\(.+?file_put_contents\\([^,]+,\\s*\\1.+?chmod\\(.+?touch\\(.+?(fsockopen\\(.+?fgets\\(|curl_init\\(.+?curl_exec\\().+?ob_start\\(.+?\\/\\/iend[^\\n\\?]*/is"}, {"id": "197", "name": "if isset REQUEST eval", "regx": "/<\\?[ph\\s]+((\\$[a-z_0-9]+\\s*=[^;]+;\\s*)*(if[\\s\\(]+isset|for(each)?)[\\(\\s]+\\$_(REQUES|GE|POS)T[^\\)]+\\)[\\)\\s\\{]+(\\$[a-z_0-9]+[\\.=\\s]+\\$_(REQUES|GE|POS)T\\[[^\\]]+\\][;\\s]+|if\\s*\\([^;]+;[\\s\\}]*function\\s+[a-z_0-9]+\\(.+return[^;]*;[\\}\\s]*(\\$[a-z_0-9]+\\s*=[^;]+;\\s*)*)+((\\$[a-z_0-9]+[=\\s]+)?\\@?(eval|file_put_contents|fopen|fwrite|fclose)\\([^\\)]+\\);\\s*)+((echo|exit)[^\\;]*;\\s*)*[\\}\\s]*(else[\\s\\{]*)?)+($|\\?>)/i"}, {"id": "198", "name": "error_reporting eval curl_init file_get_contents file_put_contents include unlink", "regx": "/\\$[a-z_0-9]+[=\\s]+__FILE__;\\s*\\$[a-z_0-9]+\\s*=[^;]{2000}.*?error_reporting\\(.+?eval\\(.+?(curl_init|file_get_contents).+?file_put_contents\\(.+?include.*?unlink\\(.*?\\);\\s*\\}\\s*else\\s*\\{[^\\}]+[\\}]+/is"}, {"id": "199", "name": "set_error_handler eval file_get_contents", "regx": "/<\\?(?:php)?(?:(?<C>(?:\\#|\\/\\/)[^\\r\\n]*+[\\r\\n]++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/|\\s++)|(?:error_reporting|set_error_handler|ini_set)(?&C)*+\\([^;]*+;|null;|=|\\@)*+(echo[\\s\\(]++\\@?file_get_contents[\\(\\s]++(chr\\(\\d++[\\s\\)\\.]++)++|eval(?&C)*+\\([^\\)]*?file_get_contents(?&C)*+\\((?:(?:\"[^\"]*+\"|'[^']+')((?&C)|[\\.\\)])++)++);(?&C)*+(?:$|\\?>\\s*+)/is"}, {"id": "200", "name": "if for unset wp_wp form", "regx": "/<\\?[ph\\s]*(\\$([a-z_0-9]+)\\s*=[^;]+;\\s*)*(\\$(((l|wp)[_]*){2})\\s*=[^;]+;\\s*)+(if|for|unset|(\\$[a-z_0-9]+\\s*=\\s*)?\\$(\\2|\\4))\\s*\\(\\$(\\2|\\4)[^;]+;\\s*.*?\\?>\\s*<form.+?name=['\"](((l|wp)[_]*){2})['\"].+?<\\/form>/is"}, {"id": "201", "name": "php class viaWorm", "regx": "/<\\?[ph\\s]+(\\/\\*.*?\\*\\/\\s*)*(class viaWorm\\s*\\{.+?fwrite\\(.+?file_get_contents\\(.+?unlink\\(.+?base64_encode\\(.+?)?file_put_contents\\([^,]+,\\s*base64_decode\\((\\@?file_get_contents\\(.+|[^\\)]+[\\);\\s]+echo[\\s\\(]+file_get_contents\\([^\\)]+[\\);\\s]+($|\\?>))/is"}, {"id": "202", "name": "assert Hex 64encodedText Hex", "regx": "/(assert_options\\(.+?\\);\\s*|function [a-z_0-9]+\\([^\\)]*[\\)\\s\\{]+.+?return[^;]*[;\\}\\s]+|\\$(color|auth|pass|default|__)[_\\-\\>\\.a-z0-9]*\\s*=.+?;\\s*)*(((\\$[a-z_0-9]+)\\s*=.*?\\$\\2|(\\$[a-z_0-9]+)\\s*=.*?\\5).*\\s*)*assert\\(\\s*(\"(\\\\x[0-9A-F][0-9A-F])+'[\\/a-z\\_0-9\\=]+'(\\\\x[0-9A-F][0-9A-F])+\"|\\6)[\\)\\s]+;/i"}, {"id": "203", "name": "post strtoupper if isset eval", "regx": "/((\\$[a-z_0-9]+)\\s*=[^;]+;\\s*)+(\\$[a-z_0-9]+\\s*=\\s*(\\$[a-z_0-9]+|strto...er)\\s*\\(([\\s\\.]*\\$[a-z_0-9]+\\s*\\[\\d+\\])+[\\);\\s]+)+if[\\(\\s]+isset\\s*\\([^\\)]+[\\)\\s]+\\{\\s*eval\\s*\\([^\\)]+[\\);\\s]+\\}/i"}, {"id": "204", "name": "function gzinflate base64_decode for chr ord eval", "regx": "/(function\\s+([a-z_0-9]+)\\s*\\(\\s*(\\$[a-z_0-9]+)[\\)\\s\\{]+\\3[=\\s]+gzinflate\\(base64_decode\\(.+[\\);\\s]+)?for\\s*\\(([^\\{]+|.+?)\\{\\s*(\\$[a-z_0-9]+)(\\s*\\[[^\\]]+\\]+)*[\\.\\s]*=[\\@\\s]*chr\\([^;]+[;\\s\\}]+(return[^;]*[;\\}\\s]+)?eval\\((\\2\\(|\\3|\\5)[^\\)]*\\)[\\);\\s]+/i"}, {"id": "205", "name": "ini_set ob_start register_shutdown_function if HTTP_USER_AGENT file_get_contents return", "regx": "/<\\?[ph\\s]+(\\@?(ignore_user_abort|ini_set|set_time_limit|error_reporting)\\(.*?\\);\\s*)++(?=.*?(register_shutdown_function\\(['\"\\s]++([a-z_0-9]++)['\"\\s]++[^;]++;))(?=.+(ob_start\\([\"'\\s]*+(.*?)['\"\\s]*+\\);)).*?function\\s+(\\6\\s*\\(.+?return[^;]*;[\\s\\}]+|\\4\\s*\\((\\$[a-z_0-9]++).*?unlink[\\s\\(]+\\8[^;]+;[\\s\\}]*)(if\\s*\\([^\\)]++[\\)\\s\\{]+|\\3[\\}\\s]*|\\5[\\}\\s]*|exit\\([^;]+;\\s*class\\s+([a-z_0-9]++)[\\s\\{]+.*?function\\s+([a-z_0-9]++)[\\s\\(]+.+\\10[\\:\\->]+\\11\\s*\\([^;]++[;\\s\\}]+){3}($|\\?>)/i"}, {"id": "206", "name": "if isset REQUEST eval REQUEST", "regx": "/if[\\s\\(]+(isset|empty)[\\s\\(]+\\$_(REQUES|GE|POS)T[^\\{]+[\\{\\s]+.*eval[\\s\\(]+[\\$a-z_0-9]+[^\\)]*\\)+(.+(die|exit|echo|print|return)[^;]*;)*[\\s\\};]*($|\\?>)/i"}, {"id": "207", "name": "if COOKIE gzinflate base64_decode eval", "regx": "/(?:\\$([a-z_0-9]++)\\s*+=\\s*+getallheaders[\\s\\(]++[^;]++[;\\s]++)?if\\s*+\\(.+?\\(\\$(?:_COOKIE|\\1).+?[\\{\\s]+(\\$[a-z_0-9]+)\\s*+=\\s*+(?:.*?base64_decode\\(.+?eval\\(\\2\\).+|(['\"]).+?\\3[;\\s]++(\\$[a-z_0-9]+)\\s*=[^;]++[;\\s]++(\\@?file_put_contents\\(\\4.+?\\2[^;]++[;\\s]+|(\\@|echo[\\(\\s]+['\"]\\\\x.+?['\"][\\);\\s]+)*(include|unlink)[\\(\\s]+\\4[\\);\\s]+){3,}.+|\\$\\1(?:\\s*\\[[^\\]]*+\\])*\\s*\\([^;]++[;\\s]+\\2\\s*\\([^;]++[;\\s]+)[\\s\\}]*/i"}, {"id": "208", "name": "auth_pass copy FILES exec passthru system shell_exec", "regx": "/<\\?(?=.*\\$(md5|auth)_pass\\s*=)(?=.*copy\\(\\$_(POST|FILES)\\[)(?=.*exec\\()(?=.*passthru)(?=.*system)(?=.*shell_exec\\().+/is"}, {"id": "209", "name": "ini_set error_reporting if function_exists function echo return file_get_contents file_put_contents setcookie add_action add_filter", "regx": "/(?:(?<C>(?:\\#|\\/\\/)[^\\r\\n]*+\\s++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+)++|\\$\\w++(?:\\s*+\\[[^\\]]*+\\]++)*+[\\s\\.]*+=[^;]++;\\s*+|(?:ini_set|error_reporting|set_time_limit|session_start)\\s*+\\([^\\)]*+\\);\\s*+)++(?:(?:if[\\s\\(\\!]++function_exists[\\(\\s]++([\"'])(.+?)\\2[\\)\\s]++\\{\\s*+)?function\\s++(\\w++)\\s*+\\([^\\)]*+\\)[\\)\\s\\{]++.+?(?:(?:echo|return)[^;]*+;[\\}\\s]*+)++)++(?:(\\$\\w++)\\s*+=[^;]*?(?:\\3|\\4)[^;]++;\\s*+|.+?(?:file_..t_contents|etcookie)\\(.+?file_..t_contents\\().*?(?:(?:Include|eval)[\\s\\('\"\\.]++(?:\\3|\\4|\\5)[^;]++;[\\}\\s]*+|add_(?:action|filter)\\([^,]++,\\s*+['\"](?:\\3|\\4)['\"][\\s\\)]++;[\\}\\s]*+){2,}/is"}, {"id": "210", "name": "if isset base64_decode REQUEST eval", "regx": "/(?:<(?:(?:\\!DOCTYPE\\s++)?(html)|(head)|(title)|(body)|(form)|(label)|input|(?:\\/(?:\\1|\\2|\\3|\\4|\\5|\\6)))[^>]*+>[^<]*+)*+<\\?(?:php)?+\\s*+(?:(?:\\@?(?:ini_set|ignore_user_abort|set_time_limit)\\(|\\$\\w++\\s*+=)[^;]++[;\\s]++)*+if[\\(\\s\\!]++(?:PHP_SAPI[^&]*+[\\&\\(\\s\\!]++)?(?:(?:isset|empty|strpos)[\\(\\s]++)?(?<G>\\@?\\$_(?:SERVER|REQUEST|GET|POST)\\[)[^\\{]++[\\s\\{]++(?:(?:(\\$\\w++)\\s*+=\\s*+(?:(?&G)(?:base64_decode|\\$\\w++)\\s*+\\([^;]++|(?:base64_decode|\\$\\w++)\\s*+\\(++\\s*+(?&G)[^\\]]++\\]++)[\\);\\s]++)++.+?eval\\(\\8|(print|echo)[\\s\\(]++base64_decode\\s*+\\([^\\)]++)[\\);\\s\\}]++(?:\\s*else[\\s\\{]+)?(?:echo (?:\"[^\"]*+\"|\\$\\w++);[\\s\\}]++)?(?:$|\\?>\\s*+(?:[^<]*+<\\/(?:\\1|\\4|\\5)\\>)*+)/i"}, {"id": "211", "name": "base64_decode function curl return exit", "regx": "/<\\?[ph\\s]*(\\/\\*.+?\\*\\/\\s*)*((\\$[a-z_0-9]+\\s*=\\s*base64_decode|error_reporting|if[\\s\\(]+file_exists)\\([^;]+[\\);\\s\\}]+|echo[^;]+;[\\s\\}]*|(\\$[a-z_0-9]+)\\s*=\\s*\\$_(REQUES|GE|POS)T.+(\\$[a-z_0-9]+)\\s*=\\s*file_get_contents[\\s\\(]+\\4.+eval[\\s\\(]+\\6[^;]+;[\\s\\}]*)+function\\s+([a-z_0-9]+)\\s*\\([^\\{]+[\\{\\s]+((\\$[a-z_0-9]+[\\s*=]+)?curl_[^;]+;\\s*)+.*?return.+\\7(.+exit)?[^;]*;[\\}\\s]+($|\\?>)/is"}, {"id": "212", "name": "php base64_decode file_put_contents unlink function_exists Hex call_user_func Hex", "regx": "/<\\?[ph\\s]+(\\$\\{[^\\}]+[\\}\\s]+(\\[[^\\]]+[\\]\\s\\}]+)*=[^;]+[;\\s]+(\\@?(\\$[a-z_0-9]+\\s*=|define\\(|session_start\\(|error_reporting\\(|ini_set\\(|set_time_limit\\(|set_magic_quotes_runtime\\()[^;]+[;\\s]+|if\\([^\\{]+[\\{\\s]+[^\\}]+[\\}\\s]+)*)+([^;]+[;\\s]+)?.+file_put_contents\\(.+?base64_decode\\(.+?unlink\\(.+?function_exists\\(['\"]\\\\x.+?call_user_func\\(['\"]\\\\x.+?($|\\?>)/is"}, {"id": "213", "name": "curl_init header Location return preg_replace_callback create_function return REMOTE_ADDR", "regx": "/<\\?.+?curl_init\\(.+?header[\\(\"'\\s]+Location:.+?return preg_replace_callback\\(.+?create_function\\(.+?return \\$_SERVER\\[[\"']REMOTE_ADDR.+(\\?>|$)/is"}, {"id": "214", "name": "php set_time_limit ini_set error_reporting if array_key_exists HTTP_USER_AGENT search-tracker.com preg_match google bing ob_start", "regx": "/<\\?[ph\\s]+.*?set_time_limit\\(.+?ini_set\\(.+?error_reporting\\(.+?if[\\(\\!\\s]+array_key_exists\\([\"']HTTP_USER_AGENT.+?http:\\/\\/search-tracker\\.com\\/in\\.cgi\\?.+?(preg_match\\([\"']\\/([^\\/]*google|[^\\/]*bing){2}.+?){2}.+?ob_start\\(.+/is"}, {"id": "215", "name": "if isset FILES|REQUEST move_uploaded_file else echo", "regx": "/<\\?[ph\\s]+((\\$[a-z_0-9]+\\s*=|error_reporting\\(|set_time_limit\\(|ini_set\\(|echo[\\s\\('\"]+)[^;]+[;\\s]+)*(([a-z_0-9]+)\\([^;]+[;\\s\\}]+)*(if[\\(\\s]+substr[\\(\\s]+[^\\{]+[\\s\\{]+(\\$[a-z_0-9]+\\s*=[^;]+[;\\s\\}]+)+)?((if[\\(\\s]+(isset[\\(\\s]+)?\\$_(FILES|REQUEST|POST|GET)[^\\)]*[\\)\\s\\{]+((\\$[a-z_0-9]+\\s*=|echo[\\s\\('\"]+)[^;]+;\\s*)*)+(\\@|if[\\(\\s]+)*(move_uploaded_file\\([^;]+|(fwrite\\([^;]+;\\s*)+fclose\\([^;]+)([;\\s\\}\\@]+(chmod|echo)[\\s\\('\"]+[^;]+)*[;\\s\\}]+(else[\\{\\s]+|(echo|exit|header)[^;]*[;\\s\\}]+)*)+(function\\s+\\4\\([^;]+[;\\s\\}]+(\\$[a-z_0-9]+\\s*=[^;]+[;\\s\\}]+)*if[\\(\\s]+[^\\{]+[\\s\\{]+(\\@|if[\\(\\s]+)*mkdir\\([^;]+[;\\s\\}]+return[^;]+[;\\s\\}]+)?($|\\?>)/i"}, {"id": "216", "name": "html body php opendir readdir !file_exists fopen fwrite", "regx": "/(<(\\!|\\/)?(doctype|html|head|meta|title|body)[^>]*>[a-z_0-9\\s]*){2,}<\\?[ph\\s]+\\$[a-z_0-9]+\\s*=\\s*'<\\?[ph\\s]+.+?opendir\\(.+?readdir\\(.+?(\\!file_exists\\(.+?fopen\\(.+?fwrite\\(.+?){3,}\\?>\\s*(<\\/(html|body)[^>]*>\\s*){2,}$/is"}, {"id": "217", "name": "function X_ip X_macros error_404 http_request fwrite FUNCTION", "regx": "/\\<\\?.+?_ip\\((.+?function [a-z0-9]+_macros\\(){4}.+?function error_404\\(.+?http_request.+?fwrite\\(.+?__FUNCTION__.+/s"}, {"id": "218", "name": "perl use IO::Socket scan_dir uname system exec", "regx": "/\\#\\!(\\/usr)?\\/bin\\/(perl.+?scan_dir\\(.+?uname\\(.+?system\\(.+?filemanager.+|[ba]*sh\\s+wget\\s+\\-o\\s+(\\S+)\\s+(\\S+)[^;]*;\\s*((chmod|chown|curl)\\s+(\\S+\\s+\\3(\\s+\\4)?[;\\s]*)+)+rm\\s+\\-[rf]+\\s+\\3[;\\s]*$)/is"}, {"id": "219", "name": "path if file_exists is_writable if function_exists WriteData", "regx": "/\\$path[=\\s]+.+[\\s]*if[\\s]*\\(\\!file_exists\\(.+?is_writable\\([^\\)]*[\\)\\s\\{]+if[\\s]*\\(function_exists\\(.+?WriteData.+?WriteData\\(\\);[\\s\\}]+/i"}, {"id": "220", "name": "display_errors create_wp_user REQUEST fwrite unlink", "regx": "/^.+?display_errors.+?create(_|\\s+TABLE\\s+\\`)wp_user(\\`.+?(\\$[a-z_0-9]+\\s*=(\\s*\\$[a-z_0-9]+\\s*\\(){2}[^;]+;\\s*){2}|\\(\\$_REQUEST\\[.+?fwrite\\(.+?unlink\\().+$/is"}, {"id": "221", "name": "php array foreach array eval Array Function POST", "regx": "/<\\?[ph]*\\s+(\\$[a-z_0-9\\-]+)[=\\s]+array\\([^;]+;\\s*foreach[\\s\\(]+\\1.+?eval[\\s\\(]+\\1\\[[^\\]]+\\]+[\\(\\s]+\\$_POST\\[\\1.+?($|\\?>)/is"}, {"id": "222", "name": "web shell fopen passthru", "regx": "/^.+?error_reporting\\(.+?web[ \\t]*shell.+?fopen\\(.+?passthru\\(.+?$/is"}, {"id": "223", "name": "eval array_pop REQUEST", "regx": "/eval\\([\\sa-z_0-9\\.\\(]*(?:fromCharCode\\([0-9,\\s]++|array_pop\\(\\$_(?:GE|POS|REQUES)T)(?:\\s*+\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/)*+[\\)\\s]++;\\s*/i"}, {"id": "224", "name": "/auth_pass love set_error_handler", "regx": "/<\\?(?=.*?unlink\\s*+\\((?:\\$|[A-Z]*+_++)FILE)(?=.*?\\$(?:_(?:POST|GET|REQUEST|COOKIE)\\[['\"])?(?:password(?:hash)?|auth_pass)['\"\\]\\)\\s\\!]*+=)(?=.*?loveLogin|.*?move_uploaded_file\\s*+\\()(?=.*?(?:set_error_handler|mkdir)\\s*+\\(|.*?function\\s++(?:array_to_json|dirtoarray)\\s*+\\().++/is"}, {"id": "225", "name": "GETdo_remove safe_mode end", "regx": "/if\\(\\$_GET\\['do'\\]==\"remove\"\\)\\{\\nunlink\\(getcwd\\(\\)\\.\\$_SERVER\\[\"SCRIPT_NAME\"\\]\\);.+safe_mode.+else.+'\\.\\$end;/s"}, {"id": "226", "name": "set_time_limit unlink base64_decode fwrite exec passthru", "regx": "/<\\?[ph\\s]+(?=(?:(?:(?:sleep|set_time_limit|error_reporting|ignore_user_abort)\\s*\\([^;]++[;\\s]++){2}.*?(?:(\\$[a-z_0-9]++)\\s*=\\s*)?[a-z_0-9]++\\(__FILE__.+?)?unlink\\([\\s\"'\\.]*+(\\1|__FILE__)).+?;\\s*\\@?f(ile_put_contents|write)\\s*\\(.+/is"}, {"id": "227", "name": "function class variable function", "regx": "/<\\?[ph\\s]*function\\s+([a-z_0-9]+)\\(.+?return[^;]*;\\s*\\}\\s*class[^\\{]+\\{.*?function[^\\{]+\\{\\s*(\\$[\\$\\{]*[a-z_0-9]+\\}*(\\s*\\[[^\\]]+\\])*\\s*=\\s*)*\\1\\((.*?\\$[\\$\\{]*[a-z_0-9]+[\\}\\s]*(\\[[^\\]]+\\]\\s*)*\\([^;]+;\\s*){50,}[\\}\\s]*($|\\?>)*/is"}, {"id": "228", "name": "php Starting calls c99shexit", "regx": "/<\\?[ph\\s]+\\/\\/Starting calls.+chdir\\(\\$lastdir\\)[;\\s]+[a-z0-9]+exit\\(\\)[;\\s]*($|\\?>)/is"}, {"id": "229", "name": "php password hg_exit", "regx": "/<\\?[ph\\s]*(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*\\$password.+(leafMailCheck\\s*\\(.+print[\\s\\(]*(['\"]).+?\\4;|ob_start[\\(\\s]+array[\\(\\s]+\\$[^;]+;[\\s\\}]*mt_srand\\(\\);|eval\\([^\\)]++[^;]++[;\\)\\']+|hg_exit\\(\\);.+)[\\s\\}]*($|\\?>)/is"}, {"id": "230", "name": "shell system passthru", "regx": "/\\<\\?.+?(shell|authp).+?error_reporting\\(0\\).+?set_time_limit\\(0\\).+?ini_set\\(.+fopen\\(.+/is"}, {"id": "231", "name": "function BSSV eval", "regx": "/function BSSV\\(.+eval\\(BSSV\\(.+?\\)+[;\\s]*/is"}, {"id": "232", "name": "error_reporting password exit me", "regx": "/<\\?[ph\\s]*error_reporting\\(0\\);\\s*\\/\\/If there is an error, we'll show it, k\\?\\s*\\$password\\s*=.+ :-\\)\\s*exit\\(\\);\\s*\\?>\\.\\$me\\./is"}, {"id": "233", "name": "var functions return new RecursiveArrayIterator array", "regx": "/(\\$[a-z_0-9]+[\\s=]+\"[a-z_\\-\\0-9]*\";\\s*)+((\\$[a-z_0-9]+[\\s=]+)?\\$[a-z_0-9]+\\(+.+?[\\)]+;\\s*)+.+return\\s+new\\s+RecursiveArrayIterator[\\(\\s]+array.+?[\\)\\s]+;\\}+/is"}, {"id": "234", "name": "PHP DEFINE file_get_contents base64_decode json_encode", "regx": "/<\\?(?:php)?\\s*+(?:\\@*+(?:(\\$\\w++)\\s*+(?:\\[[^\\]]*+\\]++\\s*+)*+=\\s*+(?<S>\"(?:[^\"]*+(?<=\\\\)\")*+[^\"]*+(?<!\\\\)\"|'(?:[^']*+(?<=\\\\)')*+[^']*+(?<!\\\\)'|\\d++)|(\\$...)\\w++\\s*+(?:\\[[^\\]]*+\\]++\\s*+)*+=\\s*+(?:Array|explode)\\s*+\\(|[if\\s\\(\\!]*+DEFINE[d]?+\\((['\"])(.*?)\\4|error_reporting\\()[^;]*+;[\\s\\}]*+)++.+file_get_contents\\([^;]*(?:\\5|;\\s*+\\$[stored]*+crc).+?base64_decode[\\(\\s]++(?:\\1|\\3).+(?:(\\$\\w++)\\->run\\(\\);\\s*+unset\\(\\6\\);\\s*+$|json_encode\\s*+\\([^;]++;\\s*+\\?>)/is"}, {"id": "235", "name": "if array_keys GET eval base64_decode exit", "regx": "/<\\?[ph\\s]*(\\@?error_reporting\\(0\\);\\s*)?if[\\(\\s]+array_keys\\(\\$_GET.+\\s+((<\\?[ph\\s]*)?\\$[a-z_0-9]+[\\s\\{\\$]+\\@*eval\\(base64_decode\\(['\"][a-z_\\/\\+\\=0-9]+['\"][\\)\\}\\{\\$\\s]+exit\\([\\)\\}\\s]+\\&\\s*\\$[a-z_0-9]+[;\\s\\}]+\\?>)+/i"}, {"id": "236", "name": "php error_reporting set_time_limit ini_set _xcookie base64_decode str_rot13 gzinflate", "regx": "/<\\?[ph\\s]+(\\@?(error_reporting|set_time_limit|ini_set)\\(.*?0\\);\\s*){3,}(.+?_xcookie){9}(.+?(\\$[a-z_0-9]+\\s*=|return)([\\s\\@]*(base64_[den]+code|str_rot13|gz[dein]+flate|strrev)\\(){3,}[^;]+;){2}.+return false[;\\s\\}]+($|\\?>)/is"}, {"id": "237", "name": "excessive spaces script eval function packed", "regx": "/<script[^>]*>[^<]*eval\\(function\\(p,a,c,k,e,d\\)\\{.+?\\s*<\\/script>\\s*<script[^>]*>[^<]*\\[['\"]Histats\\.start['\"\\s,]+1[\\s,]+4214393[\\s,]+[^<]*<\\/script>\\s*(<noscript>\\s*(<a[^>]*>\\s*)*<img[^>]*[\\?|\\&]4214393[^>]*>\\s*(<\\/a>\\s*)*<\\/noscript>)*/i"}, {"id": "238", "name": "php error_reporting unlink __FILE__ call_user_func", "regx": "/<\\?[ph\\s]*(\\$[a-z_0-9]+\\s*=[^;]+;\\s*|\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/[^\\n]*\\s*)*(\\@?(error_reporting|ini_set|set_time_limit)\\s*\\([^;]+;\\s*){3}.+\\@?unlink\\(__FILE__[^;]+;\\s*.+(mysqli_close|call_user_func)[\\s\\(]+\\$[^;]+;\\s*((return|echo)[^;]+;[\\s\\}]*)*($|\\?>)/is"}, {"id": "239", "name": "php error_reporting system readdir file_put_contents move_uploaded_file unlink form html", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/[^\\n]*\\s*|[\\@if\\s\\(]*\\$[a-z_0-9]+\\s*=[^;]+;\\s*|\\@*(define|session_start|error_reporting|ini_set|set_time_limit|[sg]et_magic_quotes_[a-u]+)\\([^;]+[;\\}\\s]+)+((function\\s+([a-z_0-9]+)\\s*\\(|.+?system\\s*\\(.+?setcookie\\s*\\(.+?readdir\\s*\\()(?=.+?file_put_contents\\(\\s*([^,]++),).+?unlink\\s*+\\(\\s*+(\\7|\\$_(REQUES|POS|GE)T\\[)).+?<\\/body>\\s*<\\/html>\\s*([\"'];\\s*exit[;\\}\\s]+\\6[\\(\\);\\s]*\\/\\/[^\\n]*+\\s*)?$/is"}, {"id": "240", "name": "php dir recursive function  loop", "regx": "/<\\?[ph\\s]+(?:.*(?:dir|include|require|_once|getcwd)\\s*\\([^;]+;\\s*|\\/\\/.*\\s+)*(?:(\\$[a-z_0-9]+)\\s*=.*?(?:md5|rand)\\s*\\([^;]+;\\s*)+(?:\\/\\/.*\\s+)*function ([a-z_0-9]+)\\([^\\{]+\\{\\s*(?:.+dir\\s*\\([^;]+;\\s*|[\\}\\s]*else[\\{\\s]*|(?:while|if|for(?:each)?)\\s*\\(.+[\\{\\s]*)+.*\\2\\s*\\([^;]+;\\s*(?:(?:\\}[\\}\\s]*|else[\\{\\s]*|(\\$[a-z_0-9]+)\\s*=\\s*+(?!\\$)(?!\"))*+.*(?:copy|dir|unlink|echo|header|print|getenv|file|fopen|fwrite|date[^\\(]*|(\\$[a-z_0-9]+)\\s*=|(\\$[a-z_0-9]+)(\\s*\\[[^\\]]+\\])*\\s*=)\\s*([\\('\"]+|\\3|\\4|\\5)[^;]*;\\s*|\\/\\/.*+\\s+)+($|\\?>)/i"}, {"id": "241", "name": "php error_reporting if isset REQUEST mail", "regx": "/<\\?[ph\\s]*(if[\\s\\(\\!]+(empty|strlen|isset)[\\s\\(]+\\$_(REQUES|GE|POS)T[^\\)]+[\\)\\s\\{]+[^;]+;[\\s\\}]*|error_reporting\\([^;]+;\\s*)+.*?(if[\\s\\(\\!]+(empty|strlen|isset)[\\s\\(]+\\$_(REQUES|GE|POS)T[^\\)]+[\\)\\s\\{]+(\\$[0-9_a-z]+)\\s*=\\s*(rand\\(|\\$_(REQUES|GE|POS)T)).+((\\$[a-z_0-9]+\\s*=\\s*|if[\\s\\(]+|else[^\\{]*[\\{\\s]+)*mail[\\s\\(]+[^;]*\\7[^;]+[\\);\\s\\}]*)+(((if[\\(\\s]+|else)[^\\{]*[\\{\\s]+)?((header|echo|print|exit)[^;]*[;\\s\\}]+)+)*($|\\?>)/is"}, {"id": "242", "name": "php o_0 variable functions end", "regx": "/<\\?[ph\\s]*((\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/[^\\n]*\\s*|error_reporting\\([^;]+;\\s*)*((([^;]*\\$[o_0]+|.*function\\s+([o_0]+))|(exit|break|unset|header|return)[^\\)]*)[^;]+['\"\\);\\}\\s]+)+?(([\\$\\{\"']+(\\\\x[a-f0-9]{2}|[li1_]+[\\s=]+\\7)+[\\}'\"\\s]*)(\\[[^\\]]+\\]\\s*)*\\([^;]+[;\\}\\s]+)+)+?.*([\\$\\{\"']+(\\\\x[a-f0-9]{2}|[li1_])+[\\}'\"\\s]*(\\[[^\\]]+\\]\\s*)*\\([^;]+;\\s*)+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/[^\\n]*\\s*)*($|\\?>)/i"}, {"id": "243", "name": "php function curl file_put_contents call", "regx": "/<\\?[ph\\s]+((ini_set|echo|\\$[a-z_0-9]+\\s*=)[^;]+;\\s*)*function\\s+([a-z_0-9]+)[^\\{]+[\\s\\{]+(\\$[a-z_0-9]+\\s*=[^;]+;[\\}\\s]*)*((\\$[a-z_0-9]+\\s*(\\[[^\\]]+\\]+\\s*)*=\\s*)?curl_.+;[\\}\\s]*)+(((if[\\s\\(]+[^;]+|else[\\s\\{]+[^;]+|\\$[a-z_0-9]+\\s*=\\s*)?(return|file_put_contents\\(|\\$_(SERVER|GET|POST|REQUEST)|curl_|exec\\(|echo|print)[^;]+;[\\}\\s]*)+(\\$[a-z_0-9]+\\s*(\\[[^\\]]+\\]+\\s*)*=\\s*)?\\3\\([^;]+;[\\}\\s]*((.*fopen|fwrite|fclose|eval)\\(.+\\s*)*)+($|\\?>)/i"}, {"id": "244", "name": "php _COOKIE file_get_contents_X function curl return FORM", "regx": "/<\\?(?:php)?(?:(?<C>[\\{\\s]++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++)*+(?:(?:isset|md5|if|(\\$\\w++)|[\\}\\s]*+else\\s*+\\{\\s*+(\\2)\\s*+=\\s*+['\"]https?:\\/\\/[^\\}]++\\}++|file_exists[\\s\\('\"]++\\.htaccess['\"][^\\}]++\\}++)[\\s\\(=\\!]++)++\\$(?:_REQUEST|_POST|_GET|_COOKIE|\\{[^\\}]++\\}++||\\[[^\\]]++\\]++)++.*(?:;|\\s*+\\{)(?:\\s*+(?:echo|exit)[^;]*+;)*+[\\}\\s]*+)++(?:if[\\s\\(]++)?(\\$\\w++)[=\\s]++(?:(file_get_contents_\\w++)[^\\}]++\\}\\s*+function\\s++\\5[^\\{]++|curl_init[\\(\\)\\s]++)\\{\\s*+(?:(?:(\\$\\w++)[\\s=]++curl_init|curl_setopt[\\s\\(]++(?:[^,]++[\\s,]++CURLOPT_(?!URL)|(\\4|\\6)[\\s,]++CURLOPT_URL[\\s,]++(?:\\2|\\3)))[^;]*+;\\s*+)++(?:(?:(\\$\\w++)[\\s=]++)?(?:(?:curl_exec|curl_close|eval)[\\s\\(]++)++(?:\\7|\\8)[^;]*+;\\s*+)++(?:(?:exit|return)[^;]*+;|[\\}\\s]++|\\?>|<form[^>]*+(?:>\\s*+<input[^>]*+)+>\\s*+<\\/form>|$)++/i"}, {"id": "245", "name": "php error_reporting define function get_real_ip return ip", "regx": "/<\\?[ph\\s]+(\\/\\/[^\\n]*\\n\\s*|\\/\\*[^\\*]*(\\*[^\\*\\/]*)+\\/\\s*|\\@?(ignore_user_abort|session_start|ini_set|set_time_limit|error_reporting)\\(.*?\\);\\s*|define\\([^\\)]+[\\);\\s]+|\\$+[a-z_0-9]+(\\s*\\[[^\\]]*\\])*\\s*[+\\.]*=[^;]+;\\s*)+.+function\\s+(createIndexLM|get_real_ip|ip_visitor_city)\\(.+(return \\$ip|return \\$city|mysqli_close[^;]+;\\s*return[^;]*)[;\\s\\}]+(\\/\\/[^\\n]*\\s*|\\/\\*[^\\*]*(\\*[^\\*\\/]*)+\\/\\s*)*($|\\?>)/is"}, {"id": "246", "name": "php error_reporting add_filter function script zoneid file_get_contents_curl function hide add_action", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)+((\\$[a-z_0-9]+)\\s*=[^;]+;\\s*|(|ini_set|error_reporting|set_time_limit)\\([^\\)]*\\);\\s*)+(add_(filter|action)[\\s\\(]+['\"][^,]+[,\\s]+((['\"])(.+?)\\9[\\);\\s]+function\\s+\\10\\s*\\([^\\{]+\\{|function\\s*\\([^\\{]+\\{)[^\\}]+[\\}\\);\\s]+)+(\\/*\\$[a-z_0-9]+\\s*=[\\s'\";]+)+<script.+function\\s+file_get_contents_[a-z_0-9]+[^\\{]+\\{\\s*([^;]*curl_[^;]*;\\s*|return[^;]*;\\s*|(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*|\\}\\s*)+(function\\s+(hide[a-z_0-9]+)\\([^\\{]+\\{.+add_action[\\s\\(]+['\"][^,]+[,\\s]+(['\"])\\16\\17[\\);\\s]+)?(function[^\\{]+\\{.+return\\s+\\$_SERVER\\[[^;]+[\\};\\s]+)*($|\\?>)/is"}, {"id": "247", "name": "php if class_exists class new calss else catch", "regx": "/<\\?(?:php)?\\s*+(?:(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/\\s*+|(?:\\#|\\/\\/)[^\\n]*+\\s++)++|if[\\s\\(\\!]++class_exists[^\\{]++\\{\\s*+)*+class\\s++(\\w++)\\s*+\\{.+(?:(\\$\\w++)[\\=\\s]++new\\s++\\2[^;]*+;\\s*+(\\$\\w++)[\\=\\s]++\\3[^;]*+;\\s*+(?:[^\\n]*(?:\\2|\\3|\\4|else|catch)[^\\n]*+[\\s\\}]++)++|class\\s++([a-f\\d]{32,33})\\s++extends\\s++\\2.+new\\s++\\5[^;]*+[;\\s\\}]++\\/\\/[a-f\\d]{32,33}\\s*+)(?:$|\\?>)/is"}, {"id": "248", "name": "meta propeller script function document.createElement script", "regx": "/(<meta(\\s+(name|content)=['\"](propeller|[0-9a-f]{32})['\"]){2}[^>]*>\\s*)?<(script)[^>]*>[\\s\\(]*function\\s*\\([^\\}]+\\}[\\);\\s]*\\(document\\.createElement[\\(\\s]+(String\\.fromCharCode\\(|['\"]script['\"]).+\\)[;\\s]*(\\/\\*\\s*[0-9a-z]+\\s*\\*\\/\\s*|$|<\\/\\5>)+/i"}, {"id": "249", "name": "var document createElement script String.fromCharCode getElementsByTagName appendChild", "regx": "/(var\\s+([a-z_0-9]+)\\s*=\\s*(document|\\d+)[;\\s]+)+(var\\s+([a-z_0-9]+)\\s*=\\s*\\2\\.createElement[\\(\\s'\"]+|script[^;]+;\\s*|(var\\s+[a-z_0-9]+\\s*=\\s*)?String\\.fromCharCode[^;]+;\\s*|\\5\\.[a-z_0-9]+\\s*=[^;]+;\\s*)+if[\\(\\s]+document\\.[^\\}]+[\\}\\s]+else\\s*\\{\\s*\\2\\.getElementsByTagName[^\\.]+\\.appendChild[\\s\\(]+\\5[^\\}]+\\}[\\}\\s]*/i"}, {"id": "250", "name": "php if function_exists define EOF file_put_contents", "regx": "/<\\?[ph\\s]+\\$[a-z_0-9]+\\s*=\\s*__FILE__;\\s*((\\$[a-z_0-9]+)\\s*=\\s*function_exists\\([^;]+;\\s*)+if[\\s\\(]+[^\\{]+\\2[^\\{]+\\{\\s*(define[\\('\"\\s]+([a-z_0-9]+)[\"'][^;]+;\\s*|\\/\\/[^\\n]*\\n\\s*)+\\$[a-z_0-9]+\\s*=\\s*<<<['\"]*([a-z_0-9]+)['\"\\s]+.+\\5.+file_put_contents\\([^;]+;[\\s\\}]+($|\\?>)/is"}, {"id": "251", "name": "php curl_init _REQUEST curl_exec eval", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/|\\/\\/[^\\n]*\\n\\s*)*(\\$[a-z_0-9]+)?[\\s=]*curl_init\\(\\$_(REQUES|POS|GE)T[^;]+;\\s*((\\$[a-z_0-9]+)?[\\s=]*curl_[^;]+;\\s*)+(print|die|echo|eval)['\"\\s\\(\\?\\>\\.]+\\6['\"\\s\\);\\}]+\\s*($|\\?>)/i"}, {"id": "252", "name": "php define create_function echo form", "regx": "/<\\?[ph\\s]+((\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/[^\\n]*\\s*|if\\s*\\(\\$[a-z_0-9]+[^\\}]+\\}\\s*)*(define[d\\('\"\\s]+([a-z_0-9]+)[\"'][^;]+[;\\}\\s]+|function\\s+([a-z_0-9]+)\\(.+?return[^;]+;[\\}\\s]+)+)+(\\$[a-z_0-9]+)\\s*=\\s*[^;]*\\$(_REQUEST|_POST|_GET|_COOKIE)[^;]*;\\s*(\\$[a-z_0-9]+)\\s*=\\s*\\6\\([^;]*\\5[^,]+,\\s*\\7*.+?(\\$[a-z_0-9]+)\\s*=\\s*create_function\\([^,]+,\\s*\\9.+?\\10\\([^;]*;[\\}\\s]*(echo|exit)[\\s\\('\"]+<form[^>]*(>\\s*<input[^>]*)+>\\s*<\\/form>['\";\\}\\s]+($|\\?>)/is"}, {"id": "253", "name": "php new wp_http_curl function rms add_action admin_footer function script", "regx": "/<\\?.+?new Wp_Http_Curl\\(\\);(.+?\\/\\*.*?\\*\\/\\s*function\\s+[a-z_0-9]*rms_[a-z_0-9]+){5,}.+?\\/\\*.*?\\*\\/\\s*add_action\\([\"']admin_footer['\"],\\s*function\\(\\)\\s*\\{\\s*\\?>\\s*<script.+?<\\/script>\\s*<\\?[ph\\s\\}\\);]*($|\\?>)/is"}, {"id": "254", "name": "try password file_get_contents catch Exception", "regx": "/(try\\s*\\{\\s*(.+password.+[\\r\\n]+\\s*)*(file_get_contents\\s*\\([^;]*;[\\s\\}]*)+catch[\\(\\s]*Exception\\s*\\$[a-z_0-9]+[\\)\\s]*\\{\\s*(if[\\s\\(]+[^\\{]+\\{\\s*|\\}))+[\\s\\}]*/i"}, {"id": "255", "name": "error_reporting global zeeta if isset function curl_ preg_match fwrite include unlink", "regx": "/(\\@?(ignore_user_abort|set_time_limit|ini_set|error_reporting)\\([^\\)]*\\)[\\s\\)]*;\\s*|\\/\\/[^\\n]*\\n\\s*)*global (\\$[a-z_0-9]+)[^;]*;\\s*if[\\s\\(\\!]+((empty\\(|strlen\\(|isset\\()*(\\$[a-z_0-9]+)[\\)\\s\\&\\=\\!]*isset[\\(\\s]+\\3)[^\\{]+\\{(?=.+?function\\s+([a-z_0-9]+)\\([^\\{]+\\{\\s*[^\\n]*(curl_|file_get_contents))(?=.*?(\\$[a-z_0-9]+)\\s*=\\s*(['\"])\\7).+?(\\$[a-z_0-9]+)\\s*=\\s*\\9\\([^;]+;\\s*preg_match\\([^\\n]+\\11[,\\s]+(\\$[a-z_0-9]+).+?fwrite\\([^\\n]+\\12[^\\n]+;\\s*fclose\\([^\\n]+;\\s*include(_once)?[\\(\\s]+([^\\)]+)[\\);\\s]+((unlink[\\(\\s]+\\14|\\3\\s*=|\\6\\s*=)[^;]*;[\\}\\s]+){3,}/is"}, {"id": "256", "name": "php session_start header _REQUEST function mail curl return", "regx": "/<\\?[ph\\s]*+(?=(?:.*+\\s*+)+?function\\s++(\\w++)\\()(?:(?:(?:ini_set|session_start|header|\\1)\\([^;]++;\\s*+|(?:(?:[els\\s]*+if|isset|foreach)[\\s\\(]++)++\\$_(?:REQUEST|GET|POST|SERVER).[^\\{]++\\{\\s*+)?(?:(\\$\\w++)[\\s\\.]*+=[^;]*+[;\\s]++(?:exit;\\s*+)?|\\/\\/.++\\s++)*+[\\}\\s]*+)++(?:function\\s++\\w++\\([^\\{]++\\{\\s*+(?:(\\$\\w++)[\\s\\.]*+=[^;]++;\\s*+|(?:if[\\s\\(]++|else)++[^\\{]*+[\\{\\s]++|\\}\\s*+)*(?:(?:(\\$\\w++)\\s*+=\\s*+)*+(?:mail|curl_[a-z]++)\\s*+\\([^;]++[\\);\\s\\}]*+(?:(\\$\\w++)[\\s\\.]*+=.++\\s++|return[^;]*+;\\s*+|(?:if[\\s\\(]++|else)++[^\\{]*+[\\{\\s]++|\\}\\s*+|\\/\\/.*+\\s*+)*+)++)++($|\\?>)/i"}, {"id": "257", "name": "php goto Variable Function _REQUEST", "regx": "/<\\?(?:php)?+\\s*+(?:(?:\\#|\\/\\/)[^\\r\\n]*+\\s++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+)*+goto\\s++\\w++;\\s*+\\w++\\:.+?(?:\\$\\w++|\\beval|curl_setopt|copy)\\s*+\\(\\s*+(?:\\w++\\s*+\\(\\s*+)*+(?:(?:['\"\\s]++|\\$[^\\\\]++)\\\\(?:x[a-f\\d]{2}|\\d++)|\\)[\\.\\s]++'[\\-\\+\\w\\/=\\\\]++').+?(?:(?<=\\?>)|\\s*+(?:$|\\?>))(?:\\s*+[\\-\\+\\w\\/=\\\\]{200,}\\s*+$)?+/i"}, {"id": "258", "name": "php if function o_0 var o_0 66+", "regx": "/<\\?[ph\\s]*((\\$[a-z_\\-0-9]+)\\s*=[^;]+;[\\}\\s]*|e(xit|lse)[\\s\\{;\\}]*|if\\s*\\([^\\{]+\\{[^;]+;[\\}\\s]*|([o_0]+)\\s*\\([^;]+;[\\s\\}]*|(date_default_timezone_set|error_reporting|header)\\([^\\)]+\\)+;\\s*)*function\\s+([o_0]+)\\s*\\((.*?\\$[o_0-9\\{\\}]{3,}\\s*=){16,}[^;]+['\"\\);\\}\\s]+(((\\$[a-z_0-9]+)\\s*=\\s*)?f(open|close|write)\\s*\\([^;]+;[\\}\\s]*|return\\s*\\10;[\\s\\}]*)*($|\\?>)/i"}, {"id": "259", "name": "php function if header Location add_action", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/[^\\n]*\\s*)*(function\\s+([a-z_0-9]+)[\\s\\(]+[^\\{]+\\{+\\s*(return (in_array\\(|\\$GLOBALS)+[^;]+;[\\}\\s]+|(\\/\\/[^\\n]*\\n+\\s*|\\$[a-z_0-9]+\\s*=[^;]+;\\s*)+if[\\s\\(]+[^\\{]+\\{+\\s*header[\\s\\('\"]+Location\\:[^;]+;\\s*(e(cho|xit)\\s*\\([^;]+;\\s*|else\\s*\\{\\s*|\\}\\s*)+))+add_action\\([^,]+,[\\s'\"]+\\4[^;]+;[\\}\\s]*($|\\?>)/is"}, {"id": "260", "name": "php file_get_contents file_put_contents mkdir curl scandir chmod new pclzip unlink echo flush", "regx": "/<\\?[ph\\s]+([\\@\\/\\#\\s]*(error_reporting|ini_set|set_time_limit|header)\\s*\\([^\\)]*[\\);\\s]+)*((([;\\s]*(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|(\\$[a-z_0-9]+)\\s*=[^;]+;\\s*|if\\s*\\([^;]*|else[\\s\\{]*))*(\\@*((\\$[a-z_0-9]+)\\s*=\\s*)?(file_get_contents\\s*\\(|file_put_contents\\s*\\(([^,]*),|unlink\\s*\\(|mkdir\\s*\\(|curl_[a-z]+\\s*\\(|die\\s*\\(|(echo|print)[^;]+;\\s*(return|exit))[^\\;]+[;\\}\\s]+)+)+.*(\\$[a-z_0-9]+)\\s*=\\s*(scandir\\s*\\(\\s*\\11.+chmod\\s*\\(\\s*\\11|new pclzip\\s*\\(\\s*\\13.+unlink\\s*\\(\\s*\\13)[^;]+;\\s*)+((\\$[a-z_0-9]+)\\s*=[^;]+;[\\}\\s]*)+(echo|print)['\"\\s*\\(]+\\19[^;]*;[\\}\\s]+([ob_]*flush\\(\\);\\s*)+($|\\?>)/is"}, {"id": "261", "name": "php function dir file scandir _POST", "regx": "/<\\?[ph\\s]*(function (([a-z_0-9]+dir)|([a-z_0-9]+file))\\([^{]+\\{\\s*((echo|[\\}\\s]*else[\\s\\{]*|if\\s*\\(|(\\$[a-z_0-9]+)\\s*=\\s*scandir\\(|foreach[\\s\\(]+\\7|\\3\\(\\$_(REQUEST|GET|POST|SERVER))[^;]+;[\\s\\}]*)+)+($|\\?>)/i"}, {"id": "262", "name": "try if isset SERVER COOKIE curl variable function catch Exception", "regx": "/(?:<(\\?)(?:php)?+|try\\s*+(\\{))\\s*+(?:(?:if|for(?:each)?)\\s*+(?<B>\\((?:[^\\(\\)'\"\\/\\#]++|\\/[^\\*\\/]|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/|(?:\\#|\\/\\/)[^\\n]*+\\s++|\"(?:\\\\\"|[^\"])*+(?<!\\\\)\"|'(?:\\\\'|[^'])*+(?<!\\\\)'|(?&B))*+\\))[\\{\\s]*+|\\$\\w++[\\s\\.\\+]*+=[^;]++;\\s*+|(?:\\{|\\}|else)\\s*+)*?(?:(?:(\\$\\w++)\\s*+=\\s*+)?+\\@*+(?:mail|curl_[a-z]++)\\s*+\\([^;]++[;\\s\\}]++)++(?:(?:[^;]*?\\4[^;]*+[;\\s\\}]++)*?(?:[^;]*?(\\$\\w++)(?:[\\s=]++array\\s*+)?+\\([^;]++[;\\}\\s]++)++(?:(?:(\\$\\w++)\\s*+=\\s*+)?+[^;]*?(?:\\5|\\6)[^;]*+;[\\s\\}]*+)*+)(?:catch[\\s\\(]++Exception\\s++\\$\\w++[\\)\\s]++\\2[^\\}]*+\\}\\s*+|[^\\?]*+(?:\\?(?!>)[^\\?]*+)*+\\1>\\s*+)/i"}, {"id": "263", "name": "php function variable function include variable", "regx": "/<\\?(?:php)?+\\s*+(?:\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++)*+[\\s\\?><ph]*+(?:(\\$\\w++)\\s*+=\\s*+\\$_COOKIE|function\\s*(\\w++)).+?(\\$\\w++)(?:\\s*+\\[[^\\]]*+\\]++)*+[\\s\\.]*+=\\s*+(?:\\1.+?(\\$\\w++)[\\s\\.]*+=\\s*+\\3(?:\\s*+\\[[^\\]]*+\\]++)*+|(['\"]).+?\\5[\\.\\s]++\\2\\(\\$\\w++)\\s*\\(.+?(?:eval|include)(?:_once)?+[\\s\\(\"]++(?:\\3|\\4)[^;]*+;\\s*+(?:\\$\\w++\\s*+=[^;]++;\\s*+)*+(?:$|\\?>\\s*+)/is"}, {"id": "264", "name": "php define class Exception class function new class unset", "regx": "/<\\?[ph\\s]*([if\\(\\s\\!]*define(d\\s*\\([^\\)]+|\\s*\\([^,]+,\\s*([a-z_0-9\\(]+|['\"]))[^\\)]*[\\);\\s\\{\\}]+|\\$[\\{\\$a-z_0-9\\}\\[\\'\"\\]\\s]+=[^;]+;[\\}\\s]*)*class\\s+(_+[a-z]_+[0-9]+)\\s+extends\\s+Exception\\s*\\{(.+?\\}\\s*class\\s+(_+[a-z]_+[0-9]+)\\s+extends\\s+\\4)+.+?\\}\\s*class\\s+(_+[a-z]_+[0-9]+)\\s*\\{.+?function __construct\\s*\\(.+?\\}\\s*function\\s+([a-z][0-9]+)\\s*\\(.+(\\$[a-z_0-9]+)\\s*=\\s*new\\s+\\7[^;]+;\\s*\\9\\-\\>\\8\\([^;]+;\\s*unset\\(\\9\\);\\s*($|\\?>\\s*)/is"}, {"id": "265", "name": "php fopen contactinfo fwrite fclose echo", "regx": "/<\\?[ph\\s]+((\\$[a-z_0-9]+)\\s*=[^;]+;\\s*)+((\\$[a-z_0-9]+)\\s*=\\s*fopen\\(.+?contactinfo[^;]+;\\s*fwrite\\(\\4,\\s*\\2\\s*\\);\\s*fclose\\(\\4[^;]+;\\s*)+(echo\\s*[\"'][^;]+[;\\s]+)+($|\\?>)/i"}, {"id": "266", "name": "if class_exists WPTemplatesOptions foreach wp_get_themes if file_exists include", "regx": "/if[\\s\\(\\!]+class_exists[\\s\\('\"]+WPTemplatesOptions[^\\{]+\\{\\s*foreach[\\s\\(]+wp_get_themes[^\\{]+\\{\\s*((\\$[a-z_0-9]+)\\s*=[^;]*;\\s*)+if[\\s\\(]+file_exists[\\s\\('\"]+\\2[^\\{]+\\{\\s*include_once[\\s\\('\"]+\\2[^;]*;(\\s*\\}){3}/i"}, {"id": "267", "name": "php error_reporting define class class extends new class", "regx": "/<\\?[ph\\s]+(\\/\\/[^\\n]*\\n\\s*|\\/\\*[^\\*]*(\\*[^\\*\\/]*)+\\/\\s*|\\@?(ignore_user_abort|session_start|ini_set|set_time_limit|error_reporting)\\(.*?\\);\\s*|define\\([\\s'\"]+([a-z_0-9]+)['\"][^\\)]+[\\);\\s]+|\\$+[a-z_0-9]+(\\s*\\[[^\\]]*\\])*\\s*[+\\.]*=[^;]+;\\s*)+.+?class\\s+([a-z_0-9]+)\\s*\\{.+(class\\s+([a-z_0-9]+)\\s+extends\\s+\\6)+.+(\\$[a-z_0-9]+)\\s*=\\s*new\\s+\\8[^;]+;\\s*print[^;]*\\9\\-\\>[^;]*;[\\s\\}]*($|\\?>)(\\s*\\#[0-9a-f]+\\s*)?/is"}, {"id": "268", "name": "php define _POST function array_map variable function", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/[^\\n]*\\s*|if\\s*\\([\\!\\s]*|define[d\\('\"\\s]+([a-z_0-9]+)[\"'][^;]+[;\\}\\s]+|(empty|isset)[\\s\\(]+\\$(_REQUEST|_POST|_GET|_COOKIE)[^;]*;\\s*((echo|exit)[^;]*;[\\}\\s]*)+)+function\\s+([a-z_0-9]+)[\\s\\(]+[^\\{]+\\{\\s*((array(_map)?[\\s\\(]+(['\"])?(\\8\\12[,\\s]+)?)*\\$(_REQUEST|_POST|_GET|_COOKIE)(\\s*\\[[^\\]]+\\])*\\s*\\([^;]+;[\\}\\s]+)+($|\\?>)/i"}, {"id": "269", "name": "JS if undefined function Hex new HttpClient Hex EOF", "regx": "/(?<!https:|http:|\"|')(?:^|(?<=;(?=;if[^\\r\\n]++))|[\\r\\n]++|\\/\\/.*?);\\s*+if[\\s\\(]++(?:typeof\\s++)?+\\w++[\\s\\=\"']++undefined['\"\\)\\s\\{]++(?=.+?\\([\"\\s']*+0x[a-f\\d]++['\\*\\s\\)\\|\\&]).+var\\s++(\\w++)[\\s\\=]++new\\s++HttpClient\\([^;]++;\\s*+\\1\\s*+\\[[^\\]]*+[\\]\\s]++\\(.+[\\/\\s\\-\\(]++0x[a-f\\d]++[;\\(\\)\\}\\s]++$/i"}, {"id": "270", "name": "php if define header echo exit function register_shutdown_function variable function session_set_save_handler session_start", "regx": "/<\\?[ph\\s]*(((\\/\\/.+|\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/|\\$[a-z_0-9\\[\\]\\{\\}'\"]+\\s*=[^\\;]+;)\\s*)*((([els\\s]*if)\\s*\\([^\\{]+|else\\s*)\\{\\s*)+((define|header|echo|exit)[^;]*;[\\}\\s]*)+(\\/\\/.+\\n\\s*)*)+function ([a-z_0-9]+)\\s*\\([^\\{]+\\{\\s*\\@?register_shutdown_function\\s*\\(\\s*\\$[a-z_0-9\\[\\]\\{\\}'\"]+\\s*\\([^;]+;[\\}\\s]*(\\$[a-z_0-9]+)\\s*=\\s*function[^\\{]+\\{[^\\}]*[\\};\\s]*\\@?session_set_save_handler\\s*\\([^;]*?\\11[^;]*?\\12[^;]+;[\\}\\s]*\\@?session_start\\s*\\([^;]+;[\\}\\s]*($|\\?>)/i"}, {"id": "271", "name": "php error_reporting long var function create_function array_map set_error_handler variable function trigger_error", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|(ini_set|error_reporting|set_time_limit)\\([^\\)]*\\)+;\\s*|(\\$[a-z_0-9]+)\\s*=[^\\;]+;\\s*|(([els\\s]*if)\\s*\\([^\\{]+|else\\s*)\\{\\s*)+\\}\\s*(\\$[a-z_0-9]+)\\s*=\\s*[a-z_0-9]+\\s*\\([^;]+\\4[^;]+;\\s*(\\$[a-z_0-9]+)\\s*=\\s*['\"][^;]{2000,};\\s*+function\\s*([a-z_0-9]+)\\s*\\([^\\{]+\\{\\s*(\\$[a-z_0-9]+)\\s*=\\s*create_function\\s*\\([^;]*;\\s*((array_map|set_error_handler)\\s*\\(['\"\\s]*(\\10|\\9)[^;]*;[\\}\\s]*)+(\\$[a-z_0-9]+)\\s*=\\s*\\7\\s*\\(\\s*\\8[\\);\\s]+trigger_error\\s*\\(\\s*\\14[^;]+;\\s*(((([els\\s]*if)\\s*\\([^\\{]+|else\\s*)\\{\\s*)+(\\@?(define|error_reporting|ini_set)\\s*\\([^;]+;[\\}\\s]*)+|\\/\\/.+\\n\\s*)+($|\\?>)/i"}, {"id": "272", "name": "php fopen __FILE__ stream_get_contents create_function variable function", "regx": "/<\\?[ph\\s]*(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|error_reporting\\([0\\);\\s]+)*(\\$[a-z_0-9\\[\\]\\{\\}'\"]+)\\s*=\\s*fopen[\\s\\(]+__FILE__[^\\)]+[\\);\\s]+(((\\$[a-z_0-9\\[\\]\\{\\}'\"]+)\\s*=\\s*)?(f[a-z]+|stream_get_contents)\\s*\\(\\s*\\3[^;]+;\\s*)+(\\$[a-z_0-9\\[\\]\\{\\}'\"]+)\\s*=\\s*create_function[\\s\\(]+[^;]+;\\s*\\8\\s*\\(.+/is"}, {"id": "273", "name": "php error_reporting base64_decode _SERVER fsockopen fgets fopen fwrite", "regx": "/<\\?[ph\\s]+error_reporting\\(0\\);\\$[a-z_0-9]+\\s*=\\s*base64_decode[\\(\\s]+'[^']+'[\\)\\s\\.]+\\$_(REQUEST|GET|POST|SERVER)\\[.+?(\\$[a-z_0-9]+)\\s*=\\s*\\@?fsockopen\\(.+?fgets\\(\\s*(\\$_\\1|\\2)(.+fopen\\(.+?fwrite\\(.+?)[^;]*;[\\)\\}\\s]+\\$[a-z_0-9]+\\s*=[^;]*;[\\s\\}]*($|\\?\\>)/i"}, {"id": "274", "name": "var Hex function try Hex catch if else Hex call", "regx": "/(?:(?:function\\s+([a-z_0-9]+)\\s*\\([^\\{]+\\{)?\\s*(?:(?:(?:var|else|try|(?:if|catch)\\s*\\([^\\)]++\\)+)+[\\{\\s]+(?:(?:[\\$a-z_0-9]+(?:\\[['\"](?:\\\\x[a-f0-9]{2})+['\"]\\])*(?:\\s*=+\\s*(?:\\/[^;]++|(?:[a-z_0-9]+\\[+)?(?:(['\"])(?:\\\\x[a-f0-9]{2}|\\s)+\\2[\\]\\[]*)+\\s*(?:\\([^\\)]*+\\)+)?))+)?(?:[;\\}]\\s*)+)+)+)+)+\\1\\s*\\([^;]+;\\s*/i"}, {"id": "275", "name": "var COOKIE Variable Function", "regx": "/\\@*+(\\$\\w++)(?<A>\\s*+\\[[^\\]]*+\\]++)*+\\s*+=\\s*+\\$_(?:REQUEST|GET|POST|COOKIE|SESSION)[^;]*+;\\s*+.*(?<!(?:self|this)(?:\\:\\:|\\-\\>))\\1(?&A)*+\\s*+\\([^;]++[;\\s]*(?:.*\\1[^;]*+[;\\s\\}]+)*+(\\n|$|(?=\\?>))/i"}, {"id": "276", "name": "php var range include var", "regx": "/<\\?[ph\\s]+(\\$[a-z_0-9]+)\\s*=\\s*(range|array|chr)\\([^;]++;[\\s\\@]*include[\\s\\(]+\\1\\[[^;]++;\\s*($|\\?>)/i"}, {"id": "277", "name": "php function array_merge _REQUEST foreach unserialize variable function base64_decode eval exit", "regx": "/<\\?[ph\\s]+(\\@?(\\/\\/[^\\n]*\\n|ini_set\\s*\\(|error_reporting\\s*\\(|set_time_limit\\s*\\(|\\$[\\{\\$a-z_0-9\\}\\[\\\\'\"\\]\\s]+(?:\\(|=\\s*(?:chr\\(|[\"']\\\\[x0-9][0-9a-f]+)))[^;]+;[\\}\\s]*)+function\\s*([a-z_0-9]+)\\s*\\(.+?(\\$[a-z_0-9]+)\\s*=\\s*array_merge[\\(\\s]+\\$_(COOKIE|REQUEST|GET|POST).+?foreach[\\s\\(]+\\4[\\sas]+(\\$[a-z_0-9]+)\\s*=\\>\\s*(\\$[a-z_0-9]+)[\\)\\s\\{]*\\7\\s*=([\\@\\s]*(unserialize|\\3|base64_decode|\\$[a-z_0-9]+)\\s*\\(){4}.+?(eval|\\$[a-z_0-9]+)\\s*\\(.*\\7[^;]*;[\\}\\s]*(\\@?(include|exit|\\$[a-z_0-9]+)\\s*\\([^;]*;[\\}\\s]*)*($|\\?>)/is"}, {"id": "278", "name": "php foreach _REQUEST file_put_contents _REQUEST", "regx": "/<\\?[ph\\s]++foreach[\\s\\(]++\\$_(?:COOKIE|REQUEST|GET|POST)[\\sas]++(\\$[a-z_0-9]++)\\s*+=\\>\\s*+(\\$[a-z_0-9]++).+?file_put_contents[\\s\\(]++[^;]*(?:\\2|\\1)[^;]++;[\\s\\}]*+(?:(?:echo|exit)[^;]*+;[\\s\\}]*+)*+(?:$|\\?>)/is"}, {"id": "279", "name": "php error_reporting set_time_limit if _REQUEST file_get_contents header", "regx": "/<\\?[ph\\s]*(\\$([0-9_a-z]+)\\s*=[^;]+;\\s*)*((\\$([0-9_a-z]+)\\s*=\\s*explode[^,]+,\\s*base64_decode\\([^;]+;\\s*|\\@?(header|ini_set|error_reporting|ob_implicit_flush|set_time_limit|ignore_user_abort|session_start)\\([^\\)]*[\\);\\s]+|((if|isset|empty|foreach)[\\s\\(\\!]+)*\\$(\\2|\\5|_REQUEST|_GET|_POST|_SERVER).[^\\{]+\\{\\s*)+((\\$[a-z_0-9]+)[\\s\\.]*=[a-z_0-9\\s\\(]*[\\$\"f'](\\5|_REQUEST|_GET|_POST|_SERVER|REMOTE_ADDR|http[s]?\\:\\/\\/|ile_get_contents[\\s\\(]+)([^;]*[\\);\\}\\s]+else[\\{\\s]+|echo|print)*[^;]*[;\\s]+(\\s*exit;)?|\\/\\/.+\\n+)*[\\}\\s]*)+((\\$[0-9_a-z]+)[\\s\\.]*=[^;]+;\\s*)*.*(\\$[a-z_0-9]+)\\s*=\\s*file_get_contents\\s*\\([^;]+;[\\);\\s\\}]*(((if[\\(\\s]+|else)[^\\{]*[\\{\\s]+)?((.*\\17|header|chmod|usleep|echo|exit|return)[^;]*[;\\s\\}]+)+)+($|\\?>)/i"}, {"id": "280", "name": "php mkdir base64_decode file_put_contents unlink _SERVER SCRIPT_ unlink", "regx": "/<\\?[ph\\s]*(\\$[a-z_0-9]+\\s*=[^;]+;\\s*)*for[\\s\\(]+[^\\{]++[\\{\\s]*((\\$[a-z_0-9]+)[\\s\\.]*=[^;]+;\\s*)+\\}\\s*mkdir[\\s\\(\"'\\.\\/]+\\3[^\\)]*+[\\);\\s]+(\\$[a-z_0-9]+)\\s*=\\s*base64_decode\\([^\\)]++[\\);\\s]+file_put_contents([\\s\\(\"'\\.\\/]+\\3[^,]*+),\\s*\\4[^\\)]*+[\\);\\s]+.+?unlink\\5[^\\)]*\\);\\s*(\\$[a-z_0-9]+)\\s*=[^;]*?\\$_SERVER[\\s\\[\"']+SCRIPT_[^;]+;\\s*\\@*unlink\\s*\\(\\s*\\6\\s*\\);\\s*.++($|\\?>)/is"}, {"id": "281", "name": "php variable function long_srting function eval variable function", "regx": "/<\\?[ph\\s]*((\\$[a-z_0-9]+\\s*=\\s*function|function\\s+([a-z_0-9]+))\\s*\\([^\\)]*[\\)\\s]+\\{.*?return\\s+\\$[a-z_0-9]+\\s*\\([^\\)]*[\\)\\s]+[;\\}\\s]+)+(\\$[a-z_0-9\\[\\]\\{\\}'\"]+)\\s*=[^\\;]{500,};\\s*(\\$[a-z_0-9]+)\\s*=\\s*function[^\\{]+\\{\\s*eval\\s*\\(.+?\\5[\\s\\(]+\\4[\\s\\);]+($|\\?>)/is"}, {"id": "282", "name": "php error_reporting ini_set var foreach _COOKIE if wordpress_logged_in break echo", "regx": "/(?:(?<=\\<\\/html\\>)\\s*\\?\\>\\s*)?<\\?[ph\\s]+(\\@?(ini_set\\s*\\(|error_reporting\\s*\\(|set_time_limit\\s*\\(|(\\$[\\{\\$a-z_0-9\\}\\['\"\\]\\\\]+)\\s*=)[^;]+;\\s*)+((if|foreach)[\\s\\(]+.*?(wordpress_logged_in|\\$_COOKIE)[^\\{]+\\{\\s*)+((\\3|break)[^;]*+;[\\}\\s]*)+if[\\s\\(]+\\3[^\\{]+\\{\\s*echo[\\s\\(]*(['\"])[^\\9]*\\9[^;]*+;[\\}\\s]*($|\\?>)/i"}, {"id": "283", "name": "php if empty _REQUEST extract if isset var http header Location exit", "regx": "/<\\?[ph]*+(?:\\s++|(?:\\#|\\/\\/)[^\\n]*+\\n|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/)*+if[\\s\\(\\!]++(?:isset|empty)[\\s\\(]++\\$_COOKIE[^\\)]*+[\\)\\s\\{]++header[\\s\\(]++(['\"])(?:Location[\\:\\s]+\\2|[htps\\/0-9\\.]++\\s++404.*?)\\1[\\);\\s]++(?:die[^;]*+|exit);[\\s\\}]+(\\/\\/[^\\n]*+\\n\\s*)*($|\\?>)/i"}, {"id": "284", "name": "if function_exists wp_get_themes foreach aMD5 if file_existss include_once if class_exists break", "regx": "/if[\\s\\(]+([\\!\\&\\s]*(function|class)_exists[\\s\\(]+(['\"])((wp_get_themes)|(a[0-9a-f]{32}))\\3[\\s\\)]+)+\\{\\s*foreach[\\s\\(]+\\5[\\(\\)\\sAS]+\\$a[0-9a-f]{32}[\\s\\=\\>]+\\$a[0-9a-f]{32}[\\s\\)]+\\{\\s*(\\$a[0-9a-f]{32})\\s*=[^;]+;\\s*if[\\s\\(]+file_exists[\\s\\(]+\\7[\\s\\)]+\\{\\s*include_once[\\s\\(]+\\7[^;]*;\\s*if[\\s\\(]+class_exists[\\s\\(]+(['\"])\\6\\8[\\)\\s\\{]+break;[\\s\\}]+/i"}, {"id": "285", "name": "php function return function eval eval function", "regx": "/<\\?[ph\\s]+function\\s+([a-z_0-9]+)\\(\\s*(\\$[a-z_0-9]+)[^\\)]*+[\\)\\s]+\\{+\\s*.+?return\\s+\\2;[;\\}\\s]+.*?function\\s+([a-z_0-9]+)\\(\\s*(\\$[a-z_0-9]+)[^\\)]*+[\\)\\s]+\\{+\\s*\\@?eval[\\(\\s]+\\4[\\);\\}\\s]+.*?\\3[\\(\\s]+\\1[\\(\\s]+[^\\)]*+[\\)\\s]+;\\s*($|\\?>)/is"}, {"id": "286", "name": "function if username_exists wp_create_user set_role administrator add_action filter pre_user_query views_users function return query_where", "regx": "/(?:\\@?(?:ignore_user_abort|session_start|ini_set|set_time_limit|error_reporting)\\s*+\\([^\\)]*+[^;]++;\\s*+)*+function\\s++(\\w++)\\s*+\\([^\\{]++[\\{\\s]++(?:\\$\\w++\\s*+=[^;]++;\\s*+)*+if[\\s\\(\\!]++(?:username_exists|is_user_logged_in)\\([^\\{]+[\\{\\s]++\\$\\w++\\s*+=\\s*+(?:wp_create_user\\((?:[^s]++|s(?!et_role))++set_|get_users[\\(\\['\"\\s]++)role[\\(\\s'\"=>]+administrator[^;]++;[\\s\\}]++(?:add_action\\s*+\\([^,]++,\\s*+(['\"])\\1\\2[\\);\\s]++(?:add_(?:filter|action)\\s*+\\(\\s*+(['\"])(?:pre_user_query|views_users)\\3\\s*+,\\s*+(['\"])(\\w++)\\4[\\);\\s]++function\\s++\\5\\s*+\\(\\s*+(\\$\\w++)[^\\{]++[\\{\\s]++[^\\}]+(?:return\\s++\\6|\\6->query_where[^;]++)[;\\s\\}]++){2}|(?:[^\\}]++[\\}\\s]++)+\\1\\s*+\\([^;](?:;\\s*+wp\\(\\))?[;\\s\\}]++)(?=$|\\?>|\\/\\*)/is"}, {"id": "287", "name": "my var while DATA tag eval var __DATA__ 61", "regx": "/^my\\s+(\\$[a-z_0-9]+)[\\s\\='\"]+;(?=.+?while\\(<([a-z_0-9]+)>\\)).*eval[\\s\\(]+\\1[\\);\\s]++\\_++\\2\\_++\\s+(\\S{61}\\s++)+\\S*+\\s*+$/i"}, {"id": "288", "name": "var function Lots of Unicode Hex", "regx": "/(?:^|(?<=;);)\\s*+(?:var\\s++\\w++\\s*+=[^;]+;\\s*+(?=function)|(?=(?<I>if[\\s\\(]++typeof[^=]++=++[\\s'\"]++undefined[\\s'\"\\)]++)[\\s\\{\\(]++function))++(?=.+?new\\s++XMLHttpRequest)(?=(?:.*?\\\\u[\\dA-F]{4}){257}|(?:.*?0x[\\dA-F]{2}){147})(?:(?:\\((?=[^\\)]++(\\))))?\\s*+)*+(?:function\\s*+(?:(\\w++)\\s*+\\(|(\\())[^\\{]*+(?<B>\\{(?:[^{}'\"\\/\\#]++|(?<S>\\/[^\\*\\/]|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/|(?:\\#|\\/\\/)[^\\n]*+\\s++|\"(?:\\\\\"|[^\"])*+(?<!\\\\)\"|'(?:\\\\'|[^'])*+(?<!\\\\)')|(?&B))*\\})\\s*+(?:\\3\\s*+(?=\\()|(?=\\4))(?<P>\\((?:[^\\(\\)'\"\\/\\#]++|(?&S)|(?&P))*\\))[;\\s]*+\\2?|(?&I)(?&B))[;\\s]*+/i"}, {"id": "289", "name": "Quin foreach function return variable function", "regx": "/<\\?.*?(?<!\\/\\/|#)\\bforeach(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/|[\\@\\s]++)*+\\([^\\)]*?[\\)\\s]*+\\bas\\b[^\\)]++[\\)\\s]++\\{(?&C)*+function(?&C)++\\w++(?&C)*+\\(.++/is"}, {"id": "290", "name": "misplaced style div a tag before body tag", "regx": "/<style[^>]*>\\s*[\\.\\#]([a-z_\\-0-9]+)\\s*\\{[^\\}]*?display:[^\\}]++\\}\\s*<\\/style>\\s*<(div|span)\\s+(class|id)=[\"']\\1['\"][^>]*+>\\s*(<a\\s+[^>]*?href[='\"]++http[^>]++>[^<]++<\\/a>\\s*){69,}<\\/\\2>\\s*(?=\\<[\\/]?body[\\s\\>])/i"}, {"id": "291", "name": "php var curl_init CURLOPT_URL curl_exec echo", "regx": "/<\\?[ph\\s]+(?:\\@?(?:ignore_user_abort|set_time_limit|ini_set|error_reporting)\\([^\\)]*\\)[\\s\\)]*;\\s*|\\/\\/[^\\n]*\\n\\s*)*(?:(\\$[a-z_0-9\\['\"\\]]++)\\s*=[^;]++;\\s*)*(\\$[a-z_0-9]++)\\s*=\\s*curl_init\\(.+?curl_setopt\\([^,]+,\\s*CURLOPT_URL[,\\s]+\\1.+?(\\$[a-z_0-9]++)\\s*=\\s*curl_exec\\(\\2[\\);\\s]++(echo[\\s\\(\"]*\\3[\"\\);\\s]++|curl_close\\(\\2[\\);\\s]++)++($|\\?>)/is"}, {"id": "292", "name": "php fopen fputs fclose include", "regx": "/<\\?[ph\\s]+(?:(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|while\\s*\\([^\\n]++[\\{\\s]++|else[\\s\\{]*|break;\\s*|if\\s*\\([^\\n]++[\\{\\s]++|\\}\\s*|\\/\\/[^\\n]*+\\n\\s*|\\$[a-z_0-9\\['\"\\]]+\\s*=\\s*+(?:'[^']*'|[a-z0-9\\$]).*;\\s*)+(?:(\\$[a-z_0-9]+\\s*=)?[\\s\\@]*(?!\\4)(fopen(?=[\\s\\(]++(\\$[a-z_0-9]+))|fputs|fclose|(?:mk|close)dir|include(?:_once)?(?=[\\s\\(]++\\5.*?\\);\\s*+((?:[\\}\\s]++|\\$[a-z_0-9\\['\"\\]]+\\s*=.*;\\s*)++($|\\?>))?))\\s*\\(.*?\\);\\s*)+)+\\6\\s*$/i"}, {"id": "293", "name": "var function 0x var String 0X var document script", "regx": "/^(?:(?:var\\s++([a-z]++)\\s*+=\\s*+([a-z]++);\\s*+|\\()*+(?:function(?:\\(|\\s++(?:([a-z]++)\\([^,\\)]*+\\)|([a-z]++)\\([^,\\)]*+,))[^\\{]*+(?<B>\\{(?:(?:[^{}]*|(?&B))*)\\}\\s*)(?:\\([a-z]++[,\\s]++(?<X>0x[0-9a-f]++)[\\)\\s]++;\\s*+)?))+var\\s+([a-z]+)\\s*=\\s*'[^']*+'(?<S>[\\+\\s]+String\\[(?<F>\\1\\(['\"\\s]++(?&X)['\"\\)\\s]++)\\]\\((?:(?&X)[,\\s]*+)++[\\)\\s\\+]++(?:'[^']*+'|(?&F)))(?&S)[,\\s;]++([a-z0-9]++)\\s*+=\\s*+(?&F);\\s*+(?:function\\s++\\2\\([^\\{]++(?&B))?if[\\s\\(]++\\3[\\s\\(]++[^\\{]++\\{var\\s++([a-z]++)\\s*+=\\s*+document[,\\s;]++([a-z]++)\\s*+=\\s*+\\11\\[(?&F)\\]\\(['\\s]+s['\\s\\+]*c['\\s\\+]*r['\\s\\+]*i['\\s\\+]*p['\\s\\+]*t['\\s]+[\\);\\s]++(?:(?:\\12|\\11)\\[(?&F)\\][^;]++;\\s*+)+[\\}\\s]+($|(?=\\/\\*))/i"}, {"id": "294", "name": "php function vars return Xor if isset include var", "regx": "/<\\?[ph\\s]*(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+)*+(?<S>(?<V>\\$\\w++)[\\s\\.]*+=(?:[\\s\\.]*+(?:(?&V)|\"(?:[^\"]*+(?<=\\\\)\")*+[^\"]*+(?<!\\\\)\"|'(?:[^']*+(?<=\\\\)')*+[^']*+(?<!\\\\)'))++;\\s*+)++function\\s++(\\w++)[\\s\\(]++((?&V))[,\\s]++((?&V))[^\\{]++[\\{\\s]++(?:((?&V))[\\s\\.]*+=\\s*+\\5;\\s*+|((?&V))[\\s\\.]*+=\\s*+\\6;\\s*+|(?&S))*+(?:(?:((?&V))\\s*+=\\s*+\\w++[\\s\\(]++(?:\\5|\\7)|((?&V))\\s*+=\\s*+\\w++[\\s\\(]++(?:\\6|\\8))[^;]++;\\s*+|(?&S))++(?:((?&V))[\\s\\.]*+=(?:[\\s\\^\\.]*+(?:\\9|\\10)){2}[^;]*+;\\s*+)?(?&S)*+return(?:(?:[\\s\\^]++(?:\\9|\\10)){2}|\\s*+\\11)[^\\}]++\\}\\s*+((?&V))\\s*+=[\\s\\$\\{]*+\\4\\(.*?\\3[^;]++;\\s*+(?:(?:((?&V))[\\s\\.]*+=\\s*+\\12|((?&V))[\\s\\.]*+=\\s*+isset[\\s\\(]++(?:\\12|\\13))[^;]*+;\\s*+|(?&S))*+if[\\s\\(]++(?:\\14|isset[\\s\\(]++(?:\\12|\\13)[\\s\\[]++\\3)[^\\{]++[\\{\\s]++(?&S)*+((?&V))\\s*+=\\s*+(?:\\12|\\13|\\14)[\\s\\[]++\\3[^;]++;\\s*+((?&V))\\s*+=\\s*+\\15[\\s\\[]++\\4\\([^;]++;\\s*+(?:((?&V))[\\s\\.]*+=\\s*+\\16[^;]*+;\\s*+|(?&S))*+include(?:_once)?[\\s\\(]++(?:\\16|\\17)[^;]++;(?:(?&S)|[\\}\\s]++|(?&C))++($|\\?>)/i"}, {"id": "295", "name": "Function createElement getElementsByTagName async src parentNode insertBefore Pass window document script http", "regx": "/^\\s*+;\\s*+\\(function\\([^\\{]*+\\{\\s*+(?:(?:[a-z]++\\s*+=\\s*+)?[a-z]++\\.(?:createElement|getElementsByTagName|async|src|(?:parentNode|\\.?insertBefore)++)\\s*+[=\\(][^;]++;\\s*+){4,}[\\s\\}\\);]++(?:[\\(,\\s]++(?:window|document|['\"]script['\"]|(['\"])http(?:(?!\\1).)++\\1)[\\s\\)]*+){3,};\\s*+/i"}, {"id": "296", "name": "php error_reporting 0 Alone", "regx": "/<\\?[ph]+(?<C>[\\s\\@]+|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/)*+(?:(?:ini_set|error_reporting|set_time_limit)(?&C)*+\\([^,0\\)]*+[,\\s]*+0(?&C)*+\\)+;(?&C)*+){2,}(?:$|\\?>)/i"}, {"id": "297", "name": "Comments in Var Hex Variable Function Include", "regx": "/<\\?(?:php)?(?<C>[\\@\\s]++|(?<T>\\/\\*++\\s*+[\\w]{1,6}\\s*+\\*++\\/))*+(?=(?:(?:\\/[^\\*]|(?&T) *+[\\r\\n]|[^\\/]*+)*+(?&T) *+[^\\r\\n]){40}).*?(\\$\\w++)(?&C)*+=(?&C)*+\\$\\w++(?&C)*+\\(.*?(?:eval|\\$\\w++)(?&C)*+\\([^;]*?\\3[^\\?]*+(?:\\?(?!>)[^\\?]*+)*+(?:$|\\?>\\s*+)/is"}, {"id": "298", "name": "html form php error_reporting if _REQUEST fopen fread fwrite fclose", "regx": "/(?:(?:<(?:(?:\\!DOCTYPE\\s++)?(html)|(body)|(form)|input|br|(?:\\/(?:\\1|\\2|\\3)))[^>]*+>[^<]*+)*+<\\?[ph\\s]++(?:(?:(?:\\@?(?:error_reporting|ignore_user_abort|set_time_limit)\\s*+\\(|\\$\\w++\\s*+=)[^;]++[;\\s]++)*+(?:echo[\\s\\(\"]++(?<G>\\$_(?:SERVER|REQUEST|GET|POST)\\[)[^;]++[\\);\\s]++\\?>\\s*+|\\/\\/.*+\\s++|if[\\(\\s\\!]++(?:(?:isset|empty)[\\(\\s]++)?(?&G)[^\\{;]++[\\s;\\{]++)++)++)++(?:(\\$\\w++)\\s*+=\\s*+(?&G)[^;]++[;\\s]++|(\\$\\w++)\\s*+=\\s*+(?!fopen)[^;]*\\5[^;]*+[\\);\\s]++)++(?:(?:if[\\(\\s]++[^\\{]++[\\{\\s]++(\\$\\w++)|(\\$\\w++))\\s*+=\\s*+fopen\\s*+\\(\\s*+(?!\\9)(\\5|\\6)[^;]++[;\\s\\}]++){2}(?:(?:(?:while|if)[\\(\\s]++[^\\{]++[\\{\\s]++)*+(?:(?:(\\$\\w++)\\s*+=\\s*+)?fread|(?:[^;]++[;\\s\\}]++)?fwrite)[\\(\\s]++(\\7|\\8)\\s*+,\\s*+){2}[^;]++[;\\s\\}]++(?:(?:if[\\(\\s]++(\\7|\\8)[\\s\\)\\{\\s]++)?fclose[\\(\\s]++(\\7|\\8)[^;]*+[;\\s\\}]++){2}\\?>(?:[^<]*+<\\/(?:\\1|\\2|\\3)\\>\\s*+)++/i"}, {"id": "299", "name": "php function if isset include var Function vars return Xor call", "regx": "/(?:<\\?[ph\\s]*(?:\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+)*+|^\\s*+\\*\\/\\s*+)(?:(?<V>\\$\\w++)(?<S>[\\s\\.]*+=(?:[\\s\\.,]*+(?:(?&V)\\[?|\"(?:[^\"]*+(?<=\\\\)\")*+[^\"]*+(?<!\\\\)\"|'(?:[^']*+(?<=\\\\)')*+[^']*+(?<!\\\\)'))++[\\]\\s]*+;[\\s\\}]*+))*+(?:function\\s++(\\w++)[\\s\\(]++[^\\)]*+[\\)\\{\\s]++)?(?:((?&V))\\s*+=\\s*+(?&V);\\s*+|(?&V)(?&S))++((?&V))(?<F>\\s*+=[\\s\\$\\{]*+(?:\\w++\\[(\\w++))\\s*+\\([\\s\"']++[\\w\\%\\+\\-\\.]++[\\s\"',]++\\4[\\)\\}\\]\\s]++;\\s*+)(?:(?:((?&V))[\\s\\.]*+=\\s*+\\5|((?&V))[\\s\\.]*+=\\s*+isset[\\s\\(]++(?:\\5|\\8))[^;]*+;\\s*+|(?&V)(?&S))*+if[\\s\\(]++(?:\\9|isset[\\s\\(]++(?:\\5|\\8)[\\s\\[]++(?&V)[\\]\\s]++)[\\)\\s]++[\\{\\s]++(?:(?&V)(?&S))*+((?&V))(?&F)(?:((?&V))[\\s\\.]*+=\\s*+\\10[^;]*+;\\s*+|(?&V)(?&S))*+include(?:_once)?[\\s\\(]++(?:\\10|\\11)[^;]++[;\\s\\}]++(?:(?&V)(?&S))*+function\\s++\\7[\\s\\(]++((?&V))[,\\s]++((?&V))[^\\{]++[\\{\\s]++(?:((?&V))[\\s\\.]*+=(?:[\\s\\.]*+\"(?:[^\"]*+(?<=\\\\)\")*+[^\"]*+(?<!\\\\)\")++[;\\s]++|((?&V))[\\s\\.]*+=(?:[\\s\\.]*+'(?:[^']*+(?<=\\\\)')*+[^']*+(?<!\\\\)')++[;\\s]++|((?&V))[\\s\\.]*+=\\s*+(?:\\14[\\s\\(]++)?\\13[\\);\\s]++|((?&V))[\\s\\.]*+=\\s*+(?:\\14[\\s\\(]++)?\\12[\\);\\s]++)*+(?:(?:((?&V))\\s*+=\\s*+\\w++[\\s\\(]++(?:\\12|\\15|\\16)|((?&V))\\s*+=\\s*+\\w++[\\s\\(]++(?:\\13|\\17))[^;]++;\\s*+|(?&V)(?&S)|((?&V))[\\s\\.]*+=(?:[\\s\\^\\.]*+(?:\\18|\\19|\\15|\\16|\\17)){2}[^;]*+;\\s*+|((?&V))\\s*+=\\s*+\\w++[\\s\\(]++\\20[^;]++;\\s*+)++return(?:(?:[\\s\\^]++(?:\\18|\\19|\\15|\\16|\\17)){2}|\\s*+\\20)[^\\}]++\\}\\s*+(?:(?&V)(?&S))*+[\\}\\s]*+\\3\\s*+\\([^;]++;\\s*+(?:(?&V)(?&S))*+[\\}\\s]*+(?:\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+)*+($|\\?>)/i"}, {"id": "300", "name": "php var concat 3X Variable Function END", "regx": "/<\\?[ph\\s]*(?:(?<V>\\$[\\{\\w\\\\'\"\\}]++)(?<A>\\s*+\\[[^\\]]*+\\]++)*+[\\s\\.]*+=(?:[\\s\\(\\[\\@\\.\\^,]*+(?<S>(?:array|chr)\\s*+\\([^\\)]*+|(?&V)(?&A)*+|\"(?:\\\\\"|[^\"])*+(?<!\\\\)\"|'(?:\\\\'|[^'])*+(?<!\\\\)')[\\)\\]\\s]*+){3,};\\s*+)+?(?:((?&V))(?&A)*+[\\s\\.]*+=\\s*+(?:(?&S)|\\1(?&A)++)[^;]*+;\\s*+)+.*(\\1|\\4)(?&A)*+\\s*+\\([^;]*+;[\\}\\s]*+(?:(?:(?:else)?if[\\s\\(]++[^\\{]++\\{\\s*+)*+(?:(?&V)\\s*+=|die|eval|exit|echo)[^;]*+;[\\}\\s]*+)*+($|\\?>)/is"}, {"id": "301", "name": "HTML form fwrite rename unlink _REQUEST", "regx": "/(?:<[^\\?\\>]*+>\\s*+)++\\#I\\s++LOVE\\s++YOU\\s++EVERDAY\\#\\s*+<\\/title>(?=.+?unlink\\s*+\\(\\s*+(?:[^\\)]*?(?:\\$(?<R>_(?:REQUES|POS|GE)T)|(\\$(?!(?&R))\\w++)))++)(?=.+?rename\\s*+\\(\\s*+(?:[^\\)]*?(?:\\$(?&R)|(\\$(?!(?&R))\\w++)))++)(?=.+?fopen\\s*+\\(\\s*+(?:[^\\)]*?(?:\\$(?&R)|(\\$(?!(?&R))\\w++)))++)(?:.+?(?:\\2|\\3|\\4)\\s*+=[^;]*?(?&R)){3}.++/is"}, {"id": "302", "name": "function URL return file_get_contents var _POST log _POST pwd if wp_authenticate log pwd if call URL", "regx": "/function\\s++(\\w++)[\\s\\(]++(\\$\\w++)[\\s\\)]++\\{\\s*+.*?(\\$\\w++)[\\s=\\@]++file_get_contents[\\s\\(]++\\2[\\s\\)]++;\\s*+.*?return\\s++\\3[;\\s\\}]++(?:(\\$\\w++)[\\s=]++(?<P>\\$_(?:GE|POS|REQUES)T[\\[\\s]++['\"])log['\"][\\]\\s]++;\\s*+|(\\$\\w++)[\\s=]++(?&P)pwd['\"][\\]\\s]++;\\s*+)++if[\\s\\(]++[^\\{]++\\{\\s*+(\\$\\w++)[\\s=\\@]++wp_authenticate[\\s\\(]++(?:\\4|(?&P)log['\"][\\]\\s]++)[\\s,]++(?:\\6|(?&P)pwd['\"][\\]\\s]++)[\\s\\)]++;\\s*+if[\\s\\(]++[^\\{]++\\{\\s*+(\\$\\w++)[\\s=]++(?:[^;]+?\\4|[^;]+?\\6){2}.*?\\1[\\s\\(]++[^;]*?\\8[^\\)]*+[\\)\\s]++;(?:\\s*+\\}){2}/is"}, {"id": "303", "name": "var languageX3 Cookie expirationDate function return 5795", "regx": "/var\\s++(?:[a-z]*?language[a-z]*+[,\\s]++){3}hasWasCookie[,\\s]++expirationDate;\\s*+\\(function\\(\\)\\s*+(?<B>\\{(?:(?:[^{}'\"]*|\"(?:\\\\\"|[^\"])*+(?<!\\\\)\"|'(?:\\\\'|[^'])*+(?<!\\\\)'|(?&B))*)\\}\\s*)(?<=return 5795\\})\\)\\s*+\\(\\)[;\\s]*+/i"}, {"id": "304", "name": "php if _GET header exit", "regx": "/<\\?[ph\\s]*+(?:(?:if[\\s\\(]++\\$_(?:REQUEST|GET|POST|SERVER)[^\\)]++[\\s\\)]++\\{\\s*+)++\\$\\w++[\\s=]++\\w*?get\\w*+\\s*+\\([^\\)]*?\\$_SERVER\\[[^\\}]*+[;\\}\\s]++)++(?:header[\\s\\(]++[^\\)]++(?:[\\s\\);\\}]++(?:exit[\\(]?)?)++)++($|\\?>)/i"}, {"id": "305", "name": "php X class_exists if NOT X class eval exit C new CLASS C NULL", "regx": "/<\\?[ph\\s]*+(?:\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++|\\@?(?:ini_set|error_reporting|set_time_limit)\\([^\\)]*+\\)++;\\s*+)*+(\\$\\w++)\\s*+=\\s*+class_exists[\\s\\('\"]++(\\w++)['\"\\)\\s]++;\\s*+(?:(\\$\\w++)\\s*+=\\s*+\\1\\s*+;\\s*+)?if[\\s\\(\\!]++(?:\\1|\\3)[\\)\\s\\{]++class\\s++\\2\\s*+\\{.+(?:eval)\\s*+\\([^\\)]++[^;]++[;\\s\\}]++(?:exit\\([^\\)]*+[^;]++[;\\s\\}]++)?(\\$\\w++)\\s*+=\\s*+new\\s++\\2(?:\\:\\:getInstance)?\\([^\\)]*+[\\);\\s]++(?:\\4(?:\\:\\:|->|\\s*+=\\s*+)(?:NULL|[\\w\\{\\}]++\\s*+\\()[^;]*+;[\\s\\}]*+)++(?:$|\\?>)/is"}, {"id": "306", "name": "Isabel ZeroBot log file", "regx": "/(?:<\\?.+?class\\s*+ZEROBOT\\s*+\\{\\s*+(?:public\\s*+\\$\\w++[^;]*+;\\s*+|\\/\\/[^\\r\\n]*+\\s++)*?public\\s*+\\$\\w++\\s*+=\\s*+['\"])?<\\?[ph\\s]*+(?:(?:error_reporting|session_start)\\s*+\\([^\\)]*+\\)++;\\s*+)*+(?:\\$\\w++\\s*+=\\s*+['\"][^;]++;\\s*+)*+(\\$\\w++)\\s*+=\\s*+explode\\s*+\\([^,]++,\\s*+file_get_contents\\s*+\\(\\s*+basename\\s*+\\(\\s*+\\$_SERVER\\s*+\\[[^;]++;\\s*+(?:\\$\\w++\\s*+=\\s*+substr_count\\s*+\\(\\s*+\\1[^\\)]++[^;]++;\\s*+)++\\?>\\s*+<h.++$/is"}, {"id": "307", "name": "script function _0x  var _0x function return V return F", "regx": "/<(script)[^>]*+>\\s*+(?:function|var)\\s++_0x(?=.+?\\b((_0x\\w++)\\s*+=\\s*+atob\\s*+(?:.+?\\3\\s*+)?\\(\\s*+_0x|document[\\[\\s]++_0x\\w++\\s*+[\\[\\(]\\s*+0x)).+?\\bfunction\\s++(_0x\\w++)[\\s\\(\\)]++\\{\\s*+(?:const|var)\\s++(_0x\\w++)\\s*+=\\s*+\\[[^\\]]*+];\\s*+\\4\\s*+=\\s*+function[\\s\\(\\)]++\\{\\s*+return\\s++\\5[\\};]++return\\s++\\4[\\s\\(\\)]++[\\};\\s]++(?:.+\\2[^;]++[;\\}\\(\\)\\s]++)?\\s*+<\\/\\1>/i"}, {"id": "308", "name": "String script echo base64_decode String", "regx": "/(?:(\\$\\w++)\\s*+=\\s*+(['\"])(?<S>PHNjcmlwd[\\w\\/\\-=]++)\\2[^;]*+;\\s*+)?(?:echo|print|die)[\\s\\('\"]++base64_decode[\\s\\('\"]++(?:\\1|(?&S))['\"\\)\\s]*+;/i"}, {"id": "309", "name": "if is_wp_error csrf lines file_put_contents update_option", "regx": "/if[\\s\\(\\!]++is_wp_error\\s*+\\([^\\)]*[\\)\\s]++\\{\\s*+(?:(?:\\$(?:csrf|line)s?[\\[\\]\\s]*+=[^;]++;\\s*+)++\\@?(?:file_put_contents|update_option)\\s*+\\([^\\)]++[\\)\\s]++[^;]*+;\\s*+)++\\}/i"}, {"id": "310", "name": "td_live_css_on_rest_api_init tagDiv Composer Vulnerability", "regx": "/(?<=(?:for now only save\\n|or now only save\\r\\n)\\s\\*\\/)\\s++add_action[\\('\"\\s]++rest_api_init[\"',\\s]++td_live_css_on_rest_api_init[\"'\\)\\s]++;/i"}, {"id": "311", "name": "php function F new Class NULL class call F", "regx": "/<\\?(?:php)?(?<C>\\s++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/)*+(?<V>\\$\\w++(?&C)*+(?:\\[[^\\]]+\\](?&C)*+)*+[\\s\\.]*+=[^;]++;(?&C)*+)*+if\\s*\\([^\\)]++\\)[^\\{]*+\\{\\s*+function(?&C)++(\\w++)(?&C)*+\\([^\\{]++[\\{\\s]++(\\$\\w++)(?&C)*+(?:\\[[^\\]]+\\](?&C)*+)*+[\\s\\.]*+=(?&C)*+new(?&C)*+(\\w++)[^;]++;(?&C)*+\\4(?&C)*+(?:\\[[^\\]]+\\](?&C)*+)*+[\\s\\.]*+=(?&C)*+NULL(?&C)*+;(?&C)*+\\}(?&C)*+(?&V)*+class(?&C)*+\\5(?&C)*+(?<B>\\{(?:(?:[^{}'\"]*|\"(?:\\\\\"|[^\"])*+(?<!\\\\)\"|'(?:\\\\'|[^'])*+(?<!\\\\)'|(?&B))*)\\}\\s*+)\\3(?&C)*+\\([^\\)]*+\\)(?&C)*+;(?&C)*+\\}\\s*+(?:$|\\?>)/is"}, {"id": "312", "name": "php function return function eval Call", "regx": "/<\\?(?:php)?\\s*+(?:(?<F>function\\s++\\w++[\\(\\s]++(\\$\\w++)?[^\\)]*+[^\\{]++\\{\\s*+(?:(\\$\\w++)\\s*+=[^;]++;\\s*+)*+return\\s++(?:(?:\\w++\\s*+\\(\\s*+)*+\\2|\\3)[^;]*+[;\\}\\s]++)++function\\s++(?:(\\w++)[\\(\\s]++[^\\)]*+[^\\{]++\\{\\s*+(?:(\\$\\w++)\\s*+=[^;]++;\\s*+)++(\\w++)\\s*+\\(\\s*+\\5|\\6[\\(\\s]++(\\$\\w++)[^\\)]*+[^\\{]++\\{\\s*+eval[\\s\\(]++\\7|(\\w++)[\\(\\s]++[^\\)]*+[^\\{]++\\{\\s*+(?:(\\$\\w++)\\s*+=[^;]++;\\s*+)++return\\s++\\w++\\s*+\\(\\s*+\\9)\\s*+\\)\\s*+[;\\}\\s]++)++(?&F)*+(?:(?:\\$\\w++\\s*+=\\s*+)?(?:\\4|\\8)\\s*+\\([^\\)]*+[\\);\\}\\s]++)++(?:$|\\?>)/i"}, {"id": "313", "name": "php function A X eval X function B call A call B", "regx": "/<\\?(?=.*?function\\s++(\\w++)\\s*+\\(\\s*+(\\$\\w++)[^\\{]++\\{[^\\}]*?\\beval\\s*+\\(\\s*+\\2).*?function\\s++(\\w++)\\s*+\\([^\\{]++(?=(?<B>\\{(?:[^{}'\"\\/\\#]++|(?<C>\\/[^\\*\\/]|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/\\s*+|(?:\\#|\\/\\/)[^\\n]*+\\s++)|\"(?:\\\\\"|[^\"])*+(?<!\\\\)\"|'(?:\\\\'|[^'])*+(?<!\\\\)'|(?&B))*+\\})(\\s*+(?&C)*+function\\s++(\\w++)))\\{\\s*+(?:(?!\\}\\6)(?:(\\$\\w++)\\s*+=\\s*+\\w++\\s*+\\(\\s*+\\)|(?!\\1))[^;]*+[;\\}\\s]++)++\\b\\1\\s*+\\(\\s*+(?:\\8.+?\\b\\3\\s*+\\(|\\7\\s*+\\([^\\)]*+)[\\)\\s]++;.*+/is"}, {"id": "314", "name": "php plugin WordPress Shell", "regx": "/<\\?(?:php)?\\s*+(?:\\@?(?:ignore_user_abort|session_start|ini_set|set_time_limit|error_reporting)\\s*+\\([^\\)]*+[^;]++;\\s*+)*+\\/\\*[\\*\\s]++Plugin Name: CMSmap - WordPress Shell.++/is"}, {"id": "315", "name": "function echo script function location.href function return fetch function localStorages addEventListener add_action wp_footer", "regx": "/function\\s++(\\w++)\\s*+\\([^\\)]*+[^\\{]++\\{\\s*+\\?>\\s*+<script[^>]*+>\\s*+\\(function\\([\\)\\s\\{]++(?:const\\s++[^;]++;\\s*+)++(?:(?:function\\s++(\\w++)[\\s\\(]++\\w++[\\)\\s]++(?=\\{\\s*+window\\.location\\.href\\s*+=)|function\\s++(\\w++)[\\s\\(]++\\w++[\\)\\s]++|function\\s++(\\w++)\\s*+\\(\\s*+\\)\\s*+(?=\\{\\s*+return\\s++fetch\\()|function\\s++(\\w++)\\s*+\\(\\s*+\\)\\s*+(?=\\{\\s*+localStorage\\.setItem\\()|function\\s++(\\w++)\\s*+\\(\\s*+\\)\\s*+)(?<B>\\{(?:[^{}'\"\\/\\#]++|(?<S>\\/[^\\*\\/]|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/|(?:\\#|\\/\\/)[^\\n]*+\\s++|\"(?:\\\\\"|[^\"])*+(?<!\\\\)\"|'(?:\\\\'|[^'])*+(?<!\\\\)')|(?&B))*\\})\\s*+)++document\\.addEventListener\\([^,]++[,\\s]++function[\\(\\)\\s\\{]++if[\\s\\(]++\\6[\\(\\)\\s\\{]++\\4[^\\{]++\\{\\s*+if[^\\{]++\\{\\s*+const\\s++\\w++\\s*+=\\s*+\\3\\([^;]*+;\\s*+\\5\\([^;]*+;\\s*+\\2\\([^;]*+[;\\s\\)\\}]++\\(\\s*+\\)\\s*+;\\s*+<\\/script>\\s*+<\\?(?:php)?+[\\s\\}]++add_action[\\s\\('\"]++wp_footer['\",\\s]++\\1['\"\\);\\s]++/i"}, {"id": "316", "name": "form type file name php move_uploaded_file _FILES name", "regx": "/^(?=(?:<input(?=[^>]*?type=['\"]file['\"])[^>]*?name=['\"](\\w++)['\"][^>]*+>|<[^>]*+>|[^<]*+)++)(?:<\\/?\\w[^>]*+>|[^<]*+)*+<\\?(?:php)?\\s*+(?:if[\\s\\(\\!]++)\\@?move_uploaded_file\\(\\$_FILES\\[['\"]\\1['\"]\\][^;]++[;\\s\\}]++(?:$|\\?>[^$]*+)/i"}, {"id": "317", "name": "html head body form input script", "regx": "/^(?:<(?:(?:\\!DOCTYPE\\s++)?(html)|head>.*?<\\/head|(body)|(form)|input)[^>]*+>[^<]*+){4,}(?:[^<]*+<\\/(?:\\1|\\2|\\3)\\>){3,}(?:\\s*+<\\!--[^>]*+>|\\s*+<script[^>]*+>.*?<\\/script>){3,}\\s*+$/is"}, {"id": "318", "name": "php if isset _REQUEST switch default die if get_var exit", "regx": "/<\\?(?:php)?+\\s*+if[\\s\\(]++isset[\\s\\(]++\\$_(?<R>REQUES|GE|POS)T\\[(['\"])(\\w++)\\2[^\\{]++\\{\\s*+switch\\s*+\\(\\s*+\\$_(?&R)T\\s*+\\[\\s*+(['\"])\\3\\4\\s*+\\].+?default:[^\\}]++[\\}\\s]++die\\([^\\}]++[\\}\\s]++if[\\s\\(]++\\$wpdb\\->get_var\\(.+?exit;[\\}\\s]++(?:$|\\?>\\s*+)/is"}, {"id": "319", "name": "PHP function A return _COOKIE function B eval A function X return B", "regx": "/<\\?(?=.*?function\\s++(\\w++)\\s*+\\([^\\{]++\\{[^\\}]*?(\\$\\w++)\\s*+=\\s*+\\$_(?:REQUEST|POST|GET|COOKIE)[^\\}]*?(?:(\\$\\w++)\\s*+=\\s*+(?:\\w++\\s*+\\(\\s*+)*+\\2[^;]++[;\\s\\}]++)?\\breturn\\s*+(?:\\2|\\3))(?=.*?function\\s++(\\w++)\\s*+\\(\\s*+(\\$\\w++)[^\\{]++\\{[^\\}]*?(?:(\\$\\w++)\\s*+=\\s*+(?:(\\w++)\\s*+\\(\\s*+)*+\\5[^;]++[;\\s\\}]++)?\\beval\\s*+\\(\\s*+\\6)(?=.*?function\\s++\\7\\s*+\\([^\\{]++\\{[^\\}]*?(\\$\\w++)\\s*+=\\s*+\\1[^\\}]*?(?:(\\$\\w++)\\s*+=\\s*+(?:\\w++\\s*+\\(\\s*+)*+\\8[^;]++[;\\s\\}]++)?\\breturn\\s*+(?:\\9|\\8)).*?\\b\\4\\s*+\\([^\\)]*+\\)++;.++/is"}, {"id": "320", "name": "php function V var is string ONLY then include V", "regx": "/<\\?(?:php)?+(?:\\s*+(?:(?<C>\\@\\s*+|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++)|(?<S>\\$\\w++(?:\\s*+\\[[^\\]]*+\\]++)*+(?:(?&C)|\\s++)*+=(?:(?&C)|\\s++)*+(?:\"(?:[^\"]*+(?<=\\\\)\")*+[^\"]*+(?<!\\\\)\"|'(?:[^']*+(?<=\\\\)')*+[^']*+(?<!\\\\)'|\\d++)(?:(?&C)|\\s++)*+;\\s*+)|(?<F>function(?:(?&C)|\\s++)++\\w++(?:(?&C)|\\s++)*+\\((?:(?&C)|\\s++)*+))++(\\$\\w++)[^\\{]++(?:[^\\}r]*+(?!\\breturn)r)*+[^\\}r]++\\breturn\\s*+\\4;\\s*+\\})++\\s*+(?:(?&C)|(?&S))*+(?&F)(\\$\\w++)[^\\{]++\\{(?:(?:(?&C)|\\s++)*+(?!\\5)(?&S))++(?:(?&C)|\\s++)*+(?:include|require)(?:_once)?+(?:(?&C)|\\s++)*+\\((?:(?&C)|\\s++)*+\\5(?:(?&C)|\\s++)*+\\)(?:(?&C)|\\s++)*+;(?:(?&C)|\\s++)*+}(?:\\s*+.++)*+\\s*+$/i"}, {"id": "321", "name": "COOKIE preg_match function_exists", "regx": "/<\\?[ph\\s]+(function\\s+([a-z_0-9]+)[\\s\\(]+(\\$[a-z_0-9]+)*[\\s\\)\\{]+((\\$[a-z_0-9]+)\\s*=\\s*implode\\([^;]+|(array_[^;]+;\\s*)*srand\\(([a-z_0-9]+)\\(\\)\\));.+?(if[\\s\\(]+isset[\\s\\(]+\\$_COOKIE\\[.+?|return (implode\\([^;]+|\\5);[\\}\\s]+))*(if[\\s\\(\\@]+preg_match\\(.+?if\\[\\s\\(\\@]+preg_match\\(.+?if[\\s\\(\\@]+function_exists\\(.+?(\\s+\\}){3}|(echo|print|die)[\\s\\(]+\\2[\\s\\(]+array[\\s\\(0-9,\\)]+;\\s*|function\\s+\\7\\(.+?return[^;]*;[\\s\\}]+((header|echo)\\([^;]*;\\s*)+)($|\\?>)/is"}, {"id": "322", "name": "script googleblogcontainer eval", "regx": "/<script[^>]+(src=['\"htps:]+\\/\\/(propu|go|bodelen|dolohen)\\.[^\\?]+\\?(zone|id|p|z)+=\\d+['\"\\&][^>]*>|(>\\(function\\(\\)\\{var n,x,e=\\[|id=\"googleblogcontainer\").+eval[\\)\\s]*\\(.+)\\s*<\\/script>\\s*/i"}, {"id": "323", "name": "include php5.php alone", "regx": "/<\\?[ph\\s]+(if\\s*\\(is[^\\)]+[\\)\\s\\{]+|\\s*\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/)*[\\@\\s]*include[\\s\\('\"]+(.+?\\.i(c|\\\\143)o|'php5\\.php)[\"'\\)]+;\\s*(die[^;]*;|\\1)*[\\s\\}]*($|\\?>)/i"}, {"id": "324", "name": "document.write iframe small", "regx": "/(document\\.write\\(['\"])?<iframe src=['\"]http:\\/\\/(.+?)( (height|width)=['\"]?[0-5]['\"]?)+( style=['\"]visibility:[\\t ]*hidden[^>]*><\\/iframe>|><\\/iframe>['\"]\\));*/i"}, {"id": "325", "name": "document.write iframe .php5", "regx": "/<script[^>]*>[\\s\\<\\!\\-]*document\\.write[\\(\\s]+unescape[\\(\\s'\"]+[\\%0-9a-f]+['\"\\)\\s;]+[\\s\\-\\/\\>]*<\\/script>/i"}, {"id": "326", "name": "array eval", "regx": "/<\\?(?:php)?(?<A>.*?(?<C>\\s*+\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/)++.*+\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++|(?:function\\s++(\\w++)[^\\{]++[^\\}]++)?[\\}\\{\\s]++|(?:return|\\$\\w++\\s*+=\\s*+['\\d\"])[^;]*+;\\s*+)*+(?:(\\$[\\{\\$]*+[\\-\\>\\.\\w]++)[\\}\\s\\.]*+=\\s*+(?:(?:array(?:_map)?|\\$\\w++)*+\\s*+\\([^\\)]*+\\)[\\)\\s]*+(?:,[^\\)]*+\\)[\\)\\s]*+)*+);\\s*+)++\\@?eval(?&C)*+\\s*+\\(.*?\\4[^\\)]*+[^;]*+;\\s*+(?:(?&A)++|\\3\\s*+\\([^\\)]*+[^;]*+;\\s*+)*+(?:$|\\?>)/i"}, {"id": "327", "name": "document.write iframe .ru", "regx": "/(document\\.write|echo)[\\(\\s]+['\"]<iframe .+(left\\:\\s*-|.ru\\/).+<\\/iframe>['\"][\\s\\);]+/i"}, {"id": "328", "name": "eval hex", "regx": "/<\\?[ph\\s]*+(?:\\@*+\\$[\\{\"]*+([a-z\\\\_0-9]++)(?:[\"\\}\\s]*\\[[^\\]]*+\\])*[\\}\\s\\.]*=[a-z_0-9\\s\\(]*+[\\$\\{\\s]*+[\"'][^;]*?(?:\\\\(?:x[0-9a-f]{2}|\\d{2,3}))++.*)*eval\\s*\\([a-z_0-9\\s\\(]*+(?:[\\{\\$]+\\1|([\"'])[^;]*?(?:\\\\(?:x[0-9a-f]{2}|\\d{2,3}))++[^;]*\\2)[\\]\\}\\);\\s]++.+($|\\?>)/i"}, {"id": "329", "name": "function_exists emo", "regx": "/<\\?[ph\\s]*(if \\(\\!function_exists\\('emo'\\).+exit;\\}|wp_foots\\(\\);)\\s*\\?>/i"}, {"id": "330", "name": "function_exists base64_decode eval", "regx": "/<\\?[ph\\s]*(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/[^\\n]*\\s*)*(\\$[a-z_0-9]+\\s*=[^;]+[;\\s]+|(\\$[\\$\\{]*[a-z_0-9]+\\}*(\\s*\\[[^\\]]+\\])*)\\s*=\\s*array)*.*?(if\\s*\\(\\s*\\!function_exists\\s*\\(.+[\\)\\s]+[\\{\\s]*)?function\\s*[a-z_0-9]+\\(.+[\\)\\s]+[\\{\\s\\$a-z_0-9\\}\\s\\=\\@]*base64_decode\\s*\\(.+eval\\s*\\(.+[\\);\\s\\}]+($|\\?>)/i"}, {"id": "331", "name": "echo gzinflate base64_decode", "regx": "/(<\\?.*?|\\s*\\#[a-z_\\-=0-9]+\\#|\\s*\\$[a-z_0-9]+\\s*=[^;]+;|\\s*\\@?eval\\s*\\(.*?){2,}[\\s\\@]*+(echo[\\(\\s\\@]+)?gz[ui]n(compress|flate)[\\(\\s\\@]+base64_decode[\\(\\s]+.+[\\)\\s]+;\\s*(\\#\\/[a-z_\\-=0-9]+\\#\\s*|\\?>|$)+/i"}, {"id": "332", "name": "preg_replace /e alone", "regx": "/<\\?[ph\\s]*((\\@?error_reporting\\(|\\(\\$[a-z\\_0-9]+\\s*=\\s*\\$_(REQUES|GE|POS)T\\[)[^\\)]*[\\)\\&;\\s]+)?\\@?preg_replace[\\( \\t]+(['\"])([\\!\\/\\#\\|\\@\\%\\^\\*\\~]).+?\\5[imsx]*e[imsx]*\\4[ \\t]*,[^,]+,[^\\)]+[\\);\\s]*(\\?>|$)/i"}, {"id": "333", "name": "preg_replace /e hex", "regx": "/<\\?(?:php)?\\s*+(?:(?:(?<C>(?:\\#|\\/\\/)[^\\r\\n]*+\\s++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+)|(\\$\\w++)(?:\\s*+\\[[^\\]]*+\\]++)*+[\\s\\.]*+=[^;]++;\\s*+|\\@?(?:error_reporting|set_time_limit|ignore_user_abort)\\s*+\\([^\\)]*+\\)++;\\s*+|function\\s++(\\w++)[^\\{]++[\\{\\s]++.+\\s*+return[^;]*+[;\\s\\}]++|(\\$\\w++)\\s*+=\\s*+(?:\\3|range|array)\\s*+\\((?:[^\\)]*+\\)(?!;))*+[^\\)]*+\\);\\s*+|for(?:each)?[\\s\\(]++[^\\{]*?\\4[^\\{]++[\\s\\{]*+\\2[^\\}]*+[\\s\\}]*+|if[\\s\\(\\!\\@]++extension_loaded[^\\{]++[^\\}]++[\\}\\s;]++|echo[\\s\\('\"]++[^;]++;\\s*+)*+(?:\\$\\w++\\s*+=\\s*+)?\\@?(?:\\$\\w++(?:\\s*+\\[[^\\]]*+\\]++)*+\\s*+\\(\\s*+(?:[\"']{2}|\\$\\w++)*+|preg_replace\\s*+\\(\\s*+(['\"])([\\!\\/\\#\\|\\@\\%\\^\\*\\~]|\\\\[x\\d]{1,3}).*?\\6[imsx]*+(?:e|\\\\x65|\\\\145)[imsx]*+\\5)\\s*+,\\s*+(?:['\"]\\\\x[A-F\\d]{2}[^,]++|['\"]?\\$(?!cb, \\$encoded_value\\[\\$key\\]\\);)(?!repl\\.';)\\w++(?:\\s*+\\[[^\\]]*+\\]++)*+(?:[\\s\\.]*+\\$\\w++(?:\\s*+\\[[^\\]]*+\\]++)*+)*+\\s*+\\()[^\\)]++[^;]++[\\);\\s]*+(?:(?:die|exit)[^;]*+[\\);\\s\\}]*+)*+)++(?:$|\\?>\\s*+)/i"}, {"id": "334", "name": "preg_replace /e str_replace hex", "regx": "/\\@?preg_replace\\s*\\(\\s*['\"].+[\\/\\#\\|][is]*e[is]*['\"]\\s*,\\s*\\@?(\\$_(REQUES|GE|POS)T\\[|str_replace\\((['\"\\s,\\.\\$]*\\\\x[0-9A-F][0-9A-F])+).*\\)[\\s;]*/i"}, {"id": "335", "name": "eval fromCharCode", "regx": "/(<script[^>]*>\\s*(((var\\s*)?[a-z_0-9]+\\s*(;\\s*[a-z_0-9]+\\s*=\\s*[a-z_0-9][\\s\\.]*length|[\\.=]+\\s*([\"']).*?\\6\\s*|[,=]+\\s*\\[[^\\]]*\\]+\\s*)+;\\s*)+for[^\\{]+\\{\\s*[^\\}]+fromCharCode\\([^\\}]+[\\}\\s]+([a-z_0-9]+\\s*=[^;]+;\\s*)*document\\.write\\(|(document\\.write|eval)\\([^;]*fromCharCode\\()[^;]+;\\s*<\\/script>\\s*)+/i"}, {"id": "336", "name": "ini_restore base64_decode", "regx": "/<\\?[ph\\s]+ini_restore\\s*\\(.+\\s+.+base64_decode\\s*\\(.+\\s+.+php\\.ini.+\\s+.+fwrite\\s*\\([\\S\\s]+\\?>/i"}, {"id": "337", "name": "error_reporting variable-function", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|(ini_set|error_reporting|set_time_limit)\\([^\\)]*\\);\\s*)+if[\\(\\s]+\\!defined\\([^\\)]+[\\)\\s]+\\{\\s*define\\([^\\)]+[\\)\\s]+;\\s*if[\\s\\(]+\\!function_exists\\(([\"']).+?\\4\\)[\\)\\s\\{]*function\\s*(.+?)\\([^\\)]+\\)[\\)\\s\\{]+.+?\\5\\([^\\)]+\\);\\s*(\\$[^=\\(;]+)\\([^\\)]*\\)+;\\s*.+\\6\\([^\\)]*\\)+;\\s*return[^;]+;(.*$|\\s*\\?>(.*$)?)/i"}, {"id": "338", "name": "echo script iframe ", "regx": "/\\#[a-z_\\-=0-9]+\\#[\\s\\@]+echo.+<script.+\\.createElement[\\(\\s\"']+iframe.+\\.style\\.(left|top)=['\"\\\\]+-.+<\\/script>.+;\\s+\\#\\/[a-z_\\-=0-9]+\\#/i"}, {"id": "339", "name": "eval _REQUEST", "regx": "/<\\?(?:php)?(?:(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/|\\s++|(?:\\#|\\/\\/)[^\\n]*+\\s++)*+|\\@++)*+(?:ini_set|error_reporting|set_time_limit|ignore_user_abort)(?:(?:.+?\\$_(?<P>REQUEST|GET|POST|SERVER)\\[base64_decode){3}(?:.+?header\\(base64_decode|.+?fwrite\\(.+?exec\\()++|.+?base64_decode\\(.+?\\beval(?&C)*+\\((?&C)*+\\$_(?&P)\\[).++(?:[\\{\\s]*+echo[^;]++;)*+\\s*+(?:$|\\?>)/i"}, {"id": "340", "name": "foreach eval array", "regx": "/(?:<\\?[ph\\s]++|\\/\\*\\s++([\\w\\-]++)\\s*\\*\\/\\s*+)++function\\s++(\\w++)\\(\\s*+(\\$\\w++)[^\\)]*+[\\)\\s]++(?:(?:\\{++\\s*+|(?=function\\s++(\\w++)\\())(?:[^\\n]++\\s++)*?(?:(\\$\\w++)\\s*+=\\s*+(?:base64_decode|\\2)\\s*+\\([^;]++[;\\}\\s]++)?return\\s++(?:\\3|\\5);[;\\}\\s]++)++(?:(\\$\\w++)\\s*+=\\s*+\"[^\"]*+\";[;\\}\\s]*+)*+(\\$\\w++)\\s*+=\\s*+(?:\\2|\\4|\\6)\\s*+\\(\\s*+[^;]++;\\s*+(?:if[\\s\\(]++\\7[\\)\\s\\{]++)?\\@?eval[\\s\\(]++\\7[^;]++['\"\\)\\};\\s]++(?:\\/\\*\\s*+\\/\\1\\s*+\\*\\/|.*?($|\\?>))/i"}, {"id": "341", "name": "excesive spaces in hashed block", "regx": "/\\#\\s*([a-z0-9]+)\\s*\\#[\\s]{50}.+\\s+\\#\\/\\s*\\1\\s*\\#/i"}, {"id": "342", "name": "Javascript obscure eval array", "regx": "/(\\/\\*\\s*([0-9a-f]{32})\\s*\\*\\/\\s*|<(script)[^>]*>\\s*)*var\\s+[a-z_0-9]+\\s*=(\\s*[\\[,]\\s*(['\"])(\\\\?x[0-9a-f]{2})*\\5)+[\\s\\]]+;\\s*document\\s*((\\[[^\\]]+[\\]\\s]+)+(\\(([a-z_0-9]+(\\[[^\\]]+[\\]\\s]+)*)*)+\\)+[;\\s]*)+(\\/\\*[\\s\\/]*\\2\\s*\\*\\/\\s*|<\\/\\3[^>]*>\\s*)*/is"}, {"id": "343", "name": "JavaScript function xViewState", "regx": "/([\\s]{50}<script[^>]*>\\s*eval\\s*\\(\\s*function\\s*\\(|function\\s+[a-z0-9]+ViewState\\(\\))(.+\\s*)+?<\\/script>/i"}, {"id": "344", "name": "add-div-content Via<PERSON>", "regx": "/<\\!--start-add-div-content[0-9]*-->.+Viagra.+Cialis.+<\\!--end-add-div-content[0-9]*-->/i"}, {"id": "345", "name": "javascript array eval", "regx": "/<script[^>]*>[^<]*(((['\"])(\\\\x[a-f0-9]{2})+\\3\\]|['\"\\\\,0-9A-Fx]{200}|(var\\s+([a-z_0-9]+)\\s*=\\s*)?(['\\],\\[\"\\\\x]+[0-9A-F]+){200}.+?(String[\\[\\s]+\\4[\\[\\s\\]0-9]+|eval)\\s*\\()|(eval.+?[0-9\\s\\,]{300})).+?<\\/script>/i"}, {"id": "346", "name": "isset REQUEST eval alone", "regx": "/<\\?[ph\\s]+(\\$[_\\-\\>\\.a-z0-9]+\\s*=\\s*(['\"]).+?\\2;\\s*)*if[\\s\\(]+([a-z_0-9]+\\s*\\(\\s*)*\\$_(REQUES|GE|POS)T\\[.+(system|eval)\\(.+\\s*exit[^;]*[;\\s*\\}]+($|\\?>)/i"}, {"id": "347", "name": "isset HTTP_USER_AGENT header alone", "regx": "/if\\s*\\([^\\{\\n]*\\$_SERVER\\[['\"]HTTP_USER_AGENT['\"]\\][^\\}\\n]+(\\}\\s*else\\s*|\\{\\s*)*\\s*header\\(['\"]Location: .+?;[\\}\\s]+/i"}, {"id": "348", "name": "strrev Assert eval base64", "regx": "/[\\r\\n]++(?:[^\\r\\n\\(]++|\\((?!(?<E>[\"'](\\\\145|e)(\\\\166|v)(\\\\141|a)(\\\\154|l)(\\\\050|\\()(\\\\142|b)(\\\\141|a)(\\\\163|s)(\\\\145|e)(\\\\066|6)(\\\\064|4)(\\\\137|_)(\\\\144|d)(\\\\145|e)(\\\\143|c)(\\\\157|o)(\\\\144|d)(\\\\145|e)(\\\\050|\\())))++\\((?&E).+?\\\\051\\\\051\\\\073[\"\\\\'\\);]++(?<=\\);)/i"}, {"id": "349", "name": "Retry base64_decode Curl", "regx": "/<\\?[\\shp]*\\@?error_reporting\\([\\s0]+\\);\\s*((\\$[a-z_0-9]+\\s*=\\s*)?(urldecode[\\s\\(]+)?\\$_COOKIE\\[[^\\]]+\\]+[\\);\\s]+)+.+mail\\([^\\)]+\\)+[\\s\\{]+post_stats\\((.+?function\\s+(post_stats|_host2int|mch|smtp_lookup|post_mch)){5}.+socket_close\\([^\\)]+\\)+[;\\s\\}]+die\\(\\);[\\s\\}]*($|\\?>)/is"}, {"id": "350", "name": "preg_replace all hex", "regx": "/(\\$[a-z_0-9]+\\s*=)?[\\s\\@]*preg_replace\\s*\\(\\s*['\"](.).*?\\2([^\\)]*?\\\\x[0-9A-F]{2}){13,}.+?\\);/i"}, {"id": "351", "name": "iframe in head", "regx": "/\\<iframe .+\\<\\/iframe\\>\\s*(?=\\<\\/h(3ml|ead)\\>)/i"}, {"id": "352", "name": "Tagged script try document.body eval", "regx": "/<\\!--[a-z_0-9\\s]+-->\\s*<script .+?(bdv_ref_pid=([0-9]+);.+?<\\/script>\\s*<script .+?pid=\\2|try\\{document\\.body.+?eval).+?<\\/script>\\s*(<noscript.+<\\/noscript>\\s*)?<\\!--[\\/a-z_0-9\\s]+-->/i"}, {"id": "353", "name": "Tagged try document.body eval", "regx": "/\\/\\*\\s*([0-9a-f]+)\\s*\\*\\/\\s*.*?try\\{document\\.body.+?eval.+?\\s+\\/\\*[\\s\\/]+\\1\\s*\\*\\//i"}, {"id": "354", "name": "eval variable-function long-nb-string", "regx": "/(?:\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++|(?:\\$[\\w\\[\\]\\{\\}'\"]++\\s*+=|f(?:open|write|close)\\s*+\\()[^;]++;\\s*+)*\\@?(?:eval|assert)\\s*+\\(\\s*+(?:\\$[\\w\\[\\]\\{\\}'\"]++\\s*+\\(\\s*+)++['\"][\\w\\/\\-\\+\\=\\s]{200,}['\"]\\)++;\\s*+/i"}, {"id": "355", "name": "function ob_get_level ob_start add_action", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/[^\\n]*\\n\\s*|\\$[a-z_0-9]+\\s*=[^;]+;\\s*|error_reporting\\([^;]+;\\s*|define\\([^;]+;\\s*)*(((add_action|add_submenu_page)\\s*\\([\\s'\"a-z_0-9]+\\\\x[0-9a-f]{2}[^;]+[\\);\\s\\}]*)+(echo[\\(\\s'\\\\\"a-z_0-9]+\\\\x[0-9a-f]{2}[^;]+[\\);\\s\\}]*)*)+($|\\?>)/i"}, {"id": "356", "name": "head script document.write", "regx": "/(?<=\\<\\/head\\>)\\<script.+?document\\.write\\(.+?\\<\\/script\\>/is"}, {"id": "357", "name": "script http IP", "regx": "/(?<!['\"])(?:(?:<script(?:[^>]*+>\\s*+(?:var\\s++(?:popunder|pm_tag|pm_pid)\\s*+=[^;]++;[\\}\\s]*+)++<\\/script>\\s*+<script)?[^>]*?|importScripts\\s*+(?=(\\()\\s*+(['\"]).+(\\2\\s*+\\)\\s*+;\\s*+)))(?:(?:\\1\\s*+|src=)['\"fhtps\\:]++\\/(?:(?:[\\/|\\.]\\d++){4}|\\/(?:[\\w\\-]++\\.)?(?:[\\w\\-]+?prize\\d*+\\.life|is\\.gd|mockingcard\\.com|onlinekey\\.biz|betamedia\\.biz|lowerthenskyactive\\.ga|js\\.donatelloflowfirstly\\.ga|ofgogoatan\\.com|js\\.digestcolect\\.com|3vwp\\.org|hotopponents\\.site|onclasvr\\.com|pushsar\\.com|scripts\\.trasnaltemyrecords\\.com|examhome\\.net|uustoughtonma\\.org|voipnewswire\\.net|allyouwant\\.online|eeduelements\\.com|cdns\\.ws|adsptp\\.com|json\\.gdn|cloudflare\\.solutions|coin[\\-]?hive\\.com|freehtml5templates\\.com|i7wp\\.org|locationforexpert\\.com|stringengines\\.com|(?:wfcs|xms)\\.lol))\\/|\\/wp-includes\\/js\\/jcrop\\/jquery\\.js|>.*http:\\/\\/mbs-support\\.com\\/js\\/jquery\\.min\\.php.*document\\.write\\([\"']<script.*\\/script)(?:.*?\\3|[^>]*+>\\s*+(?:<\\/script>\\s*+<script[^>]*>\\s*+var\\s++(\\w++)\\s*+=\\s*+new\\s++CoinHive\\.Anonymous[\\('\"\\s]++)?.*?(?:\\s*+\\4\\.start\\(\\);[\\s\\}]*+)?<\\/script>))++/i"}, {"id": "358", "name": "script encode eval", "regx": "/(var\\s*([a-z_0-9]+)\\s*=\\s*function[^\\{]+\\{\\s*)?var\\s*[_\\-\\>\\.a-z0-9]+\\s*=\\s*(String)?\\[\\s*['\"](\\\\x[0-9A-F]{2}|[^\\]]*?fromCharCode)+[\"'][^\\]]*\\].+?(\\{a[0-9]*|\\2|eval)\\s*\\([^\\)]*[\\)\\s\\}]+;*/i"}, {"id": "359", "name": "Tagged base64_decode file_get_contents position iframe", "regx": "/((\\/\\*|\\#)\\s*([a-z_0-9]+\\s*(\\2|\\*\\/))\\s*.+?base64_decode.+?\\s*.+?file_get_contents.+?\\s*.+?position.+?\\s*.+?<\\/iframe>.+\\s*(\\/\\*|\\#)[\\/\\s]*\\3|if\\s*\\([^\\{]*((google|bot|yahoo|bing|HTTP_USER_AGENT)[^\\{]+){5,}\\{((\\$[a-z_0-9]+[\\s\\.\\+]*=\\s*)?(shuffle|array)\\([^;]+;\\s*)*foreach\\([^\\{]+\\{\\s*if\\s*\\(preg_match\\s*\\([^\\{]+\\{\\s*.+?([\\@\\~\\s]*(base64_decode|file_get_contents)\\s*\\(){3}.+?\\s*(\\}\\s*){3})/i"}, {"id": "360", "name": "script ajax POC", "regx": "/<script[^>]+(VBScript.+?CreateObject\\(['\"]Scripting\\.FileSystemObject['\"]\\).+?\\.CreateTextFile\\(.+?\\.Write.+?CreateObject\\(['\"]WScript\\.Shell['\"]\\).+?|ajax.php['\"]>['\"]POC['\"]|>\\s*(\\$[\\=\\~\\[\\]\\{\\}\\(\\)_\\:;\\+'\"\\!\\?\\.,\\|\\/\\\\]*){20,}(?<=\\)\\(\\);))\\s*<\\/script>/is"}, {"id": "361", "name": "targets array JAPluginDone", "regx": "/(\\/\\/files\\s+)?\\$targets\\s*=\\s*array\\(.+?echo[\\s\"']+JAPluginDone[\\s\"';]+/is"}, {"id": "362", "name": "include favicon", "regx": "/(?:\\/\\*\\s*+([a-z_\\-0-9\\=]{5,32}\\s*+\\*\\/)|(?:[\\n\\r]|(?<=;|<\\?php))(;))[\\s\\@]*+include(?:_once)?[\\s\\(]++([\"']).*(?<!\\.php)\\3[\\)\\s]*+(?:\\2|;\\s*+\\/\\*[\\/\\s]*+\\1)/i"}, {"id": "363", "name": "add_filter cred", "regx": "/add_filter\\('template_include','get_cred',1\\);\\s+add_filter\\('shutdown','cred',0\\);/i"}, {"id": "364", "name": "preg_replace strrev e", "regx": "/(\\$|var\\s+)[a-z_0-9\\s\\.]+=\\s*(['\"]e\\/\\*\\.\\/['\"];\\s*preg_replace\\(\\s*strrev\\(.*\\);|[\\[ary]+([\"\\s'\\,]*\\\\(x[0-9a-f]{2}|[0-9]{2,3}))+[\"\\s'\\];]*((\\[*[a-z_0-9]+\\[)+[0-9]+\\]+\\s*\\([^\\)]*\\)+[;\\s]*)+$)/i"}, {"id": "365", "name": "function_exists get file function curl_init file_get_contents fopen curl_exec", "regx": "/<\\?[ph\\s]+((ini_set|\\$[a-z_0-9]+\\s*=)[^;]+;\\s*)*function\\s+([a-z_0-9]+)[^\\{]+[\\s\\{]+(\\$[a-z_0-9]+\\s*=[^;]+;\\s*)*(foreach[^\\{]+[\\s\\{]+(\\$[a-z_0-9]+\\s*(\\[[^\\]]+\\]+\\s*)*=\\s*)?curl_init.+?return[^;]*;|(return\\s+)?curl_[^;]+;\\s*)+[\\}\\s]+((\\$[a-z_0-9]+\\s*=\\s*)?(\\$_SERVER\\[|\\3\\(|fopen\\(|fwrite\\(|fclose\\()[^;]+;\\s*)*(if[\\s\\(]+(file_exists\\(|\\$_(REQUES|GE|POS)T\\[).+?\\3\\(.+){2}/is"}, {"id": "366", "name": "error_reporting include wp-apps", "regx": "/((\\$[a-z_0-9]+[\\s=]+)?(error_reporting|ini_set|getenv|substr)\\([^\\)]*\\)+;\\s*)*\\@*(require|include)(_once)?[\\(\"'\\s]+[^;\\n]+wp-(includes\\/([int]+|(css|js|images)\\/[^;]+)|head|apps|text|admin\\/(css|js|images)\\/[^;]+)+\\.(php|js|css)[\"'][\\s\\)]*;\\s*(\\@(require|include)(_once)?[\\(\\s]+[\"'][^;]+;\\s*)*/i"}, {"id": "367", "name": "require cgi-local php comment alone", "regx": "/<\\?[ph\\s]+(\\/\\*.+?\\*\\/\\s*|\\@)*(require|include)(_once)?[\\(\\s]+(\\$_SERVER[\\[\\{][\"']DOCUMENT_ROOT['\"][\\]\\}][\\s\\.]+[\"'][\\.\\/]*wp-[^;]++|['\"]cgi-local\\/[^;]+?\\.php['\"][\\s\\)]*);\\s+(\\#.*\\s*)*\\?>/i"}, {"id": "368", "name": "ob_start gzinflate ob_get_contents ob_end_clean eval", "regx": "/<\\?[ph\\s]*([if\\(\\s\\!]*define(d\\s*\\([^\\)]+|\\s*\\([^,]+,\\s*([a-z_0-9\\(]+))[^\\)]*[\\);\\s\\{\\}]+)*(\\@|\\$[a-z_0-9]+[\\s\\.]*=\\s*)*ob_start\\s*\\((['\"\\s]+(.*?)['\"\\s]+\\);\\s*function\\s+\\6\\(.+?function\\s+\\3.+return\\s*(['\"])[^\\7]*\\7|gzinflate[\\(\\s]+ob_get_contents[\\(\\);\\s]+ob_end_clean[\\(\\);\\s]+eval\\([^\\)]+[\\)\\s]*);[\\s\\}]*($|\\?>\\s*)/is"}, {"id": "369", "name": "tagged iframe 1px", "regx": "/<\\!-- .+? -->\\s*<iframe width=\"1px\" height=\"1px\" src=\"http:\\/\\/[^>]+>\\s*<\\/iframe>\\s*<\\!-- .+? -->/i"}, {"id": "370", "name": "script after closing body tag", "regx": "/(^|(?<=\\<\\/(body|head)\\>))(\\s*<(script|a[^>]*)>\\s*+(?!function copyText\\()(?!\\/\\/[^<\\n]*\\s*<\\/\\4>).+?<\\/\\4>\\s*)+(?=\\<(body|(\\/|\\!DOCTYPE\\s*)?html))/is"}, {"id": "371", "name": "var R function pYMuS window", "regx": "/<script[^>]*>\\s*(var\\s+)?([a-z_0-9]+)\\s*=\\s*\\[.+?(([a-z_0-9]+)\\s*(\\[[^\\]]+[\\]\\s]*)+=\\s*\\2\\[[^\\]]+[\\]\\s]+\\+.+window[\\[\\s]+\\2[\\[\\s]+[^\\]]+[\\]\\s]*(=\\4|,\\2[^\\]]+[\\]\\s\\)]+)[\\};\\s]+|function pYMuS\\(.+?\\)\\(window\\))<\\/script>/i"}, {"id": "372", "name": "Tagged echo script eval HexHex_", "regx": "/\\#(\\w++)\\#\\s++echo[\\s\\('\"]++<script.+?eval.+?(?:[a-z\\d]{2}\\_){100}.+?<\\/script>['\"\\s\\);]++\\#\\/\\1\\#/is"}, {"id": "373", "name": "variable create_function strrev", "regx": "/<\\?(?:php)?\\s*+(?:(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++)*+(?:(?:(\\$\\w++)\\s*+=)?.*function\\s*+(\\w*+)\\s*+\\([^\\{]++\\{\\s*+(?:[^r]++|r(?!eturn))*+(?:return[^;]*+;\\s*+(?:\\}\\s*+)++)++|class\\s*+(\\w*+)\\s*+\\{\\s*+(?:const\\s++[^;]++;\\s*+)*+)++)++(?:(?&C)|(?:(\\$\\w++)\\s*+=\\s*+)?(?:hex2bin|file_..t_contents|\\4\\s*+(?:::|->)\\w++)\\s*+\\([^;]++;\\s*+)*+(?:eval\\((?:\\3\\s*+\\(|\\5)|base64_decode\\(|\\2\\s*+\\(\\s*+strrev\\().*?(?:\\)\\s*+)++;\\s*+(?:eval\\(.+?(?:\\)\\s*+){2,};\\s*+|\\$\\w++\\s*+=[^;]++;\\s*+|\\1\\s*+)*+(?:$|\\?>\\s*+)/i"}, {"id": "374", "name": "html embed object html", "regx": "/(<(html|head|title)>\\s*)+(hack[3e]d.by[^\\n]*\\s*(<\\/[a-z]+>\\s*)+.+)+<(script|object|embed|iframe).+<\\/(script|object|embed|iframe)>\\s*(<\\/(html|body|center)>\\s*)+/is"}, {"id": "375", "name": "require new SAPE_client return_links", "regx": "/(\\$[a-z_0-9]+)\\s*=\\s*new\\s*SAPE_client\\(.+?\\1->return_links\\(\\);/s"}, {"id": "376", "name": "if function_exists _php_cache_speedup_func_optimizer_ register_shutdown_function ob_end_flush", "regx": "/[;\\s]*if\\s*\\(\\!function_exists\\([' \"]+_php_cache_speedup_func_optimizer_[' \"]+\\)\\).+?register_shutdown_function\\([' \"]+ob_end_flush[' \"]+\\)[;\\s]*\\}/s"}, {"id": "377", "name": "error_reporting ini_set if count POST return", "regx": "/if[\\s\\(\\!]+(\\@*(DEFINE[d]*\\((['\"]).*?\\3|(\\$[a-z_0-9]+)\\s*(\\[[^\\]]*\\]\\s*)*=\\s*(['\"]).*?\\6|error_reporting\\()([\\)\\s\\{]+|[^;]*;[\\s\\}]*)){3,}.+?((\\$[a-z_]+)[0-9]*\\s*(\\[[^\\]]*\\]\\s*)*=\\s*['\"htps\\:]+\\/\\/[^;]*;[\\s\\}]*)+(.+?(file_get_contents|curl_exec|setcookie)\\(\\9[^;]*[;\\s]+){3,}((echo[\\s\\(\"]+\\9|exit)[^;]*;[\\}\\s]*)+(.+\\}[\\s\\}]*\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*|(\\@*(error_reporting|ini_set)\\([^;]+[;\\s]+){2}if[\\s\\(]+[a-z]+[\\s\\(]+\\$_POST.+return \\$[a-z0-9]+[;\\s\\}]+/i"}, {"id": "378", "name": "div <PERSON><PERSON> C<PERSON>is script style.display", "regx": "/<div id=['\"]([^>]*)['\"]>.*Viagra.+Cialis.*<\\/div>[\\r\\n \\t]*<script[^>]*>.*document\\.getElementById\\([\"']\\1[\"']\\)\\.style\\.display.*<\\/script>/i"}, {"id": "379", "name": "php variable array base64_decode function_exists numeric-named function", "regx": "/(\\/\\*([^\\*]*\\*[^\\/])*[^\\*]*\\*\\/\\s*|\\/\\/[^\\n]*\\s+)*\\$[a-z_0-9'\"\\[\\]\\s]+=\\s*array\\(.*?base64_decode\\(.+?\\)+;\\s*((if\\s*\\(\\!function_exists\\([\"']_[0-9]+[\"'][\\)\\s]+\\{\\s*|\\?>\\s*<\\?[ph\\s]*)*function ([a-z_0-9]+)\\(.+?\\}[\\}\\s]+((\\?>\\s*<\\?[ph\\s]*)*\\@?\\$GLOBALS\\s*(\\[[^\\]]*\\]+\\s*|\\{[^\\}]*\\}+\\s*)+\\([^;]+;\\s*)+.+?((\\$[a-z_0-9]+\\s*=\\s*)?(eval|\\$GLOBALS(\\s*\\[[^\\]]*\\]+\\s*|\\{[^\\}]*\\}+)+)\\s*\\(([^;]+)[\\);\\}\\s]+)+)+.+(\\1|\\5\\s*\\([^;]+;[\\);\\}\\s]*)/i"}, {"id": "380", "name": "Tagged if empty script eval echo", "regx": "/(?:\\#(\\w++)\\#\\s*+if[\\s\\(]++empty\\((\\$\\w++)[\\)\\s\\{]++\\2[\\s'\"=]++)?(?<S><script[^>]*?(?:>\\s*+\\{_0x\\w*+\\s*+=\\s*+function\\(_0x\\w*+[\\)\\s\\{]++return\\s++_0x\\w*+\\.toString\\(|>\\s*+eval[\\(\\s]++function\\(|src=[\\\\'\"]++http[s]?\\:\\/\\/\\w++\\.transandfiestas\\.ga\\/\\w++\\.js\\?\\w++=).+\\s*<\\/script>)[\\s'\";]*+(?:echo\\s*+\\2[\\s;\\}]++\\#\\/\\1\\#|(?&S)++|$)/i"}, {"id": "381", "name": "var HTTP_USER_AGENT if match string var else", "regx": "/(\\$\\w++)\\s*+=\\s*+\\$_SERVER\\[[\"']HTTP_USER_AGENT['\"]\\];\\s*+(?:.++\\s*+)*?if\\s*+\\([\\$\\w\\s]+\\([^\\)]*?\\1[^\\{]++\\{\\s*+(?:(?:header|exit|die|(\\$\\w++)\\s*+=[^;]++;\\s*+curl_\\w++\\s*+\\([^\\)]*?\\2[^\\)]*+|curl_\\w++\\s*+\\([^\\)]++)[^;]*+;\\s*+)++(?:\\}\\s*+else\\s*+\\{[^\\}]*+)?\\}/i"}, {"id": "382", "name": "div php error_reporting fopen http", "regx": "/<div [^>]*>\\s*<\\?[ph\\s]+error_reporting\\(.+?fopen\\([\"']http:\\/\\/.+?\\?>\\s*<\\/div>/is"}, {"id": "383", "name": "DOCUMENT_ROOT if file_exists file_get_contents gzinflate preg_replace", "regx": "/(\\$([a-z_0-9]+)\\s*=[^;]*;\\s*|echo[^;]*;\\s*|\\@*([a-z_0-9]+)\\s*\\(\\s*)+\\$_SERVER[\\s\\[\\{]+([\"'])(DOCUMENT_ROOT|SCRIPT_NAME)\\4[\\s\\]\\}]+.+?(function\\s+\\3\\s*\\([^\\{]+\\{\\s*|if[\\s\\(]+file_exists\\s*\\(.+?)\\$([a-z_0-9]+)\\s*=[\\s\\@]*(file_get_contents\\s*\\(\\s*\\$\\2.+?\\$([a-z_0-9]+)\\s*=[\\s\\@]*gzinflate\\s*\\(\\s*\\$\\7.+?preg_replace.+?\\);[\\}\\s]+|scandir\\s*\\([^;]+;\\s*foreach\\s*\\(\\$\\7.+(fwrite\\s*\\([^;]+;\\s*fclose|file_put_contents)\\s*\\([^;]+(;[\\}\\s]+(unlink\\s*\\(\\s*\\$\\2\\)|(\\$[a-z_0-9]+\\s*=[^;]*;\\s*)*(if[\\s\\(]+[^\\{]+\\{\\s*)*echo[^;]*))+;\\s*)/is"}, {"id": "384", "name": "function fourofour add_filter all_plugins fourofour_pp", "regx": "/<\\?(?=.+?add_filter[\\s\\(]++[\"']all_plugins[,\\s\"']++(?!eos_dp_plugins_in_list|wpematico_showhide_addons)([a-z_0-9]++)[\"']).+function\\s++\\1[\\s\\(]++(\\$[a-z_0-9]++).+?unset[\\s\\(]++\\2.+?return[\\s\\(]++\\2.+/is"}, {"id": "385", "name": "p payday loans", "regx": "/<p[^>]*>\\s*.+?payday loan.+?[\\r\\n]+\\s*<\\/p>/i"}, {"id": "386", "name": "script src earnmoneydo.com", "regx": "/(<(script|a)[^>]+(href=['\"][fhtpsl:]*\\/\\/(secure\\.payza)[^>]+>\\s*<img[^>]+)?src=['\"][fhtpsl:]*\\/\\/(\\4|stat\\.uustoughtonma|cdn\\.scriptsplatform||cdn\\.allyouwant|cdn\\.eeduelements|online-sale24|earnmoneydo|gccanada|g00)\\.[co].+?\\s*<\\/\\2>\\s*)+/i"}, {"id": "387", "name": "php var array var text if function_exists function foreach chr return variable function text", "regx": "/<\\?[ph\\s]++(?:\\/\\/[^\\n]*\\n\\s*|\\/\\*[^\\*]*(?:\\*[^\\*\\/]*)+\\/\\s*|(?:\\$[a-z_0-9]++(?:\\s*\\[[^\\]]++\\]++)*+[\\s\\.\\+\\-]*+=\\s*(?:array\\(|\\$\\{)?(?:(['\"]).*?\\1|[0-9\\,]+|null|[^;]{4321,};|create_function\\([^\\)]++|\\$[a-z_0-9]+(\\s*\\[[^\\]]+\\]+|\\s*\\(\\s*)*)['\\.\\,\\s\\)\\}]*)+;\\s*+|\\@*(error_reporting|ignore_user_abort|set_time_limit|header)\\s*\\([^\\)]*+[\\)\\s]++[^;]*+;\\s*|(?:if\\s*+\\([^\\{]++\\{\\s*+)?function[^\\{]++\\{.*?return[^;]*+;['\"\\);\\s\\}]++)+((\\$[a-z_0-9]++(\\s*+\\[[^\\]]++\\]++)*+[\\s\\.\\+\\-]*+=\\s*+)?\\$[a-z_0-9]++(\\s*+\\[[^\\]]++\\]++)*+\\s*+\\([^\\)]*+[\\)\\s]++(?!->)[^;]*+;[\\s\\}]*+)++((if\\s*+\\([^\\{]++\\{\\s*+)?(echo|print|die|for(each)?\\s*+\\([^\\{]++)[^;]*+;[\\};\\s]++)*+($|\\?>)/is"}, {"id": "388", "name": "Tagged error_reporting base64_decode", "regx": "/(\\/\\*.+?\\*\\/|<\\!--.+?-->)\\s*(if[\\( \\!]+defined\\([^\\)]+[\\) \\{]+.*?define\\([^\\)]+\\)+;[\\s\\}]*)*((\\@|\\$[a-z_0-9]+\\s*[\\.=]+)*(error_reporting|ini_set|ob_start)\\(.+?)+base64_decode\\(.+?\\1/is"}, {"id": "389", "name": "Tagged createElement script src appendChild", "regx": "/((<script[^>]*>|\\/\\*\\s*[0-9a-z]+\\s*\\*\\/|Element\\.prototype\\.appendAfter\\s*=\\s*function.*function[\\(\\)\\s]*\\{\\s*|var)\\s*)+([a-z_0-9]+)[\\s=]+document\\.createElement[\\(\\s]+(String\\.fromCharCode\\(.+?\\3\\.src[\\s=]+|['\"]script['\"].+?\\3\\.src[\\s=]+String\\.fromCharCode\\([^\\)]+\\)+;\\s*|var\\s*([a-z_0-9]+)[\\s=]+document\\.getElementsByTagName\\(['\"]script['\"]\\);\\s*var\\s*([a-z_0-9]+)[\\s=]+true;\\s*((for[\\(\\s]+(var\\s*)?([a-z_0-9]+)[\\s=]+\\5[^\\{]+\\{\\s*|if[\\(\\s]+|[\\}\\s]*else\\s*(\\{[^\\}]*[\\}\\s]+)?)*(\\5\\[\\10\\]\\.src|\\3\\.src|\\6)[\\s=]+(\\5\\[\\10\\]\\.src|\\3\\.src|true|false)[\\)\\s\\{;]+)+)+.+?\\.appendChild\\(\\3\\)[^;]*;\\s*([\\}\\(\\);]+\\s*|\\/\\*\\s*[0-9a-z]+\\s*\\*\\/\\s*|$|<\\/script>)+/i"}, {"id": "390", "name": "PHP Vars Concat Variable Function END", "regx": "/<\\?[ph\\s]+(?:(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|^\\s*+\\*\\/\\s*+)|\\@?error_reporting[\\(0\\)\\s]++;\\s*+|function\\s++\\w++\\([^\\{]++[\\{\\s]++)*+(?:\\$(?<V>\\w++(?:\\s*+\\[[^\\]]++\\]++)*+)[\\s\\.]*+=\\s*+(?:(['\"]).*?\\3|Array\\s*+\\(|\\$(?!this)(?&V))[^;]*+;\\s*+|if[\\s\\(]+isset[\\s\\(]++\\$_[^\\{]++\\{\\s*+)+?(?:echo|(?:\\$(?&V)\\s*+=)?[\\@\\s]*+(?:\\$(?&V)|str_replace|create_function)\\s*+\\([^\\)]*+).*?((\\$(?&V)\\s*+=)?[\\@\\s]*+(?<!\")\\$(?&V)\\s*+\\((?:.+;|[^;]*+;)\\s*+)+(?:(?&C)|exit;\\s*+|\\}\\s*+)*+(?:$|\\?>(?:\\s*<form[^>]*+(?:>\\s*+<input[^>]*+)++>\\s*+<\\/form>)?)/i"}, {"id": "391", "name": "div script document getElementById visibility hidden display none", "regx": "/<div id=['\"]([a-z\\_0-9]+)['\"].+?<\\/div>\\s*<script[^>]*>\\s*((function\\s(?!hidemessage)([a-z\\_0-9]+)|if)[\\s\\(]+[^\\)]*[\\)\\s\\{]*)?(document\\.getElementById\\([\"']\\1[\"']\\)\\.style\\.(visibility|display)\\s*=\\s*[\"'](hidden|none)[\"'];\\s*)+[\\s\\}]*<\\/script>/i"}, {"id": "392", "name": "add_action wp_footer serve example_admin_notice", "regx": "/(add_action\\(\\s*['\"](wp_footer|init|admin_notices)['\"][,\\s]+(\\@?create_function[\\s\\(]+)?['\"](.+?base64_decode.+?|example_admin_notice|serve)['\"][\\s\\)]+;\\s*){2,}/i"}, {"id": "393", "name": "PHP error_reporting if !isset variable function END", "regx": "/(?:(?:error_reporting\\s*+\\(|(\\$\\w++)(?:\\s*+\\[[^\\]]*+\\]++)*+\\s*+=)[^;]+;\\s*+)*+if\\s*+\\(.+?\\)+\\s*+\\{\\s*+(?:(\\$\\w++)(?:\\s*+\\[[^\\]]*+\\]++)*+\\s*+=[^;]++;\\s*+)+?(?:(\\$\\w++)(?:\\s*+\\[[^\\]]*+\\]++)*+\\s*+=[^;]++;\\s*+)*?(?:(\\$\\w++)(?:\\s*+\\[[^\\]]*+\\]++)*+\\s*+=++\\s*+)?(?:\\1|\\2|\\3)(?:\\s*+\\[[^\\]]*+\\]++)*+\\s*+\\([^;]*?(?:(?:(\\$\\w++)(?:\\s*+\\[[^\\]]*+\\]++)*+\\s*+=\\s*+|;\\s*+)*?\\4(?:\\s*+\\[[^\\]]*+\\]++)*+\\s*+\\([^\\)]*+\\)[^;]*+|;\\s*+(?:require|include)(?:_once)?[\\s\\(\"]++(?:\\1|\\2|\\3|\\4|\\5)[^;]*+)++;\\s*+\\}\\s*?(?:\\n|$)/i"}, {"id": "394", "name": "script if navigator userAgent match document write script src http", "regx": "/<script[^>]*>((\\/\\*\\s*[0-9a-z]+\\s*\\*\\/|var|const|\\s)*((_0x[a-z_0-9]+)[\\s=]+(\\[[^\\]]+[\\],\\s]+)+;[\\}\\s]*)+(((_0x[a-z_0-9]+)?\\(\\s*|(var\\s*)?\\s*(_0x[a-z_0-9]+)[\\s=]*function[^\\{]+\\{+\\s*)+[^;]*[;\\s\\}]+)+(var\\s*)?\\s*\\8[\\s=]+\\4.+\\10[^;]+[;\\s\\}\\(this\\)]+|if[\\s\\(]+navigator\\.userAgent\\.match\\(.+?\\{\\s*document\\.write\\([\"']<scr.+? src=['\"]http.+?\\)[;\\}\\s]+)<\\/script>/i"}, {"id": "395", "name": "php function Array return base64_decode php Variable function", "regx": "/^<\\?[ph\\s]+function base64[^\\{]+\\{\\s*return base64[^\\}]+\\}\\s*if[\\s\\(]+isset[\\s\\(]+\\$_(REQUES|GE|POS)T.+<body onload=[\\s'\"\\\\]+location[^>]+[^<]+<\\/body>\\s*<\\/html>\\s*<\\?[ph\\s]*\\}+\\s*($|\\?>)/is"}, {"id": "396", "name": "include_once rss-info.php", "regx": "/((\\$[a-z_0-9]+)\\s*=.+?[a-z_0-9]\\.(js|png|gif)['\"];\\s*)*(if[\\s\\(]+(is_|file|_exists){2,}[\\(\\s]+[^\\)]+([\\)\\s]+\\.[^\\)]+)*[\\)\\s\\{]+)?\\@?(include|require)(_once)?\\s*\\((dirname\\(__FILE|ABSPATH)?[_\\)\\.'\"\\/\\s]*(\\2.*?|((wp-content\\/uploads\\/.+?|wp-vcd|wp-content\\/plugins\\/contact-form-7\\/contact-form-7-sample-options)|class\\.plugin-modules|rss-info)\\.php['\"])[\\);\\s\\}]+(else)?/i"}, {"id": "397", "name": "is_bot __via_content)", "regx": "/(var\\s+([a-z_0-9]+)\\s*=[^;]+;\\s*)*(function\\s+([^\\(]*cookie[^\\(]*|[a-z0-9]{41})\\([^\\{]*\\{.*?((document.cookie[^;]+(;\\s*['\"])?|return|if\\s*\\(document\\.referrer[^\\{]+\\{\\s*\\2\\s*=)[^;]*;\\s*(\\}\\s*)+)+){5,}(\\(*function\\s*([\\(\\)]+|[a-z0-9]{41}\\([^\\{]*)\\s*\\{.*?\\);\\s*\\}[\\)\\s]*\\10\\s*;\\s*){2,}/is"}, {"id": "398", "name": "set var str_replace var variable function", "regx": "/((\\$[a-z\\_0-9]+(\\s*\\[[^\\]]+\\])*)\\s*=\\s*\\@?\\$[a-z\\_0-9]+(\\s*\\[[^\\]]+\\]+)*\\s*\\(\\@?\\$_(REQUEST|GET|POST|COOKIE)(\\s*\\[[^\\]]+\\]+)*\\);\\s*)+(\\$[a-z\\_0-9]+|preg_replace)\\s*\\(\\s*(['\"])([\\!\\/\\#\\|\\@\\%\\^\\*\\~]).+?\\9[imsx]*e[imsx]*\\8\\s*,\\s*\\2\\s*,(([\\$a-z\\_0-9]+(\\s*\\[[^\\]]+\\])*|'[^']*'|\"[^\"]*\")[\\.\\s]*)+\\)+;\\s*(die\\([^\\)]*\\)+;)?/i"}, {"id": "399", "name": "Tagged error_reporting curl_init file_get_contents fwrite script", "regx": "/<\\?[ph\\s]+([\\@\\/\\#\\s]*(error_reporting|ini_set|set_time_limit|header)\\s*\\([^\\)]*[\\);\\s]+)*((([;\\s]*(\\$[a-z_0-9]+\\s*=[^;]+;\\s*|else[\\s\\{]*))*if\\s*\\([^;]+)+((file_get_contents\\s*\\(|mkdir\\s*\\(|curl_[a-z]+\\s*\\(|die\\s*\\(|(echo|print)[^;]+;\\s*(return|exit))[^\\}]+[\\}\\s]*)+)+(else[\\s\\{]*)?(if[\\s\\(]+[^\\)]+[\\)\\s\\{]*)?(\\$[a-z_0-9]+\\s*=\\s*file_get_contents\\([^;]+;[\\}\\s]*)*((if\\([^\\)]+|echo[^;]+)[\\)\\{\\s;]+)*(\\/*(\\$[a-z_0-9]+\\s*=)?[\\@\\s]*(fopen|fwrite|fclose)\\s*\\([^;]+;[\\}\\s]*){3,}((header|echo|print|if\\s*\\([^\\{]+[\\{\\s]*chmod\\s*\\()[^;]*;[\\}\\s]+)+($|\\?>)/i"}, {"id": "400", "name": "file_exists curl_init file_get_contents file_put_contents include_once", "regx": "/<\\?[ph\\s]+(\\$[a-z_0-9]+)\\s*=\\s*.+?(curl_init|file_get_contents\\([\\s'\"]+http[s\\:\\/]+).+?file_put_contents\\(\\1.+?(include_once|(\\$[a-z_0-9]+)\\s*=\\s*new\\s+[a-z_0-9]+)\\(\\1.*?\\);\\s*(.*\\4[^;]*;\\s*|\\}\\s*|else\\s*|\\{\\s*|die[\\s\\('\"]+[^\\)]*\\)+;\\s*)*($|\\?>)/is"}, {"id": "401", "name": "long string var eval variable function", "regx": "/<(\\?|script language=)[ph\\s'\">]+(\\$[a-z_0-9]+(\\s*\\[[^\\]]+\\]+)*[\\s\\.]*=\\s*(\"([^\"]*(?<=\\\\)\")*[^\"]*\"|'([^']*(?<=\\\\)')*[^']*'|\\d+|(\\$[a-z_0-9]+[\\[\\]\\d\\.]*)+);\\s*|\\/\\/.*\\s*|\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*\\s*|\\@?error_reporting[\\(0\\)\\s]+;\\s*|for(each)?\\s*\\(([^\\{]+\\{[^\\}]+\\}+[\\}\\s]+|([^;]*;\\s*){3})|\\?>\\s*<\\?[ph\\s]+)+eval\\s*\\(((\\$[\\$\\{]*[a-z_0-9]+)[\\}\\s\\)]*(\\[[^\\]]+\\]+[\\.\\s\\}]*|=+\\s*(\\$[a-z_0-9]+|create_function))*[\\)\\s\\{;\\}]*)+(\\(.*?\\)[\\)\\s\\};]+)+(([\\$\\{]*(\\15|\\13|\\4)[\\s\\}]*\\(|echo)[^;]+;[\\}\\s]*|\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*(\\?>(.+$)?|$|<\\/script>)/i"}, {"id": "402", "name": "php var explode numbers Variable function", "regx": "/<\\?[ph\\s]+(if\\s*\\(([^\\{]+[\\{\\s]*\\$GLOBALS[\\{\\[]['\"]\\\\x[^;]+;[\\}\\s]*)+(\\?>\\s*<\\?[ph\\s]+))?\\$[a-z_0-9]+\\s*=\\s*('.+?'|\".+?\");\\s*.*?\\s*\\$[a-z_0-9]+\\s*=\\s*explode\\([^,]+[,\"'\\.0-9\\s]+\\);\\s*.*?\\$[a-z_0-9]+\\s*\\(.*?\\?>(.+$)?/i"}, {"id": "403", "name": "function X if function_exists curl_init spamcheckr.com curl_exec curl_close echo add_action X", "regx": "/(?:\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\/\\/|\\#)[^\\r\\n]*+\\s++|\\?>\\s*+<\\?(?:php)?\\s*+)*+if[\\s\\(\\!]+function_exists\\(\\s*['\"](\\w++|(?<X>(?:\\\\(?:x[a-f\\d]{2}|\\d++))+))['\"][\\s\\)]++\\{\\s*+(?:(?:function\\s+(\\w++)\\()?.+?(?:spamcheckr\\.com|javaterm1\\.pw|['\"]jquery\\.|karanbit\\.com|function_exists[\\(\\s]++['\"](?&X)['\"][\\s\\)]+\\{).+?curl_init.+?curl_exec.+?curl_close|function\\s+\\1\\s*\\([^\\)]*[\\)\\s]+\\{\\s*(?:\\$\\w++\\s*+=[^;]++[;\\s]+)*(?:if[\\s\\(]++\\@?(?:\\$|fopen[\\s\\(]++)[^\\{]++[\\{\\s]*+)?echo\\(wp_remote_retrieve_body\\(wp_remote_get\\()(?:.*?(\\$\\w++)\\s*+=\\s*+\\3[\\(\\s'\"]++(?&X)['\"])?[^;]++(?:;[\\}\\s]*+echo[^;]++)*+;[\\}\\s]*+(?:(?:(?:if[\\s\\(]++|else)[^\\{]*+[\\s\\{]++(?=[^\\}]*+(\\})))?(?:add_action[^\\,]++\\,\\s*+['\"](?:\\1|\\3|wordpress_api_debug)|eval[\\s\\(]++\\4\\[['\"](?&X))['\"][^;]*+[\\s\\);]++)\\5?\\s*+\\}\\s*+(?:\\?>\\s*+<\\?(?:php)?)?(?:\\s*\\/\\*[^\\n]*?\\*\\/|\\s*\\#[^\\n]*\\n)?/is"}, {"id": "404", "name": "if function_exists function error_reporting Variable xFF H* if file_exists error_reporting endif", "regx": "/if[ \\(\\!]+function_exists\\([ '\"]+(.+?)[ '\"]+[\\) \\t]+\\:.+?function \\1\\(\\) \\{.+?error_reporting\\(0.+?(\\$([a-z0-9\\_]+)[ =\\t]+\"(\\\\x[0-f]{2})+\";[\\t \\r\\n]+(\\$([a-z0-9\\_]+)[ =\\t]+\\$([a-z0-9\\_]+)\\(\"H\\*\"\\,.+?;[\\t \\r\\n]+)+)+if[ \\(\\!]+file_exists.+?error_reporting\\(\\$.+?endif;/is"}, {"id": "405", "name": "include ImageFile", "regx": "/(?<!\\/\\/\\s{8})\\@?(?:include|require)(?:_once)?[\\(\\s]++[a-z_0-9,\\.'\\s\"\\/\\-\\(\\)]+?(?<!GD_SYSTEM_PLUGIN_DIR \\. '\\/images\\/404)(?<!get_template_directory\\(\\) . '\\/changes)(\\.(?:gif|jpg|png|txt|cbu|[\\s\"']+\\/wp-includes\\/init\\.php|[\\s\"']+\\/wp-includes\\/js\\/utilities\\.js|[\\s\"']+\\/wp-admin\\/includes\\/class-wp-iternal-upgrade\\.php)|wp-java\\.php|notification\\.inc\\.php)[\"'\\s\\)]+;/i"}, {"id": "406", "name": "/function array Variable Function if eval", "regx": "/(\\/\\*[^\\*]*(\\*[^\\*\\/]*)+\\/\\s*)*((\\$[_\\->\\.a-z0-9]+)\\s*=[^;]+;\\s*([a-z_0-9]+)\\([^\\)]*\\4[^\\)]*[\\)\\s;]+\\s*function\\s*\\5)\\s*\\([^\\{]+\\{\\s*(\\$[_\\->\\.a-z0-9]+)\\s*(\\[[^\\]]+\\]+\\s*)*=\\s*array(_map)?\\s*\\(.+?\\);\\s*(\\$[a-z_0-9]+(\\s*\\[[^\\]]*\\]+)*[\\.\\s]*\\(.+?if[\\s\\(]*\\$[_\\.a-z0-9\\['\"\\]\\s]+\\)\\s*\\{)+\\s*(return\\s*)?eval\\s*\\([^\\)]+[\\)\\s;\\}]+/is"}, {"id": "407", "name": "Tagged error_reporting HTTP_USER_AGENT curl", "regx": "/(error_reporting\\s*\\([^;]+;\\s*|\\$[a-z_0-9]+\\s*=\\s*(array\\s*\\([^\\)]+\\)+[;\\)\\s]*|[\"'.\\s]*mb_strtolower\\(\\$_SERVER[\\{\\['\"]+)|if\\s*\\([^\\)]+)+HTTP_USER_AGENT(.+?http:\\/\\/|.+?curl_init){2,}.+?(\\$[a-z_0-9]+)?[\\s=]*curl_exec.+?(print|die|echo)['\"\\s\\(]+\\4['\"\\s\\);\\}]+\\s*/is"}, {"id": "408", "name": "header Location http space.php", "regx": "/header\\(['\"]Location: http:\\/\\/[^\\/]+\\/space\\.php\\?[^\\)]+\\);/i"}, {"id": "409", "name": "Copyright function getCookie document.write iframe", "regx": "/(\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/)\\s*+function\\s++(\\w++)\\(.+?navigator\\.userAgent.+?<iframe .+?\\2\\(.+?\\1/is"}, {"id": "410", "name": "Copyright function setCookie function", "regx": "/\\/\\*\\s*Copyright.+\\s*\\*\\/\\s*(function [sg]etCookie\\(.+?return[^;]*[;\\}\\s]+)*function ([a-z0-9\\_]+)\\(\\)[\\{\\s]+([a-z0-9\\s\\=]+navigator\\.userAgent.+?|function\\s+)[sg]etCookie\\(.+?\\2\\([^;]+[;\\}\\s]+/is"}, {"id": "411", "name": "php hex-encoded-lines", "regx": "/<\\?[ph\\s]++(?:[^;]*?\\\\x[0-9a-f]{2}[^;]*+[;\\s]++){9,}.*+\\s*+($|\\?>)/i"}, {"id": "412", "name": "php array http mt_rand meta refresh", "regx": "/<\\?[ph\\s]+(\\$[a-z_0-9]+[\\s=]+array[\\(\\s]+(['\"]http.+[,\\);\\s]+)+\\$[a-z_0-9]+[\\s=]+mt_rand\\(.+[\\);\\s]+(\\$[a-z_0-9]+[\\s=]+.+\\s*)+|if[\\(\\s]+[^\\{]+\\{\\s*header\\([^\\}]+\\}\\s*)\\?>\\s*(<head>\\s*)?<meta.*\\s*($|\\?>)/i"}, {"id": "413", "name": "php array function return base64_decode eval", "regx": "/<\\?[ph\\s]+\\$[_\\-\\>\\.a-z0-9\\{\\['\"\\]\\}]+\\s*=\\s*array\\(.+?function\\s+([a-z_0-9]+)\\((.+?;\\s*)+((\\$[a-z_0-9]+\\s*=\\s*)?(eval|\\$[a-z_0-9]+(\\s*\\[[^\\]]+\\])*)\\s*\\(\\s*(\\$_(REQUES|GE|POS)T\\[)?[^\\)]+[\\);\\s]+(exit|die)[^;]*;\\s*)+($|\\?>)/i"}, {"id": "414", "name": "php if isset GLOBALS strtolower SERVER if strstr GLOBALS", "regx": "/if[\\s\\(\\!]+isset[\\s\\(]+\\$GLOBALS\\[\"\\\\x[^\\]]+[\\]\\)\\s]+\\{\\s*(\\$[a-z_0-9]+)[\\s=]+strtolower[\\(\\s]+\\$_SERVER\\[\"\\\\x[^\\]]+[\\]\\);\\s]+((if|and)[\\(\\s\\!]+strstr[\\(\\s]+\\1[\\s,\"']+\\\\x[^\\)]+[\\)\\s\\{]+)+\\$GLOBALS\\[\"\\\\x[^\\]]+\\][^;]*;\\s*\\}/i"}, {"id": "415", "name": "function return function Variable function", "regx": "/(function[a-z_0-9\\s]+\\([^\\)]*[\\)\\s]+\\{\\s*return[a-z_0-9\\s]+\\([^\\)]*[\\)\\s]+;*\\}+\\s*)+(\\$[a-z_0-9]+[=\\s]+([a-z_0-9\\s]+\\([^\\)]*[\\)\\s]+|['\"][^;]*);+\\s*)+(\\$[a-z_0-9]+)[=\\s]+[^;]+['\"\\);]+\\s*\\4\\s*\\(+.*?\\);/i"}, {"id": "416", "name": "eval chr REPEATED", "regx": "/(<\\?[ph]*|\\/\\*[^\\*]*\\*\\/)\\s*((\\$[a-z_0-9]+)\\s*=\\s*')?(.+?\\.\\s*chr\\([0-9]+\\)\\s*\\.){20}.+\\s*(\\3\\s*=\\s*str_replace\\('\\#[',\\s]+\\3\\);\\s*)?((\\$[a-z_0-9]+)\\s*=\\s*create_function\\(['\\s,]*\\3\\);\\s*\\7\\(\\);)?(\\?>\\s*|$|\\1)/i"}, {"id": "417", "name": "garbage around eval VeriableFunction", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*(\\$[a-z_0-9]+[\\s\\.]*=([\\s\\.\\(]*(([\"']).*?(?<!_e)\\6|[\\$\\{a-z_0-9]+[\\}\\s]*(\\[[^\\]]+\\]+)*;|\\([^\\)]*\\);+|\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*(?=\\/))+[\\/\\.;\\^\\&\\|\\~\\)]*(?!\\*)\\s*|\\#.+\\n)+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*)+((\\@\\$[a-z_0-9]+|eval|if)\\s*\\(|\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/)*\\$[\\$\\{]*[a-z_0-9]+[\\}\\s]*(\\[[^\\]]+\\][\\s]*)*\\(.*?(.+'[\\.;\\)]+)?(\\s*'.+'[\\.;\\)]+)*\\s*(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*(\\?>|$)/i"}, {"id": "418", "name": "if defined define function global eval Variable Function", "regx": "/if[\\(\\s\\!]+defined\\([^\\)]+[\\)\\s\\{]+define\\([^\\)]+[\\)\\s]+;\\s*(\\$[a-z_0-9]+[\\s\\.]*=[^;]*;\\s*)*function\\s+([^\\(]*)\\([^\\)]*[\\)\\s]+\\{\\s*(global (\\$[^;]+);\\s*|(\\$[a-z_0-9]+[\\s\\.]*=[^;]*;\\s*)*for(each)?\\s*\\()(.+[\\r\\n]+)+?eval(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*\\((\\2|\\4)\\([^\\)]*[\\)\\s]+;(\\s*return[^;]*;\\s*\\})?\\s*\\}\\s*(\\/\\/.*\\s*)*/i"}, {"id": "419", "name": "iframe small height|width", "regx": "/(<script[^>]+src=['\"]?http\\:\\/\\/([a-z\\.\\-0-9]+)[^>]*><\\/script>)?<iframe.*?(\\s*(height|width|src)=['\"]?([0-5]['\"\\s]|http\\:\\/\\/[^>]+?)){3}[^>]*><\\/iframe>/i"}, {"id": "420", "name": "php global array function_exists return for unset", "regx": "/global (\\$[a-z0-9\\_]+);\\s*\\1[\\s=]+array\\(.+?function_exists\\([^\\)]+[\\)\\s\\&]+\\!function_exists\\(['\"]([a-z0-9\\_]+)['\"][\\)\\s\\{]+function\\s+\\2\\([^\\)]+[\\)\\s\\{]+global \\1;.+?return[^;]*[;\\s\\}]+for\\s*\\([^\\)]*[\\);\\}]+(\\{\\s*[\\$a-z0-9\\_]+\\([^\\)]+[\\);\\}]+)?unset\\(\\1\\);/i"}, {"id": "421", "name": "eval pack Hex", "regx": "/<\\?(?:php)?\\s*+(?:\\$[\\w\\['\"\\]\\.\\s]++=[^;]++;\\s*+|(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)(?:[^\\n\\?]++|\\?(?!\\>))*+(?:\\s|\\?>)++)++|\\@?error_reporting[\\(0\\)\\s]++;\\s*+)*+(?:if[\\s\\(\\!]++function_exists[^\\{]++\\{\\s*+)?(?:function\\s++(\\w++)\\([^\\)]*+[\\)\\s]++\\{[^\\}]+?(\\$\\w++)[\\.\\s]*+=(?:[\\^\\s]*+(?:(?:chr|ord)\\s*+\\(|pack\\(['\"](?:H\\*['\",\\s\\.\\dA-F]+|C['\"\\s,]+hexdec\\())[^\\)]*+[\\)\\s]++)++[;\\s\\}]++return\\s++\\3[;\\s]++\\}[;\\s]++)?(?:\\$\\w++\\s*+=\\s*+(?!eval)\\w++[^;]*+;\\s*+|if[\\s\\(\\!]++[^\\{]++\\{\\s*+)*+eval\\s*+\\((?:\\2\\s*+\\(|\\2\\s*+\\(|pack\\s*+\\(['\"]H\\*['\",\\s\\.\\dA-F]++)[^;]*+['\"\\);\\s\\}]++(?&C)*+(?:$|\\?>)/is"}, {"id": "422", "name": "php HTTP_USER_AGENT if header Location http .ru", "regx": "/<\\?[ph\\s]*(\\$[a-z_0-9]+)\\s*=\\s*(array\\([^\\)]+\\.ru['\"]\\)+;\\s*(\\$[a-z_0-9]+)\\s*=\\s*\\1\\[.+;\\s*(\\$[a-z_0-9]+)\\s*=[\\s\\(]*preg_match\\s*|\\$_SERVER\\[[\"']HTTP_USER_AGENT['\"]\\];\\s*if[\\s\\(]+[\\$a-z_0-9]+)\\(.*(\\3|\\1).*[\\)\\s\\{]+header\\(['\"]Location:\\s*(http:\\/\\/.+\\.ru\\/.*|['\"\\.\\s]+|\\1|\\3|\\4)+\\);[\\sdie\\(\\);\\}]*(\\?>\\s*|$)/i"}, {"id": "423", "name": "require_once wp-update.php REMOTE_ADDR HTTP_USER_AGENT require_once wp-class.php die", "regx": "/<\\?.+?require_once[\\s\\(\"']+wp-update\\.php[\"'\\);\\s]+\\$ip = \\$_SERVER\\[[\"']REMOTE_ADDR[\"']\\];.+?\\$_SERVER\\[[\"']HTTP_USER_AGENT[\"']\\].+?require_once[\\s\\(\"']+wp-class\\.php[\"'\\);\\s]+die\\(.+?($|\\?>)/is"}, {"id": "424", "name": "eval decodeURIComponent Encoded-text", "regx": "/(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|error_reporting\\([0\\);\\s]+|\\$[a-z_0-9\\[\\]\\{\\}'\"]+\\s*=[^;]+;\\s*|\\/\\/[^\\n]*\\n\\s*)*eval(\\s*\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/)*([\\s\\(]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*)+(gzuncompress|rawurldecode|decodeURIComponent\\((['\"]).*\\9)(\\s*\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/)*\\s*(\\(|\\))[^;]*;\\s*(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/.+\\s*)*/i"}, {"id": "425", "name": "functions BDN SVB SCk GCk if cookieEnabled GCk else SCk if loaded SVB addEventListener", "regx": "/function BDN\\(.+?function SVB\\(.+?function SCk\\(.+?function GCk\\(.+?return unescape\\(document\\.cookie\\.substring\\([^\\)]+[\\);\\s\\}]+if[\\s\\(]+navigator\\.cookieEnabled[\\)\\s\\{]+if[\\s\\(\\!]+GCk\\([^\\{]+\\{[\\}else\\{\\s]*SCk\\([^\\)]+[\\);\\s]+if[\\s\\(]+document\\.loaded[\\)\\{\\s]+SVB\\([^\\)]*[\\);\\s\\}]+else[\\{\\s]+if[\\s\\(]+window\\.addEventListener[\\)\\{\\s]+window\\.addEventListener\\([^\\)]+[\\);\\s\\}]+else[\\{\\s]+window\\.attachEvent\\([^\\)]+[\\);\\s\\}]+/is"}, {"id": "426", "name": "div style opacity0", "regx": "/<(div|font|a)\\s+(?=[^\\>]*style=['\"][^\\>]*(height|width|opacity|font-size)\\s*.\\s*0([^\\.]|\\.[0-1]))[^>]*((>[a-z\\&\\#\\!0-9;\\s]*<a\\s+)?(?=[^\\>]*href=['\"]http(?!s\\:\\/\\/premio\\.io\\/))[^<]*<\\/(a|\\1)?)+(>[a-z\\&\\#\\!0-9;\\s]*<\\/)?\\1>/is"}, {"id": "427", "name": "php error_reporting Long mail print_r SERVER", "regx": "/<\\?[ph\\s]*(error_reporting\\(.{9999,}|\\/\\/[^\\n]*\\n\\s*|\\$[a-z_0-9]+[\\s\\.]*=[^;]+;\\s*)+(if\\s*\\([^\\)]+[\\)\\s\\{]+die[\\s\\(]+[^;]+;[\\s\\}]*)*(if\\s*\\([^\\)]+[\\)\\s\\{]+)*(foreach|while)\\s*\\([^\\)]+[\\)\\s\\{]+(mail\\s*\\(.+\\s*)+([\\s\\}]*header[\\s\\('\"]+Location.+|\\s*print_r\\(\\$_SERVER.+|\\s*echo.+\\s*\\$[a-z_0-9\\+;]+)+[\\}\\s]*(\\?>((\\s*[\\[<]html[\\]>]){2}[^$]+<\\/html>)?|$)/i"}, {"id": "428", "name": "if !function_exists function curl return function include functions", "regx": "/if\\s*\\(\\s*\\!function_exists\\([\"']([a-z_0-9]+)['\"]\\)[\\)\\s\\{]*function\\s*\\1\\([^\\)]+\\)[\\)\\s\\{]+([^\\n]*curl_[^\\n]+\\s+)+return[^\\n]+[\\s+\\}]+function\\s*[a-z_0-9]+\\([^\\)]*\\)[\\)\\s\\{]+((\\$[a-z_0-9]+)\\s*=\\s*([a-z_0-9]+)\\([^\\n]+\\s+)+include[\\(\\s]+\\4.+?function\\s+\\5.+?($|(?=function )|(?=\\?\\>))/is"}, {"id": "429", "name": "if !current_user_can add_filter function a href http return", "regx": "/if\\s*\\(\\s*\\!current_user_can\\([^\\)]+[\\)\\s\\{]*add_filter[\\s*\\(]+[^,]+,\\s*[\"']([a-z_0-9]+)['\"]\\)[\\);\\s\\}]+function\\s*\\1\\(.+?add_filter[\\s*\\(]+[^,]+,\\s*[\"']([a-z_0-9]+)['\"]\\)[\\);\\s\\}]+.+return[^;]*[;\\s\\}]+function\\s*\\2\\(.+?<a href=['\"]http:\\/\\/.+return[^;]*[;\\s\\}]+/i"}, {"id": "430", "name": "php Array function return base64_decode eval Function Array", "regx": "/<\\?(?:php)?(?<C>(?:\\#|\\/\\/)[^\\r\\n]*+[\\r\\n]++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/|\\s++)*+(?:\\$\\w++(?&C)*+=\\s*+(?!array)[^;]++;++(?&C)*+|define[^;]++;++(?&C)*+|(\\$[\\$\\{\"']*+\\w++['\"\\}]*+)(?<A>(?&C)*+\\[[^\\]]++\\]++)*+(?&C)*+=(?&C)*+array(?&C)*+\\(.*?['\"][\\w\\/\\+]*+)*+(?:(?:['\"](?&C)*+,(?&C)*+['\"][\\w\\/\\+]++)++=*+['\"](?&C)*+(?:\\)(?&C)*+)++;(?&C)*+(\\$\\w++)(?&A)*+(?&C)*+=(?&C)*+create_function(?&C)*+\\((?:(?&C)*+['\"](?:\\\\x[\\dA-F]{2}|[\\*\\/]{2})++['\"](?&C)*+[,\\)\\.])++(?&C)*+;(?&C)*+|.*?function(?&C)++(\\w{11})\\w*+(?&C)*+\\(.+?return\\s++base64_decode.+?)(eval|\\$\\w++)(?&A)*+(?&C)*+(?:=(?&C)*+array_walk(?&C)*+\\((?&C)*+\\2(?&C)*+,|\\()(?&C)*+(?:(?:\\2|\\4|\\5|\\6)[^;]*+[;\\s\\}\\?><ph]++|.+(?:else[\\s\\{]+|add_action\\([^,]++,[\\s'\"]++)\\5[^;]++;(?:(?&C)*+\\})*+)++(?&C)*+(?:$|\\?>\\s*+)/is"}, {"id": "431", "name": "php Var Array Concat Variable Function", "regx": "/<\\?(?:php)?\\s*+(?:(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++)|(?:if[\\s\\(]++isset\\(|function\\s++\\w++\\([^\\{]++[\\{\\s]++.+?return)[^;]++;[\\s\\}]++)*((?:(?:(?&C)|if\\s*+)++\\(isset[\\s\\(]++[\\$\\{'\"_]++[^\\{]++[\\{\\s]*+)?(?:(?&C)|\\$\\w++\\s*+(?<A>\\[[^\\]]*+\\]++\\s*+)*+)++=\\s*+(?!require)[^;]*+;\\s*+|for(?:each)?\\s*+\\([^\\)]++[\\)\\s]++\\{[^\\}]++[\\}\\s]*+)+((?:function\\s++\\w++\\([^\\{]++[\\{\\s]++.*?|\\$\\w++\\s*+(?&A)*+=\\s*+)?\\$[\\$\\{]*+\\w++[\\}\\s]*+(?:(?&C)|(?&A))*+\\(.*;[\\}\\s]*+)++(?:$|\\?>)/i"}, {"id": "432", "name": "php array implode function Var Hex return VariableFunction eval", "regx": "/<\\?[ph\\s]+((\\$[a-z_0-9]+)\\s*=[^;]+[;\\s]+|function ([a-z_0-9]+)\\([^\\)]+[\\)\\{\\s]+.*\\s*((\\$[a-z_0-9]+)\\s*=[\\s'\"]+(\\\\(x[0-9a-f]{2}|[0-9]+))+['\"];\\s*)+)+.*\\s*eval\\s*\\((\\s*(\\3|\\2|\\5)\\s*\\(){2,}[^;]+[;\\s]+.+?($|\\?>)/i"}, {"id": "433", "name": "php array if SERVER if isbot file_get_contents header http", "regx": "/<\\?[ph\\s]+((error_reporting|ini_set)\\s*\\([^;]*;\\s*)*(\\$[a-z\\_0-9]+\\s*=[^;]+[;\\s]+)+(if[^;]+\\$isbot[^;]+[;\\}\\s]+)+(.+?(\\$[a-z_0-9]+)\\s*=\\s*(file_get_contents|curl_exec)\\s*\\([^\\}]+[\\}\\s]+)+((\\$[a-z\\_0-9]+)\\s*=[^;]*\\6[^;]*[;\\s]+)*.+((\\6|\\9)[^;]+[;\\}\\s]+((header|echo|print)[^;]+[;\\}\\s]+)+)+($|\\?>)/is"}, {"id": "434", "name": "php REQUEST array REQUEST array_filter exit", "regx": "/<\\?[ph\\s]+(\\$[a-z\\_0-9]+)\\s*=\\s*\\$_(REQUES|GE|POS)T\\[[^;]+[;\\s]+(\\$[a-z\\_0-9]+)\\s*=\\s*array\\(\\$_(REQUES|GE|POS)T\\[[^;]+[;\\s]+\\$[a-z\\_0-9]+\\s*=\\s*array_filter\\(\\3[,\\s]*\\1\\)[dexit\\(\\);\\s]*($|\\?>)/i"}, {"id": "435", "name": "php base64_decode create_function VariableFinction", "regx": "/<\\?.+?(\\$[a-z_0-9]+)\\s*=\\s*base64_decode\\(.+?((\\$[a-z_0-9]+)\\s*=\\s*(\\@?(gzinflate|strrev)\\()+\\1.+?)?(\\$[a-z_0-9]+)\\s*=\\s*create_function\\([^,]+[,\\s]+(\\1|\\3)[^;]+[;\\s]+\\6\\([^;]+[;\\s\\}]+(else[\\{\\s]+[^\\}]+[;\\s\\}]+|echo[\\s\\(]*(['\"]).+?\\9[;\\s\\}]+)*($|\\?>)/is"}, {"id": "436", "name": "php function wp_enqueue_script json2 add_action", "regx": "/(?:\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++)*+(?:if[\\s\\(\\!]++function_exists\\(\\s*+['\"](\\w++)['\"][\\s\\)]++\\{\\s*+)?function\\s+(\\w++)\\s*\\([^\\{]++\\{\\s*+(?:(?:echo|print|\\?>)[\\('\"\\s]*+(?:<script[^>]+?src=\\\\?['\"](?:data\\:|http[s]?\\:\\/\\/)[^>]++>\\s*+<\\/script>\\s*+)++(?:['\"][\\);\\s]*+|<\\?(?:php)?)[\\}\\s]*+|(?:if[\\s\\(\\!]++isset[\\(\\s]++[^\\)]++[\\s\\)]++[\\{\\s]*+)?(?:\\$\\w++\\s*+=[^;]++;\\s*+)*(?:\\$[a-z_0-9]++\\s*+=\\s*+)?(?:eval|\\$\\w++)\\s*+\\(.*?base64_decode\\(.+?|(?:(\\$\\w++)\\s*+=[\"\\s'\\(]++s[\"\\s'\\.]*+c[\"\\s'\\.]*+r[\"\\s'\\.]*+i[\"\\s'\\.]*+p[\"\\s'\\.]*+t[\"\\s'\\.\\)]++;\\s*+)?(?:echo|print)[\\s\\('\"]++<[\"\\.'\\s]*+(?:script|\\3).+?(?:chr|fromCharCode)\\(.+?(?:chr|document\\.write)\\(.+?)add_action\\s*+\\([^,]++['\"\\,\\s]++(?:\\1|\\2)['\"\\);\\s]++/is"}, {"id": "437", "name": "php if function_exists function return Variable Function eval", "regx": "/<\\?(?:php)?(?:(?<C>[\\s\\.\\)]++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/)*+(?:(?<F>function(?&C)++\\w++(?&C)*+\\((?&C)*+(\\$\\w++)?[^\\{]*+\\{(?&C)*+(?:(\\$\\w++)(?&C)*+=(?:(?&C)*+(?:\"[^\"]*+\"|'[^']*+'|\\w++(?<!pack)(?&C)*+\\([^\\)\\^]*+[^;\\^]++))++(?<E>[;\\}]++(?&C)*+)++)*+(?:\\8(?&C)*+\\([^\\)]*+(?&C)++(?&E)++|for[^\\{]++\\{[^\\}]++(?&E)++|(?:(\\$\\w++)(?&C)*+=|return)(?&C)*+(?:(?:(?<N>\\w++(?<!pack)(?&C)*+\\((?&C)*+)*+\\$\\w++(?&C)*+\\^(?&C)*+(?&N)*+\\$\\w++[^;]*+|\\@?pack(?&C)*+\\([^\\)]*+[^;]++|(?&N)*+\\3[^;]*+|\\4|\\6[^;]*+)(?&C)*+(?&E)++)++)++)|(?:if[\\s\\(]++\\!function_exists\\([^\\{]++[\\{\\s]++)?function(?&C)*+(\\w++)[^\\{]++\\{(?&C)*+|if(?&C)*+\\([^\\{]++\\{(?&C)*+|(\\$\\w++)(?&C)*+=(?&C)*+(?:\\w++(?&C)*+\\((?&C)*+)?\\$\\w++(?&C)*+\\([^\\)]*+[^;]++(?&E)++|for[^\\{]++\\{[^\\}]++(?&E)++|(?!\\$license = \\$this->license\\(\\))(?<V>\\$\\w++(?:\\s*+\\[[^\\]]++\\]++)*+(?&C)*+=[^;]++(?&E)++)++)*+(?:(?:return(?&C)++\\$\\w++|eval)(?&C)*+\\(|return(?&C)*+\\9[;\\}\\s]++(?:(?&F)++(?&V)*+)++eval(?&C)*+\\([^\\)]*+)[^;]*+(?&E)++(?&V)*+(?:(?:die(?&C)*+\\([^\\)]*+[^;]++(?&E)++|(?&F)++|\\/\\/.+\\s*+)*+(?&V)*+)++)++(?:$|\\?>)/i"}, {"id": "438", "name": "Leftover Header if GLOBALS Hex SERVER Hex/i", "regx": "/<\\?[ph\\s]+if\\s*\\([^\\{]+\\$GLOBALS[\\{\\[]['\"]\\\\x[^\\{]+[\\{\\s]+\\$[a-z_0-9]+(\\[[^\\]]+[\\]\\s\\}]+)*=[^;]+\\$_SERVER[\\{\\[]['\"]\\\\x[^;]+[;\\s]+if\\s*\\([^\\{]+\\$GLOBALS[\\{\\[]['\"]\\\\x[^;]+;[\\s\\}]*($|\\?>)/i"}, {"id": "439", "name": "if HTTP_USER_AGENT add_action wp_footer function echo", "regx": "/(?:(?:(?:\\5\\6\\7)?(?=((?:((?:\\$[a-z_0-9]+(?:\\s*\\[[^\\]]+[\\]]+)*\\s*=\\s*get_option\\([^;]++[;\\s\\}]++|if\\s*\\([^\\{]+HTTP_USER_AGENT[^\\{]+[\\{\\s]+)*add_action\\(['\"\\s]+wp_footer['\"\\s,]+)([a-z_0-9]+)(['\"\\s]+[^;]+[;\\s\\}]+))+))|(?:\\1\\s*)*+(?=(function\\s+)([a-z_0-9]+)([^\\{]+[\\{\\s]+if\\s*\\([^\\{]+HTTP_USER_AGENT[^\\{]+[\\{\\s]+(?:(\\$[a-z_0-9]+)(?:\\s*\\[[^\\]]+[\\]]+)*\\s*=(?:[\\.\\s]*(\"[^\"]++\"|'[^']++'|[0-9\\*\\+\\-\\/\\(\\)]++))++[;\\s\\}]++)+echo[\\s\\('\"]+(\\$[a-z_0-9]+)+[^\\}]++[\\}\\s]++))){2}|(?:(?:\\15\\16\\17)?(?=((?:((?:\\$[a-z_0-9]+(?:\\s*\\[[^\\]]+[\\]]+)*\\s*=\\s*get_option\\([^;]++[;\\s\\}]++)*if\\s*\\([^\\{]+HTTP_USER_AGENT[^\\{]+[\\{\\s]+add_action\\(['\"\\s]+wp_footer['\"\\s,]+)([a-z_0-9]+)(['\"\\s]+[^;]+[;\\s\\}]+))+))|(?:\\11\\s*)*+(?=(function\\s+)([a-z_0-9]+)([^\\{]+[\\{\\s]+(?:(\\$[a-z_0-9]+)(?:\\s*\\[[^\\]]+[\\]]+)*\\s*=(?:[\\.\\s]*(\"[^\"]++\"|'[^']++'|[0-9\\*\\+\\-\\/\\(\\)]++))++[;\\s\\}]++|if\\s*\\([^\\{]+HTTP_USER_AGENT[^\\{]+[\\{\\s]+)+echo[\\s\\('\"]+(\\$[a-z_0-9]+)+[^\\}]++[\\}\\s]++))){2})(\\5\\3\\7|\\2\\6\\4|\\15\\13\\17|\\12\\16\\14)/i"}, {"id": "440", "name": "div display none href http buy", "regx": "/<(?:(div)|(a))\\s+(?=[^>]+?display[\\s\\:]+none)[^>]*?(?:(?:(?!\\2)[a]?>\\s*<(a)\\s+[^>]*?)?href[='\"]+http[^>]++>[^<]*?(escort|buy)[^<]*+<\\/(?=a))+(\\3>\\s*<\\/\\1|\\2)>\\s*/i"}, {"id": "441", "name": "Copyright function setCookie return function userAgent setCookie write iframe top Neg", "regx": "/(<(script)[^>]*>\\s*)(\\/\\*\\s*Copyright.+\\s*\\*\\/\\s*|(\\$|var\\s+)?[a-z_0-9\\s]+=[^;]+;\\s*|setTimeout\\([^;]+;\\s*)*(function [sg]etCookie\\(.+?((document\\.cookie=|return)[^\\}]+[;\\}\\s]+)+)+.+?[sg]etCookie\\(.+?document\\.write\\(([\"'])<(script|iframe)[^>]+(src=['\"\\+\\shtps\\:]+\\/\\/|top:\\s*\\-)((.+?document\\.cookie|.+?\\.toUTCString){2}[\\(\\)\\}]+|.+?encodeURIComponent\\(document\\.referrer\\))?.+?\\8\\/\\9>\\8[^;]+[\\);\\}\\s]+(<\\/\\2>|$)/is"}, {"id": "442", "name": "php Lots of Hex", "regx": "/<\\?(?:php)?+\\s*+(?<C>(?:\\#|\\/\\/)[^\\r\\n]*+\\s++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+)*+(?!return[^;]*+;\\s*+(?:$|\\?>))(?:(?:[^\\\\]*+\\\\[^x\\d])*+[^\\\\]*+\\\\[x\\d][\\dA-F]){257,}[^\\r\\n]*+\\s*+(?:(?:(?:(?!\\$\\w++\\s*+\\()[^;]*+;++\\s*+)*+\\$\\w++\\s*+\\([^\\)]*+[^;]++;++\\s*+)++(?:(?&C)|\\$\\w++\\s*+=.++\\s*+)*+)?+(?:$|\\?>\\s*+)/i"}, {"id": "443", "name": "script document write div anchor script document write ENDdiv", "regx": "/<script[^>]*>\\s*document\\.write\\([\"']([\\<div][\"' \\+]*){4}[^>]*>[\"']\\);\\s*<\\/script>\\s*(<a .+?<\\/a>\\s*)+<script[^>]*>\\s*document\\.write\\([\"']([\\<\\/div][\"' \\+]*){5}>[\"']\\);\\s*<\\/script>/i"}, {"id": "444", "name": "echo div position absolute negative anchors", "regx": "/(?<!['\"])<(html>)?[\\s<]*(((script)[^>]*>\\s*document\\.write[\\(\\s'\"]+<)?(?<!\\\\n\\s{4}<)div|(style)[^>]*>\\s*[\\.\\#]([a-z_\\-0-9]+)[\\s\\{]+)([^\\}\\>]*(?:(?:height|width)\\:\\s*0p|(?:left|position|top|opacity|filter|display|text-indent)\\:\\s*(?:alpha\\(opacity=0|0?\\.|\\-[0-9\\.]{3,}|none|absolute))){3,}[^>]*>['\"\\);\\s]*(?:<\\/\\5>)?.*?<(?:(?:([b-s][a-z0-9]*)[^>]*>[.\\s]*<)*a .+?<\\/a(?:>[^<]*<(?:\\/(?:[^d][a-z0-9]*|\\8))?)*)+>(?:[^<]*+<\\/(?:\\2|div)>)*/i"}, {"id": "445", "name": "PHP Garbage Around eval Variable Function", "regx": "/<\\?[ph\\s]+\\$[a-z\\_0-9]+\\s*=(\\s*'.+'[\\)\\.]+)+(\\s*'.+eval\\s*\\(\\$[\\$\\{]*[a-z\\_0-9]+[\\} \\t]*(\\[[^\\]]+\\][ \\t]*)*\\(.+'[\\)\\.]+)(\\s*'.+'[\\.;\\)]+)+\\s*(\\?>|$)/i"}, {"id": "446", "name": "stripslashes REQUEST if echo return fopen fwrite fclose echo function return", "regx": "/(\\\\xEF\\\\xBB\\\\xBF)?<\\?[ph\\s]+(\\$[a-z_0-9]+\\s*=\\s*(stripslashes[\\(\\s]+)?\\$_(REQUES|GE|POS)T\\[[\"'][^;]*[;\\s]+)+(if[\\(\\s]+\\$[^\\{]*[\\{\\s]+echo\\s*[\"'][^;]+[;\\s]+return[^;]*;[\\s\\}]+)+.+?fopen\\(.+?fwrite\\(.+?fclose\\([^;]*[;\\s]+echo\\s*[\"'][^;]+[;\\s]+(function.+?[rmk]{2}dir\\([^;]+;[\\s\\}]+(return[^;]*;[\\s\\}]+)*)+($|\\?>)/is"}, {"id": "447", "name": "Var Hex Variable Function", "regx": "/<\\?(?:php)?\\s*+(?:(?:(?:(?:(?:\\$\\w++(?:\\s*\\[[^\\]]+\\])*+[\\s\\.]*+=[\\s\\(]*+(?:[\\@\\{\\[\\$\\-\\w\\\\]|\"(?:[^\"]*+(?<=\\\\)\")*+[^\"]*+(?<!\\\\)\"|'(?:[^']*+(?<=\\\\)')*+[^']*+(?<!\\\\)')|echo[\\s\\('\"\\$]++|retur|die|for[\\s\\(][^;]++;[^;]++;[^\\)]++\\)[\\)\\s\\{]*+)[^;]*+['\\);\\}\\s\\\\]++|(?:function|if)[\\s\\(][^\\)]++[^\\{]++[\\{\\s]++)*(\\$(?!k_opad)[\\w\\{\"\\}\\\\]++).*?\\\\(?:x[a-f\\d]{2}|[0-9]{2,3})[^;]*+[;\\}\\s]++)+)+(?:if\\s*+\\([^\\{]++\\{\\s*+)?(?:(?:(\\$\\w++)\\s*+=\\s*+|return\\s*+|echo['\"\\s\\(]*+)?\\@?(?:\\$[\\w\\{\"\\}\\\\]++(?:\\s*+\\[[^\\]]++\\]++)*+|eval)\\s*+\\((?:[^;]++;(?=\\)|'))*+[^;]++;\\s*+)++[\\}\\s]*+)++(?:(?:if|for|\\1|\\2|\\/\\/)[^\\n\\?]++(?:(?!\\?>).[^\\n\\?]++)*+\\s*+|(?:echo|exit|die)[\\s\\(]*+(?:\"(?:[^\"]*+(?<=\\\\)\")*+[^\"]*+(?<!\\\\)\"|'(?:[^']*+(?<=\\\\)')*+[^']*+(?<!\\\\)')?[\\);\\s\\}]++|else[\\s\\{]++[^\\}]*+[\\};\\s]++)*+(?:$|\\?>\\s*+)/i"}, {"id": "448", "name": "ini_set if isset array base64_decode function return base64_decode eval Variable Function", "regx": "/<\\?[ph\\s]+([if\\s\\(\\!\\@]*(ini_set\\s*\\(|error_reporting\\s*\\(|set_time_limit\\s*\\(|isset\\s*\\(|define|global|\\$[\\{\\$a-z_0-9\\}\\[\\\\'\"\\]\\s]+=)[^;]+;[\\}\\s]*|foreach[\\s\\(]+[^\\{]*?\\$_COOKIE[^\\{]+\\{[^\\}]+[\\s\\}]+)+(function\\s*([a-z_0-9]+)\\s*\\(.+?(return[\\s\\(_]*(\\4|base64_decode)|\\$[a-z_0-9]+\\s*(\\[[^\\]]*\\]\\s*)*)\\(.+?)+(?<!\\/\\/,function\\(response\\) \\{ )eval\\(.+?\\)+;[\\s\\};]+(.*function[\\s\\(_]+\\6[^\\{]+[\\{\\s]+(\\$[a-z_0-9]+)\\s*=.+?return[^;]*\\9[^;]*[;\\s\\}]+)?(\\?>\\s*(\\#\\![\\/a-z_\\-0-9\\+=\\s]{200,}$)?|$)/is"}, {"id": "449", "name": "class const pack H* function include new Class", "regx": "/class\\s+([a-z_0-9]+)\\s*\\{((?=.+\\$qString[\\s=]+\\$this-\\>([a-z_0-9]+)\\()|.+?const\\s+([a-z_0-9]+)\\s*=.+?self\\:\\:([a-z_0-9]+)\\(pack\\('H\\*'[,\\s]+self\\:\\:\\4).+?function\\s+(\\3|\\5)\\((\\$[a-z_0-9]+).+?(\\$[a-z_0-9]+|\\@?(base64_decode|include|require)(_once)?)\\s*\\([^;]*\\7.+new\\s+\\1[^;]*;/is"}, {"id": "450", "name": "eval str_rot13", "regx": "/(\\/\\/.+)?\\s*((ini_set\\(|error_reporting\\(|\\$[a-z_0-9]+[\\s\\.]*=)[^;]*;\\s*)*\\@?(eval|assert|\\$[a-z\\_0-9]+(\\s*\\[[^\\]]+\\])*)\\s*\\(\\s*str_rot13\\('.*'\\)\\);(\\s*\\1)?/i"}, {"id": "451", "name": "php GLOBALS Hex function if eval", "regx": "/<\\?[ph\\s]*(\\$[a-z_0-9]+\\s*=[^;]+;\\s*)*(\\s*\\$(GLOBALS|[\\{\\$'\"]+[0-9_\\-a-z\\\\'\"\\.]+[\\}\\s]+)[\\s\\[\\{'\"]+([a-z_0-9]+)[\"'\\}\\]\\s]+([^;]*;\\s*global\\s*\\$\\4[^;]*;\\s*\\$\\4\\s*=\\s*\\$GLOBALS[^;]*;\\s*\\$(\\4|\\{[^\\}]*\\}+)\\s*(\\[[^\\]]+[\\]\\s]+|\\{[^\\}]+[\\}\\s]+)+)?=\\s*['\"](\\\\x[0-9a-f]{1,2})+[\"'];\\s*((echo|print)?[\\s\\@]*\\$(GLOBAL|\\4)[^;]+;\\s*|\\$[a-z_0-9]+\\s*([\\.=]+\\s*\\@?(NULL|(array[\\(\\s]+)?\\$(GLOBAL|\\4)[^;]*|\\$[a-z_0-9]+(\\[[^\\]]+[\\]\\s]+|\\{[^\\}]+[\\}\\s]+)*|[\"']+))+;\\s*|global\\s*\\$[^;]+;\\s*|function\\s+[0-9_a-z]+\\s*\\([^\\{]+|\\{\\s*|\\}\\s*|(for(each)?|(else\\s*)?if)\\s*\\(.+?\\)+\\s*|return[^;]*;[\\s\\}]+){30,})*eval(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*\\((?!\" \\?>\".\\$GLOBALS\\['Oxygen_VSB_Current_Comments_Class).+?\\)+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*;[\\s\\}]+(exit[^;]*;[\\s\\}]+|\\?>\\s*<\\?[ph\\s]+\\$[a-z_0-9]+\\s*=\\s*array\\([^\\)]*\\)+.+;\\s*)*($|\\?>)/i"}, {"id": "452", "name": "php if isset POST base64_decode mail", "regx": "/<\\?[ph\\s]*((\\$([0-9_a-z]+)\\s*=\\s*explode[^,]+,\\s*base64_decode\\([^;]+;\\s*|(error_reporting|ini_set|session_start)\\([^;]+;\\s*|((if|isset|foreach)[\\s\\(]+)+\\$_(REQUEST|GET|POST|SERVER).[^\\{]+\\{\\s*)?((\\$[a-z_0-9]+)[\\s\\.]*=[a-z_0-9\\s\\(]*[\\$\"f'](\\3|_REQUEST|_GET|_POST|_SERVER|REMOTE_ADDR|http[s]?\\:\\/\\/|ile_get_contents[\\s\\(]+)([^;]*[\\);\\}\\s]+else[\\{\\s]+|echo|print)*[^;]*[;\\s]+(\\s*exit;)?|\\/\\/.+\\n+|\\?>\\s*<\\?[ph\\s]*)*[\\}\\s]*)+((\\$[0-9_a-z]+)[\\s\\.]*=[^;]+;\\s*|fwrite\\([^;]+;\\s*)*((\\$[a-z_0-9]+\\s*=\\s*|if[\\s\\(]+|else[^\\{]*[\\{\\s]+)*(mail\\s*\\(|file_get_contents[\\s\\(]+['\"]http)[^;]+[\\);\\s\\}]*)+(((if[\\(\\s]+|else)[^\\{]*[\\{\\s]+)?((header|echo|exit)[^;]*[;\\s\\}]+)+)*($|\\?>)/i"}, {"id": "453", "name": "wp_enqueue_script SWEETCAPTCHA", "regx": "/wp_enqueue_script\\([^,]+,\\s*['\"]http.+?SWEETCAPTCHA[^;]+;/i"}, {"id": "454", "name": "php class Variable Functions new CLASS", "regx": "/<\\?[ph\\s]*+(?:\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++)*+(?:(?:ini_set|error_reporting|set_time_limit)\\([^\\)]*+\\)++;\\s*+|class\\s++.+?(\\$\\w++)\\s*+=[\\s\\@]*+file_get_contents\\(.+?return\\s++\\1[;\\}\\s]*+|class B\\s*+\\{.*?return 'UN'[^;]*+[;\\}\\s]++|function[^\\(]+dolly[^\\{]++\\{.*?(?:return|dbDelta)[^;]*+[;\\}\\s]*+|if[\\s\\(\\!]++class_exists[\\s\\('\"]++(\\w++)['\"\\)\\s\\{]++|(?=.*return myfuncgood\\()|(?=.*?(\\$\\w++)\\s*+=\\s*+\\$\\w++\\s*+\\([^;]++;\\s*+\\3\\s*+\\()|(\\$\\w++)\\s*+=\\s*+class_exists[\\s\\('\"]++(\\w++)['\"\\)\\s]++;\\s*+(?:(\\$\\w++)\\s*+=\\s*+\\4\\s*+;\\s*+)?if[\\s\\(\\!]++(?:\\4|\\6)[\\)\\s\\{]++)++class\\s++(\\w++)\\s*+\\{.+(?:(?<!\"|\"\\{)\\{?\\$[\\w\\{\\}]++(?:\\s*+\\[[^\\]]++\\]++)*+|file_put_contents\\s*+\\(\\s*+(\\$[\\w>]++).+(?:touch|(\\$[\\w>]++)\\s*+=\\s*+fopen\\s*+\\(\\s*+\\8.+?fwrite\\s*+\\(\\s*+\\9.+?fclose)|eval)\\s*+\\(.+(\\$\\w++|new)[\\=\\s]++(?:\\1|\\5|\\7)(?:\\:\\:getInstance)?\\([^\\)]*+[\\);\\s]++(?:(?:echo|else)*+[\\s\\{]*+[\\$\\w\\{\\}]++(?:\\:\\:|->|\\s*+=\\s*+)(?:NULL|[\\w\\{\\}]++\\s*+\\()[^;]*+;[\\s\\}]*+)*(?:.*?exit\\(\\);\\s*+)?(?:$|\\?>)/is"}, {"id": "455", "name": "visitorTracker", "regx": "/((<\\!--|\\/\\*)visitorTracker(\\*\\/|-->)|if[\\s\\(\\!]+(loadStats)[\\)\\s\\{]+function \\4[\\(\\)\\s\\{]+)\\s*(<\\?[ph\\s]+.+?base64_decode\\s*\\(.+?\\?>|.+?document\\.createElement\\(['\"]script[\"']\\);\\s*.+?\\.src\\s*=[\\s'\"]+[htps\\:]*\\/\\/.+?)\\s*(\\1|\\4[\\(\\)\\};\\s]+)/is"}, {"id": "456", "name": "fsockopen fwrite while feof fclose preg_match gzuncompress", "regx": "/<\\?[ph\\s]*((\\$[a-z_0-9]+)\\s*=[^;]+;\\s*|if[\\s\\(]+|file_exists[\\s\\(]+[^\\{]+\\{\\s*)*(\\$[a-z_0-9]+)\\s*=\\s*\\@?fsockopen\\([^;]+;\\s*.*?while[\\s\\(\\!]+feof\\(\\3[\\)\\s]+(\\$[a-z_0-9]+)[\\s\\.]*=.+?fclose\\(\\3\\);\\s*(preg_match\\([^,]+,|\\4\\s*=\\s*end\\(explode)[^,]+,\\s*(\\$[a-z_0-9]+)\\)+;\\s*(echo[\\s\\(]+\\4[^;]*;\\s*)?if[\\(\\s]+[^\\{]*\\6.+?(file_put_contents|gzuncompress)\\([^;]+;[\\}\\s]+(\\@?ini_set\\([^;]+;[\\}\\s]*)*($|\\?>)/is"}, {"id": "457", "name": "Tagged eval function", "regx": "/(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/)\\s*([\\sa-z_0-9\\=]+[;\\s\\(]+)*window[\\.\\[\"']+\\\\x[^;]+;\\s*eval[\\(\\s]+[^\\)]+['\"\\s\\(\\);\\}]+\\1/is"}, {"id": "458", "name": "script var http if document write script src", "regx": "/(<script[^>]*>\\s*((var\\s*)?[a-z_0-9]+\\s*=['\";\\s]*(setTimeout|encodeURIComponent)\\([^\\)]*['\"\\);\\s]{2,})+([var\\s]*([a-z_0-9]+)\\s*=\\s*['\"][hft]+tp[s]*:\\/\\/[^;]+;\\s*((var\\s*)?([a-z_0-9]+)\\s*=\\s*\\6[^;]+;\\s*)+)?if[^\\{]+\\{\\s*document\\.write\\([\"']<[script\\s'\"\\+]{7,}[^\\}]*src=['\"\\s\\+]+(\\9|[fht]+tp[s]*:\\/\\/.+jquery\\.(min\\.)*php)([^;]+[\\};\\s]+)+?<\\/script>)+/i"}, {"id": "459", "name": "function unset wp_list_table items add_action", "regx": "/<\\?[ph\\s]*+(?:\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/|(?:\\#|\\/\\/)[^\\r\\n]++[\\r\\n]++|\\$[^;=]++=[^;]++;|\\$\\w++(?:\\s*+\\[[^\\]]*+\\])*+\\s*+\\([^;]++;|(?:for(?:each)?|if)\\s*+\\([^\\{]++\\{|[\\:\\w]++\\s*+(?:\\([^;]++)?;|else[\\s\\{]*+|\\@|[\\}\\s]++|ini_set\\([^;]++;|function\\s++\\w++\\s*+\\([^\\}]++)*function\\s++(\\w++)\\s*+\\([^\\}]+(?:(?:update_option[\\('\"\\s]++active_plugins|pack\\(['\"]H\\*['\",\\s\\.0-9A-F\\)]++;\\s*+if[\\s\\(\\@is_]++file[_a-z]*+[\\(\\s]++(\\$\\w++)).+?file_get_contents[\\s\\(]++(\\$\\w++).+?file_put_contents[\\s\\(]++(?:\\2|\\3)|unset\\(\\$wp_list_table->items\\[|file_get_contents\\(__FILE__.+?fputs\\(.+?scandir\\().+?add_action\\([^,]++[,\\s0-9\\)'\"]++\\1.++/is"}, {"id": "460", "name": "php error_reporting function detect_cms", "regx": "/<\\?[ph\\s]+(\\@?(error_reporting|set_time_limit|ini_set)\\(.*?0\\);\\s*)+.*?function (detect_cms\\().+\\3[^;]+;\\s*((if\\s*\\([^\\)]+|else)[^\\{]*[^\\}]+[\\}\\s]+)+($|\\?>)/is"}, {"id": "461", "name": "function glues_it sanitize_key call_user_func_array", "regx": "/(if[\\s\\(\\!]+function_exists\\(\\s*['\"][a-z_0-9]+['\"][\\s\\)]+\\{\\s*)?function\\s+([a-z_0-9]+)\\s*\\([^\\)]+[\\)\\s]+\\{\\s*(\\$[a-z_0-9]+)\\s*=\\s*sanitize_key\\([^;]+;\\s*(\\$[a-z_0-9]+)\\s*=\\s*call_user_func_array\\(\\s*\\3[^;]+;\\s*return\\s*\\4.+\\2\\s*\\([^;]+;\\s*.+call_user_func_array\\([^;]+;[\\}\\s]+($|(?=\\?>))/is"}, {"id": "462", "name": "php eval <PERSON>ar <PERSON>", "regx": "/<\\?[ph\\s]+(@?eval\\s*\\(\\s*)+(['\"])[\\s\\\\]*\\$[a-z\\_0-9\\[\\]\\{\\}\\s'\"]+=[\\s\\\\'\"]*(\\\\x[0-9]{2})+.+\\2(\\s*\\))+;\\s*($|\\?>)/i"}, {"id": "463", "name": "if strpos REQUEST_URI include", "regx": "/if([\\s\\(]+str[a-z_0-9]+){2,}[\\s\\(]+\\$_SERVER\\[[\"']REQUEST_URI['\"]\\]\\s*\\)+[^\\)]+[\\)\\s]+\\{[\\s\\@]*(include|require)(_once)?[^;]+[;\\s]+(exit[\\s;]+)?\\}/i"}, {"id": "464", "name": "error_reporting ini_set set_time_limit ignore_user_abort elseif require_once", "regx": "/<\\?[ph\\s]+(\\/\\*[^\\*]*(\\*[^\\*\\/]*)+\\/\\s*|\\/\\/[^\\n]*\\s+)*(\\@?(error_reporting|ini_set|set_time_limit|ignore_user_abort)\\s*\\([^;]+[;\\s]+){3,}((else\\s*|if\\s*\\(+[^\\)]+[\\s\\)]+)+(.*?(assert|eval|file_put_contents|fwrite\\(.*?fclose)\\([^\\)]+[\\);\\}\\s]*)*\\@?(require|include)(_once)?[^;]+[;\\}\\s]+)+($|\\?>)/i"}, {"id": "465", "name": "php if function_exists function curl_init return function trim return base64_decode", "regx": "/(?:(?:(\\$\\w++)\\s*+=[^;]++;\\s*+)?if[\\s\\(\\!]++function_exists[\\s\\(]++[\"'](\\w++)['\"][\\)\\s\\{]++function\\s++\\2\\s*+\\([^\\)]*+[\\)\\s\\{]++(?:(?<I>if\\s*+\\([^\\{]++\\{\\s*+)(?:(\\$\\w++)[\\s=]++(?<P>\\$_(?:GE|POS|REQUES)T[\\[\\s]++['\"])log['\"][\\]\\s]++;\\s*+|(\\$\\w++)[\\s=]++(?&P)pwd['\"][\\]\\s]++;\\s*+)++(\\$\\w++)\\s*+=\\s*+(?:\\([^\\)]++\\))?[\\s\\@]*+wp_authenticate[\\s\\(]++(?:\\4|(?&P)log['\"][\\]\\s]++)[\\s,]++(?:\\6|(?&P)pwd['\"][\\]\\s]++)[\\s\\)]++;\\s*+(?&I)(\\$\\w++)\\s*+=[^;]*+;\\s*+(?:\\$\\w++\\s*+=\\s*+)?wp_remote_get[\\s\\(]++\\8[\\s\\)]++;\\s*+(?<E>\\}\\s*+){2}|(?:(\\$\\w++)[\\s\\.]*+=\\s*+(?:'[^']*?<\\/?script[^']*+'|\"[^\"]*?<\\/?script[^\"]*+\")[;\\s\\.]++)++echo[\\s\\(\"]++\\10[\"\\);\\s]++)(?&E)add_action\\s*+\\([^,]++[,\\s'\"]++\\2['\"][^;]++;\\s*+(?&E)){2}/i"}, {"id": "466", "name": "PHP reversed", "regx": "/^(\\s*\\>\\?\\s*)?;.+(ohce|\\$)([ph\\s]*\\?\\<)?\\s*$/is"}, {"id": "467", "name": "if isset REQUEST Variable Function", "regx": "/(?<=<\\?php|\\*\\/)\\s*+(?:(?:\\@\\s*'[^']*+[^;]*[;\\s]*|\\$[\\w\\{\\}]++(?:\\s*+\\[[^\\]]*+\\]++)*+\\s*+=[^;]++[;\\s]++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/|\\/\\/[^\\r\\n]++\\s++)+.*)?(?:if[\\s\\(\\!]++(?:isset|empty)[\\s\\(]++\\$_(REQUEST|GET|POST|COOKIE|SESSION)[^\\{]++\\{\\s*+)++(?:\\$[\\w\\{\\}]++\\s*+=\\s*+(?:'[^']*+'|\"[^\"]*+\"|\\$|extract[\\s\\(]++\\$_\\1)[^;]*['\";\\)\\s]*)*.*((?<!-\\>)\\$\\w++)(?:\\s*+\\[[^\\]]*+\\])*+\\s*+\\(.*?\\)++\\s*+;[^\\}]*+\\}++(?:[;\\s]*+(?:if[\\s\\(]++[^\\{]++|else\\s*+)\\{[^\\}]++\\}++)*[^\\?\\n]*+(?:\\n|$|(?=\\?>))/i"}, {"id": "468", "name": "eval base64_decode", "regx": "/<\\?(?:php)?(?:(?<C>(?:\\#|\\/\\/)[^\\r\\n]*+[\\r\\n]++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/|\\s++)|null;|=|\\@|(?:(?:error_reporting|eval|set_time_limit|ini_(?:set|restore))\\s*+\\([^\\)]*+\\)++|\\$\\w++\\s*+=)[^;]*+;[\\s;\"'\\)]*+|\\?>\\s*+<\\?(?:php)?(?&C)*+)*(?:if[\\(\\s]++(?:[issetmd5]++\\()*+\\$_(?:POS|GE|REQUES)T\\[.+[\\)\\s\\{]+|echo\\s*+)*(?:(\\$\\w++)\\s*+=\\s*+\\@?(?:base64_decode|strrev)[\\s\\(]++[^;]++;\\s*+\\@?(?:eval[\\s\\(]++.*\\2|\\2\\s*+\\(\\s*+\\$_(?:POS|GE|REQUES)T)|\\@?eval(?&C)*+\\([^;]*?(?:file_get_contents|base64_decode)(?&C)*+\\()[^\\)]*+\\)++(?<Q>;[^\\?\\r\\n]*+(?:\\?(?!>)[^\\?\\r\\n]*+)*+[\\s\\}]*+)(?:return[^;]*+(?&Q))*+(?:$|\\?>\\s*+)/i"}, {"id": "469", "name": "php if md5 REQUEST eval REQUEST", "regx": "/<\\?(?:php)?(?<C>\\s++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/|(?:\\#|\\/\\/)[^\\n]++\\s++)*+(?:(\\$\\w++)(?&C)*+=[^;]+;(?&C)*+|if(?&C)*+\\((?&C)*+(?:isset|md5)(?&C)*+\\((?&C)*+\\$(?<R>_REQUEST|_POST|_GET|_COOKIE|\\{[\"'][^\\}]+\\}+)\\s*+\\[[^\\)]++\\)++(?:(?&C)|\\{)*+)*?(?:\\$\\w++(?&C)*+=(?&C)*+)?(?:assert|eval|\\2|\\$\\3(?<A>\\s*+\\[[^\\]]*+\\]++)++)(?&C)*+\\((?&C)*+\\$(?&R)(?&A)++(?&C)*+[^;]++;(?&C)*+(?:(?:die|exit|echo|print)[^;]*+;(?&C)*+|\\}(?&C)*+)*+(?:$|\\?>\\s*+(?:<form[^>]*+(?:>\\s*+<input[^>]*+)++>\\s*+<\\/form>\\s*+)?)/i"}, {"id": "470", "name": "if isset eval Variable function", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/.*\\s*|\\@?touch\\(.*\\)+;\\s*|foreach[^\\{]++[\\s\\{]+|\\$[a-z_0-9\\{\\$\\}]++(\\[[^\\]]++\\]|[\\.\\s]++|\\/\\*[a-z\\s]++\\*\\/)*+=[^;]++[;\\}\\s]++)*if([\\s\\(\\!]++|\\/\\*[a-z\\s]++\\*\\/)++(isset|\\$[a-z_0-9\\$\\{\\}]+(\\[[^\\]]+\\]|\\s++|\\/\\*[a-z\\s]++\\*\\/)*+)\\([^\\{]++[\\s\\{]++.*?\\s*(\\$[a-z_0-9\\{\\$\\}]+(\\s*\\[[^\\]]+\\])*\\s*=\\s*)?\\@?eval([\\s\\(]++|\\/\\*[a-z\\s]++\\*\\/)++.*?\\$[a-z_0-9\\{\\$\\}]+(\\[[^\\]]+\\]|\\s++|\\/\\*[a-z\\s]++\\*\\/)*+\\(.*?[\\s\\)]+;([;\\}\\s]++|\\/\\*[a-z\\s]++\\*\\/)++(exit[^;]++([;\\}\\s]++|\\/\\*[a-z\\s]++\\*\\/)++)?($|\\?>)/i"}, {"id": "471", "name": "error_reporting exec system passthru fopen REQUEST fwrite", "regx": "/<\\?.+?(set_time_limit\\(0|error_reporting\\(0|explode\\(['\"]wp-content)(?=.+?passthru\\s*\\()(?=.+?system\\s*\\()(?=.+?exec\\s*\\()((?=.+?curl_init\\s*\\()(?=.+?file_get_contents\\s*\\()|(?=.+?readdir\\s*\\()(?=.+?unlink\\s*\\()|(?=.+?fopen\\s*\\((\\$[a-z_0-9]+))(?=.+?\\3\\s*=\\s*\\$_POST))(?=.+?fwrite\\s*\\().+/is"}, {"id": "472", "name": "/function x eval x", "regx": "/(?:if\\s*+\\([^\\{]++\\{\\s*+)?function\\s++(\\w++)\\s*+\\([^\\{]++\\{\\s*+(?:(?:(?:\\$\\w++\\s*+=\\s*+['\"0-9\\-][^;]++|(\\$\\w++)\\s*+=\\s*+\\$(?:\\3|_COOKIE|_REQUEST|_GET|_POST))|(?:(?:\\2|isset\\(\\2[^\\)]*+)[\\)\\s]*+[\\&\\?]++[\\s\\(]*+)*+\\$(\\w++)\\s*+=\\s*+\\2)[^\\&;]*+[\\&;\\s\\(]++)*+\\@?(?:assert|eval)\\((?:(?:\\$\\3|\\2).+?\\1\\(|.+?\\1\\(\\$_(REQUES|GE|POS)T\\[).*?\\);[\\s\\}]*+/is"}, {"id": "473", "name": "GET_dl safe_mode end", "regx": "/^<\\?(?:php)?\\s*+(?:(?:\\@*+(?:session_start|error_reporting|set_time_limit)\\s*+\\([^;]++;\\s*+|\\?>\\s*+<\\?(?:php)?\\s*+|if[\\s\\(\\!]++function_exists[\\s\\(]++[^\\)]*+[\\)\\s\\{]++)*+(?:(?:if[\\s\\(\\!\\@]++(?:(?:isset|stripos)[\\s\\(]++)*+\\$_(?:GET|POST|REQUEST|SESSION|COOKIE|SERVER\\[[\"']HTTP_USER_AGENT)(?:['\"]\\]|\\s*+\\[['\"]\\w{2}['\"]\\])[^\\)]*+[^\\{]++[\\{\\s]++)++(?:die\\s*+\\([^;]++[;\\s\\}]++|.*?safe_mode))++)++.+?(?:\\?>(?:\\s*+<\\/div>\\s*+<\\/body>\\s*+<\\/html>)?|<\\/body>\\s*+<\\/html>['\";\\s\\}]*+(?:\\?>\\s*+)?)$/is"}, {"id": "474", "name": "GLOBALS 0 eval", "regx": "/(^my\\s+\\$([a-z_0-9]+)[O_0][\\s\\='\"]+;(?=.+?while\\(<([a-z_0-9]+)>\\))|<\\?[ph\\s]*(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/[^\\n]*\\n\\s*)*)(\\$(GLOBALS|\\2)?[\\[\\{'\"]*[O_0]+['\"\\]\\}\\s]*[\\=\\.\\s]+[^;]+;\\s*)+.*((eval|[\\$\\{\"']+(\\\\x[a-f0-9]{2}|[li1_])+[\\}'\"]+(\\s*\\[[^\\]]+\\])*)\\s*\\(.+\\)[\\);\\s]+)+(return[^;]*;|\\_++\\3\\_++\\s+(\\S{61}\\s+)+\\S*\\s*)*($|\\?>\\s*(\\~[a-z\\-\\+\\/0-9=]+\\s*$)?)/i"}, {"id": "475", "name": "keyspat viagra cialis", "regx": "/error_reporting\\(0\\);\\s*\\$keyspat[=\\s]+array[\\(\\s]+((['\"])(viagra|amoxicillin|cialis)\\2[\\s,]+){2}.+/is"}, {"id": "476", "name": "session_start error_reporting set_time_limit footer", "regx": "/<\\?[ph\\s]+(\\@*(session_start|error_reporting|set_time_limit)\\([^;]+;\\s*){2,}(echo[\\(\\s'\"]+<(\\!DOCTYPE )?HTML>.+<\\/HTML>['\";\\s]+function perms\\((\\$[a-z_0-9]+)[\\)\\s\\{]+\\$[a-z_0-9]+\\s*=\\s*fileperms\\(\\5\\);.+return[^;]+;[\\}\\s]+|.+?<\\?\\s*echo[\\(\\s]*\\$footer[\\);\\s]+)($|\\?>)/is"}, {"id": "477", "name": "md5tagged eval variable functions", "regx": "/(?:(?<C>\\@\\s*+|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++)*+(?:ini_set|error_reporting)\\s*+(?&C)*+\\([^;]*+;\\s*+)*+(?:(?&C)*+(?<V>(\\$\\w++)(?:\\s*+\\[[^\\]]*+\\]++)*+)[\\s\\.]*+(?&C)*+=(?:[\\s\\(]*+(?&C)*+(?:(?<S>\"(?:[^\"]*+(?<=\\\\)\")*+[^\"]*+(?<!\\\\)\"|'(?:[^']*+(?<=\\\\)')*+[^']*+(?<!\\\\)')|\\d++|(?&V)|strrev\\()(?&C)*+[\\s\\)]*+(?&C)*+[\\s\\.\\^\\-]*+(?:\\/(?!\\*)(?!\\/))?)++;\\s*+)+(?:(?:(?&C)*+(\\$\\w++)[\\s\\.]*+(?&C)*+=\\s*+)?(?&C)*+(?:eval|\\$\\w++)\\s*+(?&C)*+\\((?:(?:[\"']{2}|[,\\s]++|(?&C))*+\\$\\w++\\s*+(?&C)*+\\((?:[\\.\\s]*+(?:(?&V)|(?&S)|(?&C)))++[^\\)]*+[^;]++;\\s*+|[^\\)]*+[^;]*+;\\s*+(?&C)*+\\5\\s*+(?&C)*+\\([^;]++;\\s*+))++(?:(?&C)|\\$\\w++\\s*+(?:;|\\([^;]++;)\\s*+)*+/i"}, {"id": "478", "name": "if isset POST file_get_contents fopen fwrite fclose exit", "regx": "/(?:(?<=^<\\?php)|(?<=^<\\?))(?:\\s*+\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/|\\s*+(?:\\#|\\/\\/)[^\\n]++|\\s*+\\$\\w++\\s*+=\\s*+(?!\\$_(?:REQ|GET|POS|SER|SES))[^;]++;|\\s*+(\\$\\w++)\\s*+=\\s*+(?<G>\\$_(?:REQUEST|GET|POST|SERVER|SESSION)\\[)[^;]++;|[\\s\\@]*+(ignore_user_abort|ini_set|ob_start|ob_end_clean|session_start|set_time_limit|error_reporting)\\s*+\\([^\\)]*+\\)[^;]*+;)*+\\s*+if[\\(\\s\\!]++(?:(?:isset|empty)[\\(\\s]++((?&G))|[^\\)]*?\\1).+?(\\$\\w++)\\s*+=\\s*+\\@?f(?:ile_get_contents|open\\(.+?fgets)\\(\\s*+(?:\\w+?decode[\\(\\s]++)*+(?:\\2|\\5|\\4|['\"][htps:]+\\/\\/|__FILE__).+(?:file_put_contents\\(|fopen\\(.+?fwrite\\(.+?fclose\\()[^;]*+;[\\);\\}\\s]++(?:(?=\\?\\>)|[^\\?]+(?:(?!\\?\\>)\\?|return|extract\\(|die|echo|exit)[^;]*+;[\\s\\}]*+)++(?:(?=\\?\\>)|(?:\\/\\/[^\\n]*+\\n\\s*+){3,})/is"}, {"id": "479", "name": "function Array print exit", "regx": "/<\\?[ph\\s]+(\\$[_\\-\\>\\.a-z0-9\\{\\['\"\\]\\}]+\\s*=[^;]+;\\s*)*((function\\s+[a-z_0-9]+\\(.*?\\)\\s*\\{(?! \\};)(\\$[^=]+=\\s*['\"]{2})?|for[\\s(]+[^\\{]+\\{[^\\}]+\\}.*?|(return|global)\\s+(\\$\\{[^\\}]+\\}+)?[^;]*;|\\$\\{.+?\\}\\s*\\(.*?\\)+;|\\$\\{.+?\\}\\s*=\\s*array\\((([a-z_0-9]+\\(.*?\\)+,\\s*)+[a-z_0-9]+\\(.*?)?\\)+;|if\\s*\\(.*?\\)+\\s*\\{|else[\\s\\{]*|\\$[_\\-\\>\\.a-z0-9\\{\\['\"\\]\\}]+[\\s\\.]*=(\\s*chr\\(.*?\\)+|(.+?\\^){5,}|[^;]+\\^\\s*\\d+)[^;]*;|exit\\(.*?\\)+;|(\\$(\\{[a-z_0-9]+\\(.+?\\)\\}(\\s*\\[[^\\]]*\\]+)*|content|url)[\\s\\+\\.;\\}<>\\.]*)+=\\s*([\\@\\$\\{]*[a-z_0-9\\}]+(\\(.+?\\)|\\[.+?\\]))\\}*;|foreach.+?\\s+as\\s+[^\\)]+[^\\{]+\\{)[;\\s\\}]*){50,}(\\$(\\{[a-z_0-9]+\\(.+?\\)\\}(\\s*\\[[^\\]]*\\]+)*|content|url)[\\s\\+\\.;\\}<>\\.]*)+=\\s*([\\@\\$\\{]*[a-z_0-9\\}]+(\\(.+?\\)|\\[.+?\\]))[;\\}]*($|\\?>)/i"}, {"id": "480", "name": "php if isset GET echo if POST copy FILES", "regx": "/(?<=\\<\\?php)\\s*(echo[^;]*;\\s*|error_reporting\\(0\\);\\s*|\\/\\*.*?\\*\\/\\s*|\\$[a-z_0-9]+\\s*=[^;]+;\\s*|(\\/\\/|\\#)[^\\n]*\\n\\s*)*if[\\s\\(]+isset\\(\\$_(REQUES|GE|POS)T[^\\)]+[\\)\\s\\{]+((echo|print|\\$[a-z_0-9]+\\s*=)[^;]+;\\s*)+if[\\s\\(]+\\$_(REQUES|GE|POS)T[^\\)]+[\\)\\s\\{]+if[\\s\\(]+\\@?copy\\(([\\s\\,]*\\$_FILES(\\[[^\\]]+\\])+)+[\\)\\s]+\\{[^\\}]+((\\s*\\})+(\\s*else[\\s\\{]+[^\\}]+)?)+(\\s*(\\/\\/|\\#)[^\\n]*|\\s*\\/\\*.*?\\*\\/)*(\\s*error_reporting\\(0\\);\\s*function\\s+([a-z_0-9]+)\\s*\\(.+\\15[\\(\\s]+\\$_(REQUES|GE|POS)T.+echo[\\s\\(]*(['\"])<script.+?<\\/script>\\17;[\\s*\\}]+(?=\\/\\*|$|\\?>))?/is"}, {"id": "481", "name": "auth_pass FilesMan", "regx": "/<\\?(?:php)?+\\s*+(?=.*?\\$auth_pass)(?=.*?FilesMan)(?=.*?=[^;]{2000}).++$/is"}, {"id": "482", "name": "php set_time_limit file_get_contents REQUEST file_get_contents FILES fopen REQUESTfwrite", "regx": "/<\\?[ph\\s]+.+?((error_reporting|set_time_limit|date_default_timezone_set)\\([^;]+;\\s*|(\\$[o_0]+)\\s*=[^;]+;\\s*|(\\$[a-z_0-9]+)\\s*=[\\@\\s]*\\$_(POST|GET|REQUEST|SERVER)\\[[\"'][^;]+;\\s*)+((?=.+file_get_contents\\((\\$.+?)\\))(?=.+function (.+?)\\((\\3[,\\s]+\\7)\\))(?=.+\\$[a-z_0-9]+\\s*=\\s*([a-z_0-9]+\\()*\\8\\(\\3[,\\s]+(\\$.+?)\\))(?=.+\\11\\s*=[^;]*\\4)(?=.+fopen\\((\\$.+?),)(?=.+function (.+?)\\(\\3[,\\s]+\\12)(?=.+if[\\(\\s]+\\13\\(\\3[,\\s]+(\\$.+?),)(?=.+\\14\\s*=\\s*\\$_(REQUES|GE|POS)T\\[)|.+?file_get_contents\\(\\$_(REQUES|GE|POS)T.+?file_get_contents\\(\\$_FILES\\[).+?fopen\\((\\$_(REQUES|GE|POS)T|\\12,).+?fwrite\\(.+?(\\?>)/is"}, {"id": "483", "name": "clearstatcache here die", "regx": "/<\\?[ph\\s]*(error_reporting\\(0\\);\\s*)*(if[\\s\\(\\!]+isset[\\s\\(]+\\$_(REQUES|POS|GE)T\\[['\"][0-9_a-z]+['\"]\\][\\)\\s\\{]+((echo|require|\\$[0-9_a-z]+\\s*=\\s*(\\$|(['\"]).*?\\7))[^;]+;[\\s\\}]+)*)+(\\$[0-9_a-z]+\\s*=\\s*)?(\\$wpdb\\->query\\(\\s*|mysql_connect[\\s\\(]+\\$_(REQUES|POS|GE)T\\[['\"][0-9_a-z]+['\"]\\].+?)\"INSERT\\s+INTO[\\s\\`\"\\.\\$0-9_a-z\\-\\>]+users[\\s\\`]+\\(.+((mysql_close\\([^\\)]*\\)|clearstatcache.+here;\\s+die|(\\$[0-9_a-z]+)\\s*=\\s*\\@file_get_contents\\(.+\\13[\\)\\s]*);[\\}\\s]+)+($|\\?>)/is"}, {"id": "484", "name": "unset self", "regx": "/<\\?[ph\\s]*(\\@?chmod\\([^\\)]+\\);\\s*)*if[\\s\\(\\!]+(isset|empty)[\\s\\(]+\\$_(REQUEST|GET|POST|COOKIE)[^\\{]+[\\{\\s]*(\\$[a-z_0-9\\{\\}]+)\\s*=.+?mkdir[\\s\\(\"'\\.\\/]+\\4[\\);\\s]+foreach.+?\\{[^\\}]+\\}\\s*(if[\\s\\(\\!]+is_dir[^\\)]+\\)+[\\{\\s\\@]*mkdir[\\s\\(]+[^\\)]+\\)+;[\\s\\}]*)+.*?(unlink\\([^\\)]+\\);\\s*)+($|\\?>)/is"}, {"id": "485", "name": "if isset REQUEST touch move_uploaded_file", "regx": "/(?=(?:(?<=\\}|;|<\\?|<\\?php|\\*\\/)\\s*+\\{\\s*+)?(?:if[\\(\\s\\!]++(?:(?:isset|empty)[\\s\\(]++\\$_(?:REQUEST|GET|POST|FILES))[^\\{]++\\{\\s*+(?:(?:(?<V>\\$\\w++(?:\\s*+\\[[^\\]]*+\\]++)*+)[\\.\\s]*+=(?<S>[\\s\\.]*+(?:\"(?:[^\"]*+(?<=\\\\)\")*+[^\"]*+(?<!\\\\)\"|'(?:[^']*+(?<=\\\\)')*+[^']*+(?<!\\\\)'|[0-9]++))++[;\\s\\}]++)*+(?:(?:((?&V))[\\.\\s]*+=[^;]*?(?:\\3|\\4|\\5)|((?&V))[\\.\\s]*+=[^;]*?(\\$_FILES))[^;]*+;[\\s\\}]*+)++)++(?<A>touch\\s*+\\(\\s*+(?:\\3|\\4|\\5)[^;]*+;[\\s\\}]*+|((?&V))[\\.\\s]*+=(?&S)+[;\\s]++|(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/\\s*+|(?:\\#|\\/\\/)[^\\n]*+\\s++)|if[\\(\\s\\!]++(?:file_exists[\\s\\(]++)?(?:\\1|\\3|\\4|\\5|\\7)[^\\{]++\\{\\s*|(?:\\}\\s*+|else[\\s\\{]++)++|echo[\\(\\s'\"]+[^;]*+;[\\s\\}]*+)*+)++(?:\\@|if[\\(\\s]++)*+move_uploaded_file\\s*+\\([^\\;]++;(?:[\\}\\s]*+)++)[^\\{]*+(?<B>\\{(?:[^{}'\"\\/\\#]++|\\/[^\\*\\/]|(?&C)|\"(?:\\\\\"|[^\"])*+(?<!\\\\)\"|'(?:\\\\'|[^'])*+(?<!\\\\)'|(?&B))*\\})/i"}, {"id": "486", "name": "php if isset REQUEST eval file_put_contents include unlink", "regx": "/<\\?[ph]*+(?:(?:(?:\\@?(?:ignore_user_abort|set_time_limit|ini_set|error_reporting|DEFINE|\\4)\\s*+\\([^\\)]*+\\)[\\s\\)]*+|(?<C>[\\s\\}]++|\\/\\/[^\\n]*+|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/)|global(?&C)++(\\$\\w++)|(\\$\\w++)(?&C)*+=|print(?:_r)?(?&C)*+\\(|echo\\s*+[\\('\"])[^;]*+;(?&C)*+)++|(?:try\\s*+|function(?&C)++(\\w++)[^\\{]*+)\\{(?&C)*+)*+(?:(?:(?:for|while)(?&C)*+\\([^\\)]++|if[\\s\\(\\!]++(?:(?:empty|strlen|isset)\\(\\$_(?:REQUES|GE|POS)T|[\\$\\w]*+[\\s\\=]*+[scanoperd]+dir[\\(\\s]++(\\$\\w++)))[^\\{]++\\{(?&C)*+)++.*?(\\$\\w++)(?&C)*+[\\.\\s]*+=.*?(?:curl_[^\\(]+|eval|file_get_contents|\\2)(?&C)*+\\((?&C)*+(?:\\3|\\6|\\5|ord(?&C)*+\\().+?file_..t_contents[\\( ]++([\\$\\w\\['\"\\]\\.]+).+?(?:echo[\\s\\(]+\\3|include(?:_once)?[\\(\\s]++\\7.+?(?:(\\$\\w++)(?&C)*+=(?&C)*+\\7)?unlink[\\(\\s]++(?:\\7|\\8)|curl_init\\(.+?curl_setopt\\([^,]++,\\s*+CURLOPT_URL[,\\s]++\\7|\\}\\s*+catch\\s*+\\(\\s*+Exception\\s*+\\$\\w++[\\)\\s]*+\\{[^\\}]*+\\}\\s*+|file_get_contents[\\s\\(\"']++http[^;]++;[\\s\\/\\@]*+file_put_contents\\(.+?error_reporting[\\(\\s0\\)]++;\\s*+).*+/is"}, {"id": "487", "name": "php chdir REQUEST getcwd move_uploaded_file", "regx": "/<\\?.+?(chdir[\\(\\s]+\\$_(REQUES|POS|GE)T\\[|error_reporting[\\(\\s]+0).+?(\\$[a-z_0-9]+)[=\\s\\@]+getcwd[\\s\\(]+.+?((\\$[a-z_0-9]+)[=\\s\\@]+\\$_(REQUES|POS|GE)T\\[[^;]+;\\s*if[\\s\\(]+)?(copy|move_uploaded_file)[\\s\\(]+(.+?unlink[\\s\\(]+.+?scandir[\\s\\(]+(\\3|\\5)|\\$_FILES\\[[^,]+[,\\s]+(\\$_\\2|\\3|\\5))(.+changepass\\()?(.+?\\?>\\s*<\\?[ph\\s]*\\})*.+?($|\\?>(?!\")(\\s*<\\/(body|html)>)*)/is"}, {"id": "488", "name": "error_reporting ini_set unlink FILE", "regx": "/\\@?error_reporting\\(0\\);\\s*(ini_set\\(.+\\@?unlink\\(__FILE__\\);|(if[\\s\\(\\!]+(isset\\(|file_exists\\()?\\s*\\$_(SERVER|REQUEST|GET|POST|FILES)\\[[^\\]]+\\]+[^\\)]*\\)[\\)\\s\\{]*(echo[^;]+;\\s*)*)+\\@?copy\\(([\\s\\,]*\\$_FILES(\\[[^\\]]+\\]+)+)+[\\)\\s]+;[\\s\\}]+\\?>\\s*(<(form|input)[^>]+>\\s*)+<\\/form>\\s*(<\\?[ph\\s]+\\}+)?)/is"}, {"id": "489", "name": "include GET", "regx": "/(?:(?:\\s*+(\\$\\w++)\\s*+=\\s*+([\"'])(?:(?:[^\\\\'\"]*+|\\\\\\\\|\\\\\\2)*+(?:\\\\(?:x[a-f\\d]{2}|\\d)|(?<=\\.css)(?=\\2)))++[^\"']*+\\2;|\\s*+(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/)|\\s*+(\\$\\w++)\\s*+=\\s*+(?:substr\\()?\\1[^;]*+[;\\s]++|\\s*+strpos\\s*+\\([^;]++;++|\\s*+\\$\\w++\\s*+=[\\s\"']*+[\\w\\s]*+['\";\\s]++)*+\\s*+(?:if[\\s\\(\\!]++(?:(?:is|file)\\w++[\\s\\(]++(?:\\1|\\4)|\\d[^\\)]*+)[\\)\\s\\{]++(?:\\w++[\\s\\('\"]++\\1[^;]*+;\\s*+(?:[\\}\\s]*+else[\\s\\{]*+)?)*+)?\\@?(?:include|require)(?:_once)?\\s*+(?&C)*+[\\(\\s]*+(?:base64_decode\\s*+\\([^\\)]++|\"(?:[\\w\\/_\\-\\.]*+\\\\(?:x[a-f0-9]{2}|[0-9]{2,3}))++[^\"]*+\"|\\1|\\4|\\$_(?!REQUEST\\['target'\\];)(?:POS|REQUES|GE)T(?:\\s*+\\[[^\\]]++\\]|\\s*+\\{[^\\}]++\\})++\\s*+)++[\\s\\)]*+;(?:\\s*+(?:\\3|\\}))*+)++/i"}, {"id": "490", "name": "php function file_get_contents fwrite unlink __FILE__", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|[a-z_0-9]+\\([^)]*[^;]+;\\s*|\\$[a-z_0-9]+\\s*=[^;]+;\\s*|\\/\\/[^\\n]*\\n\\s*)*function\\s+([a-z_0-9]+)\\(.+((\\$[a-z_0-9]+)\\s*=[\\@\\s]*file_get_contents\\((.+(\\$[a-z_0-9]+)\\s*=[\\@\\s]*(base64_decode|file_get_contents)\\(\\5)*.+file_put_contents\\([^,]+,\\s*(\\5|\\7)[^;]+;[\\}\\s]*(\\$[a-z_0-9]+\\s*=[^;]+;\\s*)*|(file_..t_contents|mkdir)\\(.+fopen\\(.+fwrite\\(.+fclose\\((.+?function\\s+([a-z_0-9]+)\\()*.+unlink\\(\\s*(\\$_SERVER[\\s'\"\\[\\{]+SCRIPT_FILENAME|__FILE__)[^\\)]*[\\);\\s\\}]*)((\\3\\([^;]+|\\13\\([^;]+|echo[^;]*|exit[^;]*)[\\);\\s\\}]*)+($|\\?>)/is"}, {"id": "491", "name": "auth_pass FilesMan safe_mode eval", "regx": "/<\\?(?=.*\\$auth_pass)(?=.*FilesMan)(?=.*safe_mode)(?=.*(eval|netstat)\\().+/s"}, {"id": "492", "name": "php error_reporting ini_set set_time_limit if isset REQUEST file_get_contents file_put_contents unlink ie", "regx": "/<\\?[ph\\s]+(\\/\\*[^\\*]*(\\*[^\\*\\/]*)+\\/\\s*|\\/\\/[^\\n]*\\s+)*(ignore_user_abort\\s*\\([^;]*;\\s*|\\$[a-z_0-9]+\\s*=[^;]*[;\\s]+)+((else[\\s\\{]*|if[\\s\\(]+[^;]+|\\$[a-z_0-9]+\\s*=\\s*)*(error_reporting|ini_set||set_time_limit|while(?=\\(1\\)))\\s*\\([^;]+;[\\}\\s]+)+((if[\\s\\(]+(is_writable|isset|file_exists)[\\s\\(]+\\$(_REQUEST|_GET|_POST|path)[^;]+)?(\\@?(file_get_contents|file_put_contents|unlink|chmod)\\s*\\([^;]+;([\\}\\s]*(if[\\s\\(]+[^\\)]+\\)+|else)?[\\{\\s]*(die|print|sleep|echo)[^;]*;)*[\\}\\s]*)+[\\}\\s]+){3,}.*?($|\\?>)/is"}, {"id": "493", "name": "php create_function Variable function", "regx": "/<\\?[ph\\s]*(\\/\\*([^\\*]*\\*[^\\/])*[^\\*]*\\*\\/\\s*|\\/\\/.*\\s*)*((\\$[a-z_\\-0-9]+)\\s*=.+;\\s*|if\\s*\\([^\\{]+\\{\\s*)+((\\$[a-z_\\-0-9]+)\\s*=\\s*)?create_function\\([^,]+,\\s*(\\4[^;]+;[\\s\\@]*\\6\\(|\"(\\\\([0-9]{2,3}|x[0-9a-f]{2}))+\")[^\\)]*[\\);\\s\\}]+($|\\?>)/i"}, {"id": "494", "name": "if isset REQUEST eval or file_put_contents", "regx": "/<\\?[ph\\s]+(function ([a-z_0-9]+)\\([^\\{]+[\\{\\s]+.+return[^;]*;[}\\s]+)?if[\\s\\(]+isset[\\s\\(]+\\$_(REQUES|GE|POS)T(\\s*\\[[^\\]]+\\])+[\\)\\s\\{]+(\\$[a-z_\\-0-9]+\\s*=\\s*.+?;\\s*)*(((header|unlink)\\([^;]+[;\\s]+){2,}[\\}\\s]*else[\\{\\s]+(\\$[a-z_\\-0-9]+\\s*=\\s*.+?;\\s*)*((\\$[a-z_\\-0-9]+\\s*=\\s*)?\\@?(eval|\\2)\\(.+?\\);\\s*){2,}|file_put_contents\\([^,]+,\\s*base64_decode\\(.+?\\);)[\\}\\s]*(\\?>|$)/is"}, {"id": "495", "name": "php echo passthru _REQUEST", "regx": "/(?<=<\\?php\\s)[\\s\\@]*+(?:echo|print)[\\s\\(]*+(?:(['\"]).*?\\1[\\)\\s]*+;\\s*+)?(?:(\\$\\w++)\\s*+=\\s*+)?(?:\\@?(?:passthru|exec|system|\\$\\w++)\\s*+\\(|\\`)\\$_(?:REQUES|GE|POS)T[\\s\\[]++[^\\]]++[\\]\\s\\)\\`]++;(?:[\\s\\@]*+(?:echo|print)[\\s\\(]*+[^;]*?\\2[^;]*+;)?/i"}, {"id": "496", "name": "php if is_dir file_get_contents if file_put_contents echo", "regx": "/<\\?[ph\\s]+(\\/\\/[^\\n]*[\\r\\n]+|\\$[a-z_0-9]+\\s*=[^;]*;\\s*|error_reporting\\([^;]+;\\s*)+.+?(include[^;]*wp-class-headers.php.+?|if[\\(\\s]+is_dir[^\\{]+\\{(\\s*\\$[a-z_0-9]+\\s*=file_|[^\\}]+\\}\\s*)get_(all_dirs\\(.+?|contents\\([^;]*;\\s*\\$[a-z_0-9]+\\s*=[^;]*;\\s*(if[\\(\\s]+)?))file_put_contents\\(.+?((touch\\(.+|echo[^;]*;\\s*|\\}(\\s*else[\\s\\{]+)?)\\s*)+($|\\?>)/is"}, {"id": "497", "name": "move_uploaded_file _FILES __FILE__", "regx": "/<\\?(?:php)?\\s*+(?:(\\$\\w++)\\s*+=\\s*+\\$_FILES\\[[^;]++;\\s*+(\\$\\w++)\\s*+=[^;]++;\\s*+if[\\s\\(]++file_exists[\\s\\('\"\\.\\/]++\\2[\\s\\('\"\\)\\s]++unlink[\\s\\('\"\\.\\/\\)]++\\2)?.*?(?:move_uploaded_file[\\s\\(]++(?:\\$_FILES\\[[^,]++,\\s*+__FILE__|\\1)|system\\s*+\\(\\s*+['\"]mv ['\"]\\s*+\\.\\s*+\\$_FILES\\[).*?(?:$|\\?>)/is"}, {"id": "498", "name": "php function die setcookie if empty if isset form move_uploaded_file", "regx": "/<\\?[ph\\s]+((\\$[a-z_0-9]+)\\s*=[^;]+;\\s*)*(if[\\(\\s]+(\\2|\\!empty[\\(\\s]+)[^\\{]+\\{\\s*((((\\$[a-z_0-9]+)\\s*=\\s*)?(\\2\\(.*?\\)+|\\$_(REQUES|GE|POS)T[^;]+);\\s*|if[\\(\\s\\!]+(isset[\\(\\s]+.+?\\)+\\s+[a-z_0-9]+|\\8[^\\}]*)))+[\\}\\s]*|function [a-z_0-9]+\\([^\\{]+\\{\\s*(die\\(.+?\\);\\s*|(\\$_COOKIE.+?;\\s*|setcookie\\(.+?\\);\\s*){2})\\}\\s*)+([\\s\\{]*(\\$[a-z_0-9]+)\\s*=\\s*\\$_(REQUEST|GET|POST|FILE)[^;]+;[\\s\\}]*)*(\\?>\\s*|echo[\\s\\(]*(['\"]))(<(form|input)[^>]*>[a-z\\:0-9\\s]*)+<\\/form>(\\s*<\\?[ph\\s]+|['\"];\\s*)(\\$[a-z_0-9]+\\s*=[^;]+;\\s*|if[\\s\\(]+)*(move_uploaded_file\\([^\\;]+|(\\$[a-z_0-9]+)\\s*=\\s*\\$[a-z_0-9]+\\s*\\(.*?\\));[\\s\\}]*($|\\?>)/i"}, {"id": "499", "name": "php if REQUEST add_action function global if get_option return ob_start wp_rewrite->flush_rules wp_cache_flush", "regx": "/<\\?[ph\\s]+(if\\s*\\([^\\)]*\\$_(POS|REQUES|GE)T[^\\{]+[\\{\\s]+|add_action\\s*\\([^,]+,\\s*(['\"])([a-z_0-9]+)['\"\\);\\s\\}]+|function\\s+\\4[^\\{]+[\\{\\s]+)*(((include|require)(_once)?\\([^;]+;\\s*|if[\\s\\(\\!]+username_exists\\([^\\{]+[\\{\\s]+)*(\\$[a-z_0-9]+)\\s*=\\s*wp_create_user\\(.+set_role[\\(\\s'\"]+administrator|(global[^;]*;\\s*)?if[\\s\\(\\!]+get_option\\([^\\)]+[\\)\\s\\{]+return[^;]*;[\\s\\}]+ob_start\\(\\);\\s*\\?>.+\\$wp_rewrite->flush_rules\\(\\);\\s*wp_cache_flush\\()+[\"'\\s\\);\\s\\}]+($|\\?>)/is"}, {"id": "500", "name": "if isset REQUEST FILE stripslashes REQUEST", "regx": "/if[\\s\\(]+isset[\\s\\(]+\\$_(GE|POS|REQUES)T(\\s*\\[[^\\]]*\\]+|\\s*\\{[^\\}]*\\}+)+[\\s\\)\\{]+((\\$[a-z_0-9]+)\\s*=\\s*\\$_(GE|POS|REQUES)T(\\s*\\[[^\\]]*\\]+|\\s*\\{[^\\}]*\\}+)+[^;]*[;\\s]*)*(assert|eval|\\$[a-z_0-9]+)\\s*\\(+stripslashes\\(\\$_(GE|POS|REQUES)T(\\s*\\[[^\\]]*\\]+|\\s*\\{[^\\}]*\\}+)+[\\s\\)]+;[\\}\\s]*/i"}, {"id": "501", "name": "error_reporting function error_404 ttp_request_custom getIp getUseragent convertIpToString http_build_query file_get_contents fwrite long2ip", "regx": "/<\\?.+?error_reporting\\(((?=.+?get[_]*Ip\\()(?=.+?function error_404\\()|(?=.+?function _[a-z0-9]+\\(){11,})((?=.+?stream_context_create\\()|(?=.+?request_custom\\())(?=.+?http_build_query\\()(?=.+?file_get_contents\\()((?=.+?file_get_contents\\(){4,}|(?=.+?header\\())(?=.+?long2ip\\().+/is"}, {"id": "502", "name": "if isset REQUEST foreach array eval", "regx": "/if[\\s\\(]+([a-z_0-9]+\\s*\\(\\s*)*\\$_(REQUES|GE|POS)T\\[[^\\{]+\\{\\s*(\\$[a-z_0-9]+\\s*=[^;]+;\\s*|.*?foreach\\(array.*?)*[\\s\\@]*(system|eval)\\(.+\\s*(exit[^;]*;\\s*)*\\}/i"}, {"id": "503", "name": "if empty SERVER HTTP_USER_AGENT set_time_limit move_uploaded_file return", "regx": "/(?:<\\?(?:php)?\\s*+|if[\\s\\(\\!]++(?:file_exists\\s*+\\(|isset[\\s\\(]++\\$_(?:REQUES|GE|POS)T\\[[^\\]]++\\]++)[^\\{]++\\{[^\\}]++[\\s\\}]++|\\?>\\s*+|<[^\\?][^\\n]*>\\s*+){2,}(?:(?:print|echo)[^;]*+;\\s*+|(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/\\s*+|(?:\\#|\\/\\/)[^\\n]*+\\s++))*+if[\\s\\(\\!]+(?:\\@?is_uploaded_file|empty[\\s\\(]++\\$_SERVER\\[[\"']HTTP_USER_AGENT[\"']\\][\\)\\s]++.+?set_time_limit).+?move_uploaded_file\\s*+\\(.+(?:return\\s*+\\$\\w++|echo[\\(\\s'\"]++[^;]++|touch\\s*+(?&C)*+\\([^;]++);[\\s\\}]++(?:$|\\?>\\s*+(?:<\\/(?:body|html)>\\s*+)*+)/is"}, {"id": "504", "name": "fwrite unlink eval chmod POST phpinfo move_uploaded_file exec system passthru", "regx": "/<\\?.+?error_reporting\\(0\\)(?=.+?php(_uname|info)\\()(?=.+?eval\\().+?(chmod|getcwd)\\((\\$_POST|[^\\{]++\\{\\s*\\$[a-z_0-9]+\\s*=\\s*\\$_FILES)\\[.+?move_uploaded_file\\((.+?((\\$[a-z_0-9]+)\\s*=['\"htpsf\\:\\/\\.\\s]*\\$_(REQUES|GE|POS)T(?=.+?function\\s+([a-z_0-9]+)\\s*\\((\\$[a-z_0-9]+))|=\\s*\\8\\s*\\(\\6|CURLOPT_URL[,\\s]+\\9|exec\\(|system\\(|passthru\\()){3}.+/is"}, {"id": "505", "name": "if function_exists function variable function", "regx": "/<\\?(?:php)?\\s*+(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)(?:[^\\n\\?]++|\\?(?!\\>))*+(?:\\s|\\?>)++)*+(?:(?<V>\\$\\w++(?:\\s*+\\[[^\\]]++\\]++)*+)[\\.\\s]*+=(?:[\\s\\.]*+(?:(?&V)|\"(?:[^\"]*+(?<=\\\\)\")*+[^\"]*+(?<!\\\\)\"|'(?:[^']*+(?<=\\\\)')*+[^']*+(?<!\\\\)'))++;\\s*+|if[\\(\\s\\!]++function_exists[\\(\\s]++['\"][^\\{]++\\{\\s*+)*+function\\s++(\\w++)\\s*+\\([^\\}]++(?:.+?\\3\\s*+\\([^;]++[;'\"\\)\\}\\s]++)++(?:(?:(\\$\\w++)\\s*+=\\s*+)?(?&V)\\s*+\\([^;]++[\"'\\);\\}\\s]++|eval[\\s\\(\"]++\\4[\"\\);\\s\\}]++)++(?&C)*+(?:$|\\?>)/is"}, {"id": "506", "name": "require_once wp-config ini_set mysql_query UPDATE users SET user_pass", "regx": "/^.*<\\?[ph\\s]*(include|require)(_once)?\\(.+wp-config\\.php[\"'\\s\\);]+(\\@?ini_set\\([^\\)]+[\\);\\s]+)+.+?(mysql_query\\(['\"]UPDATE[\\s\\{'\".]+\\$table_prefix[\\s\\}'\".]+users\\s+SET\\s+user_pass.+){2,}$/is"}, {"id": "507", "name": "if rename or file_put_contents touch", "regx": "/<\\?[ph\\s]+if[\\s\\(]+isset[\\s\\(]+\\$_(REQUES|GE|POS)T(\\s*\\[[^\\]]+\\])+[\\)\\s\\{]+[^;]+;\\s*[^\\}]*\\}[\\}\\s]*(\\$[a-z_0-9]+)[\\s*\\.=]+base64_decode\\(.+?file_put_contents\\(([^,]+),\\s*\\3.+?unlink\\(.+?\\(\\4[\\);\\s]+.+/is"}, {"id": "508", "name": "eval REQUEST alone", "regx": "/(?:(?:(\\$\\w++)(?<C>\\@++|\\s++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++)*+=[^;]*?(?<R>\\$_(?:REQUEST|GET|POST|SERVER||COOKIE|HEADERS)(?&C)*+(?:\\[[^\\]]++\\](?&C)*+)++)[^;]*+;(?&C)*+(?<F>eval|system|echo(?&C)++base64_decode)(?:(?<P>(?&C)*+\\((?&C)*+)++stripslashes)?+(?&P)++\\1|\\@?+(?&F)(?&P)++(?&R))(?&C)*+(?:\\)(?&C)*+)++(?:;(?&C)*+)++)++/i"}, {"id": "509", "name": "FilesMan preg_replace .", "regx": "/<\\?[ph\\s]*(\\/\\*.+?\\*\\/\\s*)*(\\$[a-z_0-9]+\\s*=[^;]+;\\s*)*\\$[a-z_0-9]+\\s*=[\\s\"']*F[\"'\\.\\s]*i[\"'\\.\\s]*l[\"'\\.\\s]*e[\"'\\.\\s]*s[\"'\\.\\s]*M[\"'\\.\\s]*a[\"'\\.\\s]*n[\"'\\s]*;(\\s*\\$[a-z_0-9]+\\s*=[^;]+;)*\\s*(\\$[a-z_0-9]+|preg_replace)\\([^\\)]*[\\);\\s]+($|\\?>)/is"}, {"id": "510", "name": "html head unzip FILES unlink form", "regx": "/^(?:<(?:\\!DOCTYPE\\s++)?html[^>]*+>\\s*+)++<head>\\s*+(?:<script.+?Hacked\\s++by.+?<iframe .+?<audio.+?|.+?(?:fileperms\\s*+\\((?:(?=.+?(\\$\\w++)\\s*+=\\s*+fopen\\s*+\\(\\s*+(\\$\\w++)[^\\)]*+[\\);\\s\\{\\}if\\(\\@]++(?<=f)write\\s*+\\(\\s*+\\1)?+.+?\\2\\s*+=[^;]*?\\$_(?:REQUES|GE|POS)T|(?:.+?posix_get[pwugr]+id\\s*+\\(){2,}).+?move_uploaded_file\\s*+\\(.+?unlink\\s*\\(.+?(?:exec\\s*+\\(.+?\\breaddir\\(.+?\\?|\\?>\\s*+<script[^>]+?src=['\"][htps:]*+\\/\\/[^>]++>\\s*+<\\/script)|unzip[\\s\\(]++\\$_FILES\\[.+?unlink[\\s\\(]++\\$.+\\?>\\s*+<\\/form)>\\s*+)<\\/body>\\s*+<\\/html>\\s*+$/is"}, {"id": "511", "name": "if array_keys GET file_get_contents if unlink fopen fwrite fclose", "regx": "/<\\?[ph\\s]+(\\/\\/[^\\n]*\\n\\s*|\\/\\*[^\\*]*(\\*[^\\*\\/]*)+\\/\\s*|\\@?(ignore_user_abort|ini_set|set_time_limit|error_reporting)\\(.*?\\);\\s*|\\$[a-z_0-9]+\\s*=[^;]+;\\s*)*(((if\\s*\\(|else)[^\\{]*\\{\\s*)?function\\s+.+?return[^;]*;[\\s\\}]+)+((\\$[a-z_0-9]+\\s*=\\s*)?\\$[a-z_0-9]+\\([^;]+[\"'\\);\\}\\s]*)+.+?(f(open|write|close)\\(.+?){3,}include\\(.*?\\);\\s*unlink\\(.*?\\);\\s*(\\$[a-z_0-9]+\\s*=[^;]+;[\\}\\s]*)*($|\\?>)/is"}, {"id": "512", "name": "display_errors file_get_contents _REQUEST filename update_code fwrite unlink", "regx": "/(?:(?<=<\\?php\\s)|(?<=<\\?\\s))(?:[\\s\\@]*+(?:error_reporting\\(0[^;]++|display_errors[^;]++|ini_set\\([^;]++|global\\s++\\$\\w++|for(?:each)?[\\s\\(]++\\$\\w++[^\\{]++|function\\s++(?:\\w++)[\\s\\(]++\\$(\\w++)[^\\{]++|if[\\s\\(]++[^\\)]++[\\)\\s]++|\\$(\\w++)\\s*+=\\s*+(?!urldecode|file_get_)[^;]++)(?:\\{|;))++\\s*+(\\$\\w++)\\s*+=\\s*+(?:file_get_contents\\([^;]*|urldecode\\()\\$(?:\\1|\\2|_POST|_GET|_REQUEST).+?(?:\\$\\w++\\s*+\\(\\s*+([^\\s,\\)]++).+?include(?:_once)?|fwrite)\\s*+\\(\\s*+([^\\s,\\)]++).+?unlink\\(\\s*+(?:__FILE__|\\5|\\4)[^;]*+[\\);\\}\\s]++(?:(?:die|echo|exit|\\$\\w++\\s*+=)[^;]*+[\\);\\}\\s]++)*+(?:(?=\\?\\>)|(?=\\/[\\*\\/])|(?=require|include)|$)/is"}, {"id": "513", "name": "php Vars Array base64_decode function Variable Function", "regx": "/(\\$[\\$\\{]*[a-z_0-9]+\\}*(\\s*\\[[^\\]]+\\]|\\s*\\{[^\\}]+\\})*)\\s*=\\s*(chr|array[\\(\\s]+base64_decode)\\(([^;]+;\\s*if\\s*\\([\\s\\!\\@]*|.+?function ([a-z_0-9]+)\\s*\\(.+?)\\1\\s*(\\[[^\\]]+\\]\\s*)*\\((.+(\\$[\\$\\{]*[a-z_0-9]+\\}*(\\s*\\[[^\\]]+\\]|\\s*\\{[^\\}]+\\})*)\\s*=\\5\\()?.+?(echo|die|print)[\\s\\(]*(\\3|\\8)[^;]*;[\\s\\}]*/is"}, {"id": "514", "name": "exec system passthru fwrite Variable Function REQUEST", "regx": "/<\\?(?=.*passthru\\(|.*exec\\(|.*system\\().*?(((\\$[a-z_0-9]+)\\s*=\\s*([a-z_0-9]+\\(\\s*)*\\$_(REQUEST|GET|POST|SERVER|COOKIE)\\[[^;]+;[\\s\\}]*(else\\s*\\{[^\\}]+\\}[\\s\\}]*)?)+.*(?<!new )\\$[a-z_0-9]+(?!\\( \\$key \\) \\);)\\([\\s\"]*\\3|\\$[a-z_0-9]+\\(\\$_(REQUES|GE|POS)T).+?($|\\?>)/is"}, {"id": "515", "name": "php if function_exists file_put_contents fopen chmod system unlink", "regx": "/<\\?(.+?(touch|header)\\().+?if[\\s\\(\\!]+function_exists\\([\\s'\"]+(file_put_contents)[\"'\\s\\)\\{]+function\\s+\\3\\(.+?fopen\\(.+?\\3\\(.+?chmod\\(.+?system\\(.+(unlink|exit).+/is"}, {"id": "516", "name": "base64_decode file_put_contents chmod touch fsockopen curl_exec ob_start", "regx": "/\\/\\/\\s*istart.+?(\\$[a-z_0-9]+)[\\s*\\.=]+base64_decode\\(.+?file_put_contents\\([^,]+,\\s*\\1.+?chmod\\(.+?touch\\(.+?(fsockopen\\(.+?fgets\\(|curl_init\\(.+?curl_exec\\().+?ob_start\\(.+?\\/\\/iend[^\\n\\?]*/is"}, {"id": "517", "name": "if isset REQUEST eval", "regx": "/<\\?[ph\\s]+((\\$[a-z_0-9]+\\s*=[^;]+;\\s*)*(if[\\s\\(]+isset|for(each)?)[\\(\\s]+\\$_(REQUES|GE|POS)T[^\\)]+\\)[\\)\\s\\{]+(\\$[a-z_0-9]+[\\.=\\s]+\\$_(REQUES|GE|POS)T\\[[^\\]]+\\][;\\s]+|if\\s*\\([^;]+;[\\s\\}]*function\\s+[a-z_0-9]+\\(.+return[^;]*;[\\}\\s]*(\\$[a-z_0-9]+\\s*=[^;]+;\\s*)*)+((\\$[a-z_0-9]+[=\\s]+)?\\@?(eval|file_put_contents|fopen|fwrite|fclose)\\([^\\)]+\\);\\s*)+((echo|exit)[^\\;]*;\\s*)*[\\}\\s]*(else[\\s\\{]*)?)+($|\\?>)/i"}, {"id": "518", "name": "error_reporting eval curl_init file_get_contents file_put_contents include unlink", "regx": "/\\$[a-z_0-9]+[=\\s]+__FILE__;\\s*\\$[a-z_0-9]+\\s*=[^;]{2000}.*?error_reporting\\(.+?eval\\(.+?(curl_init|file_get_contents).+?file_put_contents\\(.+?include.*?unlink\\(.*?\\);\\s*\\}\\s*else\\s*\\{[^\\}]+[\\}]+/is"}, {"id": "519", "name": "set_error_handler eval file_get_contents", "regx": "/<\\?(?:php)?(?:(?<C>(?:\\#|\\/\\/)[^\\r\\n]*+[\\r\\n]++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/|\\s++)|(?:error_reporting|set_error_handler|ini_set)(?&C)*+\\([^;]*+;|null;|=|\\@)*+(echo[\\s\\(]++\\@?file_get_contents[\\(\\s]++(chr\\(\\d++[\\s\\)\\.]++)++|eval(?&C)*+\\([^\\)]*?file_get_contents(?&C)*+\\((?:(?:\"[^\"]*+\"|'[^']+')((?&C)|[\\.\\)])++)++);(?&C)*+(?:$|\\?>\\s*+)/is"}, {"id": "520", "name": "if for unset wp_wp form", "regx": "/<\\?[ph\\s]*(\\$([a-z_0-9]+)\\s*=[^;]+;\\s*)*(\\$(((l|wp)[_]*){2})\\s*=[^;]+;\\s*)+(if|for|unset|(\\$[a-z_0-9]+\\s*=\\s*)?\\$(\\2|\\4))\\s*\\(\\$(\\2|\\4)[^;]+;\\s*.*?\\?>\\s*<form.+?name=['\"](((l|wp)[_]*){2})['\"].+?<\\/form>/is"}, {"id": "521", "name": "php class viaWorm", "regx": "/<\\?[ph\\s]+(\\/\\*.*?\\*\\/\\s*)*(class viaWorm\\s*\\{.+?fwrite\\(.+?file_get_contents\\(.+?unlink\\(.+?base64_encode\\(.+?)?file_put_contents\\([^,]+,\\s*base64_decode\\((\\@?file_get_contents\\(.+|[^\\)]+[\\);\\s]+echo[\\s\\(]+file_get_contents\\([^\\)]+[\\);\\s]+($|\\?>))/is"}, {"id": "522", "name": "assert Hex 64encodedText Hex", "regx": "/(assert_options\\(.+?\\);\\s*|function [a-z_0-9]+\\([^\\)]*[\\)\\s\\{]+.+?return[^;]*[;\\}\\s]+|\\$(color|auth|pass|default|__)[_\\-\\>\\.a-z0-9]*\\s*=.+?;\\s*)*(((\\$[a-z_0-9]+)\\s*=.*?\\$\\2|(\\$[a-z_0-9]+)\\s*=.*?\\5).*\\s*)*assert\\(\\s*(\"(\\\\x[0-9A-F][0-9A-F])+'[\\/a-z\\_0-9\\=]+'(\\\\x[0-9A-F][0-9A-F])+\"|\\6)[\\)\\s]+;/i"}, {"id": "523", "name": "post strtoupper if isset eval", "regx": "/((\\$[a-z_0-9]+)\\s*=[^;]+;\\s*)+(\\$[a-z_0-9]+\\s*=\\s*(\\$[a-z_0-9]+|strto...er)\\s*\\(([\\s\\.]*\\$[a-z_0-9]+\\s*\\[\\d+\\])+[\\);\\s]+)+if[\\(\\s]+isset\\s*\\([^\\)]+[\\)\\s]+\\{\\s*eval\\s*\\([^\\)]+[\\);\\s]+\\}/i"}, {"id": "524", "name": "function gzinflate base64_decode for chr ord eval", "regx": "/(function\\s+([a-z_0-9]+)\\s*\\(\\s*(\\$[a-z_0-9]+)[\\)\\s\\{]+\\3[=\\s]+gzinflate\\(base64_decode\\(.+[\\);\\s]+)?for\\s*\\(([^\\{]+|.+?)\\{\\s*(\\$[a-z_0-9]+)(\\s*\\[[^\\]]+\\]+)*[\\.\\s]*=[\\@\\s]*chr\\([^;]+[;\\s\\}]+(return[^;]*[;\\}\\s]+)?eval\\((\\2\\(|\\3|\\5)[^\\)]*\\)[\\);\\s]+/i"}, {"id": "525", "name": "ini_set ob_start register_shutdown_function if HTTP_USER_AGENT file_get_contents return", "regx": "/<\\?[ph\\s]+(\\@?(ignore_user_abort|ini_set|set_time_limit|error_reporting)\\(.*?\\);\\s*)++(?=.*?(register_shutdown_function\\(['\"\\s]++([a-z_0-9]++)['\"\\s]++[^;]++;))(?=.+(ob_start\\([\"'\\s]*+(.*?)['\"\\s]*+\\);)).*?function\\s+(\\6\\s*\\(.+?return[^;]*;[\\s\\}]+|\\4\\s*\\((\\$[a-z_0-9]++).*?unlink[\\s\\(]+\\8[^;]+;[\\s\\}]*)(if\\s*\\([^\\)]++[\\)\\s\\{]+|\\3[\\}\\s]*|\\5[\\}\\s]*|exit\\([^;]+;\\s*class\\s+([a-z_0-9]++)[\\s\\{]+.*?function\\s+([a-z_0-9]++)[\\s\\(]+.+\\10[\\:\\->]+\\11\\s*\\([^;]++[;\\s\\}]+){3}($|\\?>)/i"}, {"id": "526", "name": "if isset REQUEST eval REQUEST", "regx": "/if[\\s\\(]+(isset|empty)[\\s\\(]+\\$_(REQUES|GE|POS)T[^\\{]+[\\{\\s]+.*eval[\\s\\(]+[\\$a-z_0-9]+[^\\)]*\\)+(.+(die|exit|echo|print|return)[^;]*;)*[\\s\\};]*($|\\?>)/i"}, {"id": "527", "name": "if COOKIE gzinflate base64_decode eval", "regx": "/(?:\\$([a-z_0-9]++)\\s*+=\\s*+getallheaders[\\s\\(]++[^;]++[;\\s]++)?if\\s*+\\(.+?\\(\\$(?:_COOKIE|\\1).+?[\\{\\s]+(\\$[a-z_0-9]+)\\s*+=\\s*+(?:.*?base64_decode\\(.+?eval\\(\\2\\).+|(['\"]).+?\\3[;\\s]++(\\$[a-z_0-9]+)\\s*=[^;]++[;\\s]++(\\@?file_put_contents\\(\\4.+?\\2[^;]++[;\\s]+|(\\@|echo[\\(\\s]+['\"]\\\\x.+?['\"][\\);\\s]+)*(include|unlink)[\\(\\s]+\\4[\\);\\s]+){3,}.+|\\$\\1(?:\\s*\\[[^\\]]*+\\])*\\s*\\([^;]++[;\\s]+\\2\\s*\\([^;]++[;\\s]+)[\\s\\}]*/i"}, {"id": "528", "name": "auth_pass copy FILES exec passthru system shell_exec", "regx": "/<\\?(?=.*\\$(md5|auth)_pass\\s*=)(?=.*copy\\(\\$_(POST|FILES)\\[)(?=.*exec\\()(?=.*passthru)(?=.*system)(?=.*shell_exec\\().+/is"}, {"id": "529", "name": "ini_set error_reporting if function_exists function echo return file_get_contents file_put_contents setcookie add_action add_filter", "regx": "/(?:(?<C>(?:\\#|\\/\\/)[^\\r\\n]*+\\s++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+)++|\\$\\w++(?:\\s*+\\[[^\\]]*+\\]++)*+[\\s\\.]*+=[^;]++;\\s*+|(?:ini_set|error_reporting|set_time_limit|session_start)\\s*+\\([^\\)]*+\\);\\s*+)++(?:(?:if[\\s\\(\\!]++function_exists[\\(\\s]++([\"'])(.+?)\\2[\\)\\s]++\\{\\s*+)?function\\s++(\\w++)\\s*+\\([^\\)]*+\\)[\\)\\s\\{]++.+?(?:(?:echo|return)[^;]*+;[\\}\\s]*+)++)++(?:(\\$\\w++)\\s*+=[^;]*?(?:\\3|\\4)[^;]++;\\s*+|.+?(?:file_..t_contents|etcookie)\\(.+?file_..t_contents\\().*?(?:(?:Include|eval)[\\s\\('\"\\.]++(?:\\3|\\4|\\5)[^;]++;[\\}\\s]*+|add_(?:action|filter)\\([^,]++,\\s*+['\"](?:\\3|\\4)['\"][\\s\\)]++;[\\}\\s]*+){2,}/is"}, {"id": "530", "name": "if isset base64_decode REQUEST eval", "regx": "/(?:<(?:(?:\\!DOCTYPE\\s++)?(html)|(head)|(title)|(body)|(form)|(label)|input|(?:\\/(?:\\1|\\2|\\3|\\4|\\5|\\6)))[^>]*+>[^<]*+)*+<\\?(?:php)?+\\s*+(?:(?:\\@?(?:ini_set|ignore_user_abort|set_time_limit)\\(|\\$\\w++\\s*+=)[^;]++[;\\s]++)*+if[\\(\\s\\!]++(?:PHP_SAPI[^&]*+[\\&\\(\\s\\!]++)?(?:(?:isset|empty|strpos)[\\(\\s]++)?(?<G>\\@?\\$_(?:SERVER|REQUEST|GET|POST)\\[)[^\\{]++[\\s\\{]++(?:(?:(\\$\\w++)\\s*+=\\s*+(?:(?&G)(?:base64_decode|\\$\\w++)\\s*+\\([^;]++|(?:base64_decode|\\$\\w++)\\s*+\\(++\\s*+(?&G)[^\\]]++\\]++)[\\);\\s]++)++.+?eval\\(\\8|(print|echo)[\\s\\(]++base64_decode\\s*+\\([^\\)]++)[\\);\\s\\}]++(?:\\s*else[\\s\\{]+)?(?:echo (?:\"[^\"]*+\"|\\$\\w++);[\\s\\}]++)?(?:$|\\?>\\s*+(?:[^<]*+<\\/(?:\\1|\\4|\\5)\\>)*+)/i"}, {"id": "531", "name": "base64_decode function curl return exit", "regx": "/<\\?[ph\\s]*(\\/\\*.+?\\*\\/\\s*)*((\\$[a-z_0-9]+\\s*=\\s*base64_decode|error_reporting|if[\\s\\(]+file_exists)\\([^;]+[\\);\\s\\}]+|echo[^;]+;[\\s\\}]*|(\\$[a-z_0-9]+)\\s*=\\s*\\$_(REQUES|GE|POS)T.+(\\$[a-z_0-9]+)\\s*=\\s*file_get_contents[\\s\\(]+\\4.+eval[\\s\\(]+\\6[^;]+;[\\s\\}]*)+function\\s+([a-z_0-9]+)\\s*\\([^\\{]+[\\{\\s]+((\\$[a-z_0-9]+[\\s*=]+)?curl_[^;]+;\\s*)+.*?return.+\\7(.+exit)?[^;]*;[\\}\\s]+($|\\?>)/is"}, {"id": "532", "name": "php base64_decode file_put_contents unlink function_exists Hex call_user_func Hex", "regx": "/<\\?[ph\\s]+(\\$\\{[^\\}]+[\\}\\s]+(\\[[^\\]]+[\\]\\s\\}]+)*=[^;]+[;\\s]+(\\@?(\\$[a-z_0-9]+\\s*=|define\\(|session_start\\(|error_reporting\\(|ini_set\\(|set_time_limit\\(|set_magic_quotes_runtime\\()[^;]+[;\\s]+|if\\([^\\{]+[\\{\\s]+[^\\}]+[\\}\\s]+)*)+([^;]+[;\\s]+)?.+file_put_contents\\(.+?base64_decode\\(.+?unlink\\(.+?function_exists\\(['\"]\\\\x.+?call_user_func\\(['\"]\\\\x.+?($|\\?>)/is"}, {"id": "533", "name": "curl_init header Location return preg_replace_callback create_function return REMOTE_ADDR", "regx": "/<\\?.+?curl_init\\(.+?header[\\(\"'\\s]+Location:.+?return preg_replace_callback\\(.+?create_function\\(.+?return \\$_SERVER\\[[\"']REMOTE_ADDR.+(\\?>|$)/is"}, {"id": "534", "name": "php set_time_limit ini_set error_reporting if array_key_exists HTTP_USER_AGENT search-tracker.com preg_match google bing ob_start", "regx": "/<\\?[ph\\s]+.*?set_time_limit\\(.+?ini_set\\(.+?error_reporting\\(.+?if[\\(\\!\\s]+array_key_exists\\([\"']HTTP_USER_AGENT.+?http:\\/\\/search-tracker\\.com\\/in\\.cgi\\?.+?(preg_match\\([\"']\\/([^\\/]*google|[^\\/]*bing){2}.+?){2}.+?ob_start\\(.+/is"}, {"id": "535", "name": "if isset FILES|REQUEST move_uploaded_file else echo", "regx": "/<\\?[ph\\s]+((\\$[a-z_0-9]+\\s*=|error_reporting\\(|set_time_limit\\(|ini_set\\(|echo[\\s\\('\"]+)[^;]+[;\\s]+)*(([a-z_0-9]+)\\([^;]+[;\\s\\}]+)*(if[\\(\\s]+substr[\\(\\s]+[^\\{]+[\\s\\{]+(\\$[a-z_0-9]+\\s*=[^;]+[;\\s\\}]+)+)?((if[\\(\\s]+(isset[\\(\\s]+)?\\$_(FILES|REQUEST|POST|GET)[^\\)]*[\\)\\s\\{]+((\\$[a-z_0-9]+\\s*=|echo[\\s\\('\"]+)[^;]+;\\s*)*)+(\\@|if[\\(\\s]+)*(move_uploaded_file\\([^;]+|(fwrite\\([^;]+;\\s*)+fclose\\([^;]+)([;\\s\\}\\@]+(chmod|echo)[\\s\\('\"]+[^;]+)*[;\\s\\}]+(else[\\{\\s]+|(echo|exit|header)[^;]*[;\\s\\}]+)*)+(function\\s+\\4\\([^;]+[;\\s\\}]+(\\$[a-z_0-9]+\\s*=[^;]+[;\\s\\}]+)*if[\\(\\s]+[^\\{]+[\\s\\{]+(\\@|if[\\(\\s]+)*mkdir\\([^;]+[;\\s\\}]+return[^;]+[;\\s\\}]+)?($|\\?>)/i"}, {"id": "536", "name": "html body php opendir readdir !file_exists fopen fwrite", "regx": "/(<(\\!|\\/)?(doctype|html|head|meta|title|body)[^>]*>[a-z_0-9\\s]*){2,}<\\?[ph\\s]+\\$[a-z_0-9]+\\s*=\\s*'<\\?[ph\\s]+.+?opendir\\(.+?readdir\\(.+?(\\!file_exists\\(.+?fopen\\(.+?fwrite\\(.+?){3,}\\?>\\s*(<\\/(html|body)[^>]*>\\s*){2,}$/is"}, {"id": "537", "name": "function X_ip X_macros error_404 http_request fwrite FUNCTION", "regx": "/\\<\\?.+?_ip\\((.+?function [a-z0-9]+_macros\\(){4}.+?function error_404\\(.+?http_request.+?fwrite\\(.+?__FUNCTION__.+/s"}, {"id": "538", "name": "perl use IO::Socket scan_dir uname system exec", "regx": "/\\#\\!(\\/usr)?\\/bin\\/(perl.+?scan_dir\\(.+?uname\\(.+?system\\(.+?filemanager.+|[ba]*sh\\s+wget\\s+\\-o\\s+(\\S+)\\s+(\\S+)[^;]*;\\s*((chmod|chown|curl)\\s+(\\S+\\s+\\3(\\s+\\4)?[;\\s]*)+)+rm\\s+\\-[rf]+\\s+\\3[;\\s]*$)/is"}, {"id": "539", "name": "path if file_exists is_writable if function_exists WriteData", "regx": "/\\$path[=\\s]+.+[\\s]*if[\\s]*\\(\\!file_exists\\(.+?is_writable\\([^\\)]*[\\)\\s\\{]+if[\\s]*\\(function_exists\\(.+?WriteData.+?WriteData\\(\\);[\\s\\}]+/i"}, {"id": "540", "name": "display_errors create_wp_user REQUEST fwrite unlink", "regx": "/^.+?display_errors.+?create(_|\\s+TABLE\\s+\\`)wp_user(\\`.+?(\\$[a-z_0-9]+\\s*=(\\s*\\$[a-z_0-9]+\\s*\\(){2}[^;]+;\\s*){2}|\\(\\$_REQUEST\\[.+?fwrite\\(.+?unlink\\().+$/is"}, {"id": "541", "name": "php array foreach array eval Array Function POST", "regx": "/<\\?[ph]*\\s+(\\$[a-z_0-9\\-]+)[=\\s]+array\\([^;]+;\\s*foreach[\\s\\(]+\\1.+?eval[\\s\\(]+\\1\\[[^\\]]+\\]+[\\(\\s]+\\$_POST\\[\\1.+?($|\\?>)/is"}, {"id": "542", "name": "web shell fopen passthru", "regx": "/^.+?error_reporting\\(.+?web[ \\t]*shell.+?fopen\\(.+?passthru\\(.+?$/is"}, {"id": "543", "name": "eval array_pop REQUEST", "regx": "/eval\\([\\sa-z_0-9\\.\\(]*(?:fromCharCode\\([0-9,\\s]++|array_pop\\(\\$_(?:GE|POS|REQUES)T)(?:\\s*+\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/)*+[\\)\\s]++;\\s*/i"}, {"id": "544", "name": "/auth_pass love set_error_handler", "regx": "/<\\?(?=.*?unlink\\s*+\\((?>\\$|[A-Z]*+_++)FILE)(?=.*?\\$(?>_(?>POST|GET|REQUEST|COOKIE)\\[['\"])?(?>password(?>hash)?|auth_pass)['\"\\]\\)\\s\\!]*+=\\s*+['=\"])(?=.*?loveLogin|.*?move_uploaded_file\\s*+\\()(?=.*?(?>set_error_handler|mkdir)\\s*+\\(|.*?function\\s++(?>array_to_json|dirtoarray)\\s*+\\().++/is"}, {"id": "545", "name": "GETdo_remove safe_mode end", "regx": "/if\\(\\$_GET\\['do'\\]==\"remove\"\\)\\{\\nunlink\\(getcwd\\(\\)\\.\\$_SERVER\\[\"SCRIPT_NAME\"\\]\\);.+safe_mode.+else.+'\\.\\$end;/s"}, {"id": "546", "name": "set_time_limit unlink base64_decode fwrite exec passthru", "regx": "/<\\?[ph\\s]+(?=(?:(?:(?:sleep|set_time_limit|error_reporting|ignore_user_abort)\\s*\\([^;]++[;\\s]++){2}.*?(?:(\\$[a-z_0-9]++)\\s*=\\s*)?[a-z_0-9]++\\(__FILE__.+?)?unlink\\([\\s\"'\\.]*+(\\1|__FILE__)).+?;\\s*\\@?f(ile_put_contents|write)\\s*\\(.+/is"}, {"id": "547", "name": "function class variable function", "regx": "/<\\?[ph\\s]*function\\s+([a-z_0-9]+)\\(.+?return[^;]*;\\s*\\}\\s*class[^\\{]+\\{.*?function[^\\{]+\\{\\s*(\\$[\\$\\{]*[a-z_0-9]+\\}*(\\s*\\[[^\\]]+\\])*\\s*=\\s*)*\\1\\((.*?\\$[\\$\\{]*[a-z_0-9]+[\\}\\s]*(\\[[^\\]]+\\]\\s*)*\\([^;]+;\\s*){50,}[\\}\\s]*($|\\?>)*/is"}, {"id": "548", "name": "php Starting calls c99shexit", "regx": "/<\\?[ph\\s]+\\/\\/Starting calls.+chdir\\(\\$lastdir\\)[;\\s]+[a-z0-9]+exit\\(\\)[;\\s]*($|\\?>)/is"}, {"id": "549", "name": "php password hg_exit", "regx": "/<\\?[ph\\s]*(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*\\$password.+(leafMailCheck\\s*\\(.+print[\\s\\(]*(['\"]).+?\\4;|ob_start[\\(\\s]+array[\\(\\s]+\\$[^;]+;[\\s\\}]*mt_srand\\(\\);|eval\\([^\\)]++[^;]++[;\\)\\']+|hg_exit\\(\\);.+)[\\s\\}]*($|\\?>)/is"}, {"id": "550", "name": "shell system passthru", "regx": "/\\<\\?.+?(shell|authp).+?error_reporting\\(0\\).+?set_time_limit\\(0\\).+?ini_set\\(.+fopen\\(.+/is"}, {"id": "551", "name": "function BSSV eval", "regx": "/function BSSV\\(.+eval\\(BSSV\\(.+?\\)+[;\\s]*/is"}, {"id": "552", "name": "error_reporting password exit me", "regx": "/<\\?[ph\\s]*error_reporting\\(0\\);\\s*\\/\\/If there is an error, we'll show it, k\\?\\s*\\$password\\s*=.+ :-\\)\\s*exit\\(\\);\\s*\\?>\\.\\$me\\./is"}, {"id": "553", "name": "var functions return new RecursiveArrayIterator array", "regx": "/(\\$[a-z_0-9]+[\\s=]+\"[a-z_\\-\\0-9]*\";\\s*)+((\\$[a-z_0-9]+[\\s=]+)?\\$[a-z_0-9]+\\(+.+?[\\)]+;\\s*)+.+return\\s+new\\s+RecursiveArrayIterator[\\(\\s]+array.+?[\\)\\s]+;\\}+/is"}, {"id": "554", "name": "PHP DEFINE file_get_contents base64_decode json_encode", "regx": "/<\\?(?:php)?\\s*+(?:\\@*+(?:(\\$\\w++)\\s*+(?:\\[[^\\]]*+\\]++\\s*+)*+=\\s*+(?<S>\"(?:[^\"]*+(?<=\\\\)\")*+[^\"]*+(?<!\\\\)\"|'(?:[^']*+(?<=\\\\)')*+[^']*+(?<!\\\\)'|\\d++)|(\\$...)\\w++\\s*+(?:\\[[^\\]]*+\\]++\\s*+)*+=\\s*+(?:Array|explode)\\s*+\\(|[if\\s\\(\\!]*+DEFINE[d]?+\\((['\"])(.*?)\\4|error_reporting\\()[^;]*+;[\\s\\}]*+)++.+file_get_contents\\([^;]*(?:\\5|;\\s*+\\$[stored]*+crc).+?base64_decode[\\(\\s]++(?:\\1|\\3).+(?:(\\$\\w++)\\->run\\(\\);\\s*+unset\\(\\6\\);\\s*+$|json_encode\\s*+\\([^;]++;\\s*+\\?>)/is"}, {"id": "555", "name": "if array_keys GET eval base64_decode exit", "regx": "/<\\?[ph\\s]*(\\@?error_reporting\\(0\\);\\s*)?if[\\(\\s]+array_keys\\(\\$_GET.+\\s+((<\\?[ph\\s]*)?\\$[a-z_0-9]+[\\s\\{\\$]+\\@*eval\\(base64_decode\\(['\"][a-z_\\/\\+\\=0-9]+['\"][\\)\\}\\{\\$\\s]+exit\\([\\)\\}\\s]+\\&\\s*\\$[a-z_0-9]+[;\\s\\}]+\\?>)+/i"}, {"id": "556", "name": "php error_reporting set_time_limit ini_set _xcookie base64_decode str_rot13 gzinflate", "regx": "/<\\?[ph\\s]+(\\@?(error_reporting|set_time_limit|ini_set)\\(.*?0\\);\\s*){3,}(.+?_xcookie){9}(.+?(\\$[a-z_0-9]+\\s*=|return)([\\s\\@]*(base64_[den]+code|str_rot13|gz[dein]+flate|strrev)\\(){3,}[^;]+;){2}.+return false[;\\s\\}]+($|\\?>)/is"}, {"id": "557", "name": "excessive spaces script eval function packed", "regx": "/<script[^>]*>[^<]*eval\\(function\\(p,a,c,k,e,d\\)\\{.+?\\s*<\\/script>\\s*<script[^>]*>[^<]*\\[['\"]Histats\\.start['\"\\s,]+1[\\s,]+4214393[\\s,]+[^<]*<\\/script>\\s*(<noscript>\\s*(<a[^>]*>\\s*)*<img[^>]*[\\?|\\&]4214393[^>]*>\\s*(<\\/a>\\s*)*<\\/noscript>)*/i"}, {"id": "558", "name": "php error_reporting unlink __FILE__ call_user_func", "regx": "/<\\?[ph\\s]*(\\$[a-z_0-9]+\\s*=[^;]+;\\s*|\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/[^\\n]*\\s*)*(\\@?(error_reporting|ini_set|set_time_limit)\\s*\\([^;]+;\\s*){3}.+\\@?unlink\\(__FILE__[^;]+;\\s*.+(mysqli_close|call_user_func)[\\s\\(]+\\$[^;]+;\\s*((return|echo)[^;]+;[\\s\\}]*)*($|\\?>)/is"}, {"id": "559", "name": "php error_reporting system readdir file_put_contents move_uploaded_file unlink form html", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/[^\\n]*\\s*|[\\@if\\s\\(]*\\$[a-z_0-9]+\\s*=[^;]+;\\s*|\\@*(define|session_start|error_reporting|ini_set|set_time_limit|[sg]et_magic_quotes_[a-u]+)\\([^;]+[;\\}\\s]+)+((function\\s+([a-z_0-9]+)\\s*\\(|.+?system\\s*\\(.+?setcookie\\s*\\(.+?readdir\\s*\\()(?=.+?file_put_contents\\(\\s*([^,]++),).+?unlink\\s*+\\(\\s*+(\\7|\\$_(REQUES|POS|GE)T\\[)).+?<\\/body>\\s*<\\/html>\\s*([\"'];\\s*exit[;\\}\\s]+\\6[\\(\\);\\s]*\\/\\/[^\\n]*+\\s*)?$/is"}, {"id": "560", "name": "php dir recursive function  loop", "regx": "/<\\?[ph\\s]+(?:.*(?:dir|include|require|_once|getcwd)\\s*\\([^;]+;\\s*|\\/\\/.*\\s+)*(?:(\\$[a-z_0-9]+)\\s*=.*?(?:md5|rand)\\s*\\([^;]+;\\s*)+(?:\\/\\/.*\\s+)*function ([a-z_0-9]+)\\([^\\{]+\\{\\s*(?:.+dir\\s*\\([^;]+;\\s*|[\\}\\s]*else[\\{\\s]*|(?:while|if|for(?:each)?)\\s*\\(.+[\\{\\s]*)+.*\\2\\s*\\([^;]+;\\s*(?:(?:\\}[\\}\\s]*|else[\\{\\s]*|(\\$[a-z_0-9]+)\\s*=\\s*+(?!\\$)(?!\"))*+.*(?:copy|dir|unlink|echo|header|print|getenv|file|fopen|fwrite|date[^\\(]*|(\\$[a-z_0-9]+)\\s*=|(\\$[a-z_0-9]+)(\\s*\\[[^\\]]+\\])*\\s*=)\\s*([\\('\"]+|\\3|\\4|\\5)[^;]*;\\s*|\\/\\/.*+\\s+)+($|\\?>)/i"}, {"id": "561", "name": "php error_reporting if isset REQUEST mail", "regx": "/<\\?[ph\\s]*(if[\\s\\(\\!]+(empty|strlen|isset)[\\s\\(]+\\$_(REQUES|GE|POS)T[^\\)]+[\\)\\s\\{]+[^;]+;[\\s\\}]*|error_reporting\\([^;]+;\\s*)+.*?(if[\\s\\(\\!]+(empty|strlen|isset)[\\s\\(]+\\$_(REQUES|GE|POS)T[^\\)]+[\\)\\s\\{]+(\\$[0-9_a-z]+)\\s*=\\s*(rand\\(|\\$_(REQUES|GE|POS)T)).+((\\$[a-z_0-9]+\\s*=\\s*|if[\\s\\(]+|else[^\\{]*[\\{\\s]+)*mail[\\s\\(]+[^;]*\\7[^;]+[\\);\\s\\}]*)+(((if[\\(\\s]+|else)[^\\{]*[\\{\\s]+)?((header|echo|print|exit)[^;]*[;\\s\\}]+)+)*($|\\?>)/is"}, {"id": "562", "name": "php o_0 variable functions end", "regx": "/<\\?[ph\\s]*((\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/[^\\n]*\\s*|error_reporting\\([^;]+;\\s*)*((([^;]*\\$[o_0]+|.*function\\s+([o_0]+))|(exit|break|unset|header|return)[^\\)]*)[^;]+['\"\\);\\}\\s]+)+?(([\\$\\{\"']+(\\\\x[a-f0-9]{2}|[li1_]+[\\s=]+\\7)+[\\}'\"\\s]*)(\\[[^\\]]+\\]\\s*)*\\([^;]+[;\\}\\s]+)+)+?.*([\\$\\{\"']+(\\\\x[a-f0-9]{2}|[li1_])+[\\}'\"\\s]*(\\[[^\\]]+\\]\\s*)*\\([^;]+;\\s*)+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/[^\\n]*\\s*)*($|\\?>)/i"}, {"id": "563", "name": "php function curl file_put_contents call", "regx": "/<\\?[ph\\s]+((ini_set|echo|\\$[a-z_0-9]+\\s*=)[^;]+;\\s*)*function\\s+([a-z_0-9]+)[^\\{]+[\\s\\{]+(\\$[a-z_0-9]+\\s*=[^;]+;[\\}\\s]*)*((\\$[a-z_0-9]+\\s*(\\[[^\\]]+\\]+\\s*)*=\\s*)?curl_.+;[\\}\\s]*)+(((if[\\s\\(]+[^;]+|else[\\s\\{]+[^;]+|\\$[a-z_0-9]+\\s*=\\s*)?(return|file_put_contents\\(|\\$_(SERVER|GET|POST|REQUEST)|curl_|exec\\(|echo|print)[^;]+;[\\}\\s]*)+(\\$[a-z_0-9]+\\s*(\\[[^\\]]+\\]+\\s*)*=\\s*)?\\3\\([^;]+;[\\}\\s]*((.*fopen|fwrite|fclose|eval)\\(.+\\s*)*)+($|\\?>)/i"}, {"id": "564", "name": "php _COOKIE file_get_contents_X function curl return FORM", "regx": "/<\\?(?:php)?(?:(?<C>[\\{\\s]++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++)*+(?:(?:isset|md5|if|(\\$\\w++)|[\\}\\s]*+else\\s*+\\{\\s*+(\\2)\\s*+=\\s*+['\"]https?:\\/\\/[^\\}]++\\}++|file_exists[\\s\\('\"]++\\.htaccess['\"][^\\}]++\\}++)[\\s\\(=\\!]++)++\\$(?:_REQUEST|_POST|_GET|_COOKIE|\\{[^\\}]++\\}++||\\[[^\\]]++\\]++)++.*(?:;|\\s*+\\{)(?:\\s*+(?:echo|exit)[^;]*+;)*+[\\}\\s]*+)++(?:if[\\s\\(]++)?(\\$\\w++)[=\\s]++(?:(file_get_contents_\\w++)[^\\}]++\\}\\s*+function\\s++\\5[^\\{]++|curl_init[\\(\\)\\s]++)\\{\\s*+(?:(?:(\\$\\w++)[\\s=]++curl_init|curl_setopt[\\s\\(]++(?:[^,]++[\\s,]++CURLOPT_(?!URL)|(\\4|\\6)[\\s,]++CURLOPT_URL[\\s,]++(?:\\2|\\3)))[^;]*+;\\s*+)++(?:(?:(\\$\\w++)[\\s=]++)?(?:(?:curl_exec|curl_close|eval)[\\s\\(]++)++(?:\\7|\\8)[^;]*+;\\s*+)++(?:(?:exit|return)[^;]*+;|[\\}\\s]++|\\?>|<form[^>]*+(?:>\\s*+<input[^>]*+)+>\\s*+<\\/form>|$)++/i"}, {"id": "565", "name": "php error_reporting define function get_real_ip return ip", "regx": "/<\\?[ph\\s]+(\\/\\/[^\\n]*\\n\\s*|\\/\\*[^\\*]*(\\*[^\\*\\/]*)+\\/\\s*|\\@?(ignore_user_abort|session_start|ini_set|set_time_limit|error_reporting)\\(.*?\\);\\s*|define\\([^\\)]+[\\);\\s]+|\\$+[a-z_0-9]+(\\s*\\[[^\\]]*\\])*\\s*[+\\.]*=[^;]+;\\s*)+.+function\\s+(createIndexLM|get_real_ip|ip_visitor_city)\\(.+(return \\$ip|return \\$city|mysqli_close[^;]+;\\s*return[^;]*)[;\\s\\}]+(\\/\\/[^\\n]*\\s*|\\/\\*[^\\*]*(\\*[^\\*\\/]*)+\\/\\s*)*($|\\?>)/is"}, {"id": "566", "name": "php error_reporting add_filter function script zoneid file_get_contents_curl function hide add_action", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)+((\\$[a-z_0-9]+)\\s*=[^;]+;\\s*|(|ini_set|error_reporting|set_time_limit)\\([^\\)]*\\);\\s*)+(add_(filter|action)[\\s\\(]+['\"][^,]+[,\\s]+((['\"])(.+?)\\9[\\);\\s]+function\\s+\\10\\s*\\([^\\{]+\\{|function\\s*\\([^\\{]+\\{)[^\\}]+[\\}\\);\\s]+)+(\\/*\\$[a-z_0-9]+\\s*=[\\s'\";]+)+<script.+function\\s+file_get_contents_[a-z_0-9]+[^\\{]+\\{\\s*([^;]*curl_[^;]*;\\s*|return[^;]*;\\s*|(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*)*|\\}\\s*)+(function\\s+(hide[a-z_0-9]+)\\([^\\{]+\\{.+add_action[\\s\\(]+['\"][^,]+[,\\s]+(['\"])\\16\\17[\\);\\s]+)?(function[^\\{]+\\{.+return\\s+\\$_SERVER\\[[^;]+[\\};\\s]+)*($|\\?>)/is"}, {"id": "567", "name": "php if class_exists class new calss else catch", "regx": "/<\\?(?:php)?\\s*+(?:(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/\\s*+|(?:\\#|\\/\\/)[^\\n]*+\\s++)++|if[\\s\\(\\!]++class_exists[^\\{]++\\{\\s*+)*+class\\s++(\\w++)\\s*+\\{.+(?:(\\$\\w++)[\\=\\s]++new\\s++\\2[^;]*+;\\s*+(\\$\\w++)[\\=\\s]++\\3[^;]*+;\\s*+(?:[^\\n]*(?:\\2|\\3|\\4|else|catch)[^\\n]*+[\\s\\}]++)++|class\\s++([a-f\\d]{32,33})\\s++extends\\s++\\2.+new\\s++\\5[^;]*+[;\\s\\}]++\\/\\/[a-f\\d]{32,33}\\s*+)(?:$|\\?>)/is"}, {"id": "568", "name": "meta propeller script function document.createElement script", "regx": "/(<meta(\\s+(name|content)=['\"](propeller|[0-9a-f]{32})['\"]){2}[^>]*>\\s*)?<(script)[^>]*>[\\s\\(]*function\\s*\\([^\\}]+\\}[\\);\\s]*\\(document\\.createElement[\\(\\s]+(String\\.fromCharCode\\(|['\"]script['\"]).+\\)[;\\s]*(\\/\\*\\s*[0-9a-z]+\\s*\\*\\/\\s*|$|<\\/\\5>)+/i"}, {"id": "569", "name": "var document createElement script String.fromCharCode getElementsByTagName appendChild", "regx": "/(var\\s+([a-z_0-9]+)\\s*=\\s*(document|\\d+)[;\\s]+)+(var\\s+([a-z_0-9]+)\\s*=\\s*\\2\\.createElement[\\(\\s'\"]+|script[^;]+;\\s*|(var\\s+[a-z_0-9]+\\s*=\\s*)?String\\.fromCharCode[^;]+;\\s*|\\5\\.[a-z_0-9]+\\s*=[^;]+;\\s*)+if[\\(\\s]+document\\.[^\\}]+[\\}\\s]+else\\s*\\{\\s*\\2\\.getElementsByTagName[^\\.]+\\.appendChild[\\s\\(]+\\5[^\\}]+\\}[\\}\\s]*/i"}, {"id": "570", "name": "php if function_exists define EOF file_put_contents", "regx": "/<\\?[ph\\s]+\\$[a-z_0-9]+\\s*=\\s*__FILE__;\\s*((\\$[a-z_0-9]+)\\s*=\\s*function_exists\\([^;]+;\\s*)+if[\\s\\(]+[^\\{]+\\2[^\\{]+\\{\\s*(define[\\('\"\\s]+([a-z_0-9]+)[\"'][^;]+;\\s*|\\/\\/[^\\n]*\\n\\s*)+\\$[a-z_0-9]+\\s*=\\s*<<<['\"]*([a-z_0-9]+)['\"\\s]+.+\\5.+file_put_contents\\([^;]+;[\\s\\}]+($|\\?>)/is"}, {"id": "571", "name": "php curl_init _REQUEST curl_exec eval", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/|\\/\\/[^\\n]*\\n\\s*)*(\\$[a-z_0-9]+)?[\\s=]*curl_init\\(\\$_(REQUES|POS|GE)T[^;]+;\\s*((\\$[a-z_0-9]+)?[\\s=]*curl_[^;]+;\\s*)+(print|die|echo|eval)['\"\\s\\(\\?\\>\\.]+\\6['\"\\s\\);\\}]+\\s*($|\\?>)/i"}, {"id": "572", "name": "php define create_function echo form", "regx": "/<\\?[ph\\s]+((\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/[^\\n]*\\s*|if\\s*\\(\\$[a-z_0-9]+[^\\}]+\\}\\s*)*(define[d\\('\"\\s]+([a-z_0-9]+)[\"'][^;]+[;\\}\\s]+|function\\s+([a-z_0-9]+)\\(.+?return[^;]+;[\\}\\s]+)+)+(\\$[a-z_0-9]+)\\s*=\\s*[^;]*\\$(_REQUEST|_POST|_GET|_COOKIE)[^;]*;\\s*(\\$[a-z_0-9]+)\\s*=\\s*\\6\\([^;]*\\5[^,]+,\\s*\\7*.+?(\\$[a-z_0-9]+)\\s*=\\s*create_function\\([^,]+,\\s*\\9.+?\\10\\([^;]*;[\\}\\s]*(echo|exit)[\\s\\('\"]+<form[^>]*(>\\s*<input[^>]*)+>\\s*<\\/form>['\";\\}\\s]+($|\\?>)/is"}, {"id": "573", "name": "php new wp_http_curl function rms add_action admin_footer function script", "regx": "/<\\?.+?new Wp_Http_Curl\\(\\);(.+?\\/\\*.*?\\*\\/\\s*function\\s+[a-z_0-9]*rms_[a-z_0-9]+){5,}.+?\\/\\*.*?\\*\\/\\s*add_action\\([\"']admin_footer['\"],\\s*function\\(\\)\\s*\\{\\s*\\?>\\s*<script.+?<\\/script>\\s*<\\?[ph\\s\\}\\);]*($|\\?>)/is"}, {"id": "574", "name": "try password file_get_contents catch Exception", "regx": "/(try\\s*\\{\\s*(.+password.+[\\r\\n]+\\s*)*(file_get_contents\\s*\\([^;]*;[\\s\\}]*)+catch[\\(\\s]*Exception\\s*\\$[a-z_0-9]+[\\)\\s]*\\{\\s*(if[\\s\\(]+[^\\{]+\\{\\s*|\\}))+[\\s\\}]*/i"}, {"id": "575", "name": "error_reporting global zeeta if isset function curl_ preg_match fwrite include unlink", "regx": "/(\\@?(ignore_user_abort|set_time_limit|ini_set|error_reporting)\\([^\\)]*\\)[\\s\\)]*;\\s*|\\/\\/[^\\n]*\\n\\s*)*global (\\$[a-z_0-9]+)[^;]*;\\s*if[\\s\\(\\!]+((empty\\(|strlen\\(|isset\\()*(\\$[a-z_0-9]+)[\\)\\s\\&\\=\\!]*isset[\\(\\s]+\\3)[^\\{]+\\{(?=.+?function\\s+([a-z_0-9]+)\\([^\\{]+\\{\\s*[^\\n]*(curl_|file_get_contents))(?=.*?(\\$[a-z_0-9]+)\\s*=\\s*(['\"])\\7).+?(\\$[a-z_0-9]+)\\s*=\\s*\\9\\([^;]+;\\s*preg_match\\([^\\n]+\\11[,\\s]+(\\$[a-z_0-9]+).+?fwrite\\([^\\n]+\\12[^\\n]+;\\s*fclose\\([^\\n]+;\\s*include(_once)?[\\(\\s]+([^\\)]+)[\\);\\s]+((unlink[\\(\\s]+\\14|\\3\\s*=|\\6\\s*=)[^;]*;[\\}\\s]+){3,}/is"}, {"id": "576", "name": "php session_start header _REQUEST function mail curl return", "regx": "/<\\?[ph\\s]*+(?=(?:.*+\\s*+)+?function\\s++(\\w++)\\()(?:(?:(?:ini_set|session_start|header|\\1)\\([^;]++;\\s*+|(?:(?:[els\\s]*+if|isset|foreach)[\\s\\(]++)++\\$_(?:REQUEST|GET|POST|SERVER).[^\\{]++\\{\\s*+)?(?:(\\$\\w++)[\\s\\.]*+=[^;]*+[;\\s]++(?:exit;\\s*+)?|\\/\\/.++\\s++)*+[\\}\\s]*+)++(?:function\\s++\\w++\\([^\\{]++\\{\\s*+(?:(\\$\\w++)[\\s\\.]*+=[^;]++;\\s*+|(?:if[\\s\\(]++|else)++[^\\{]*+[\\{\\s]++|\\}\\s*+)*(?:(?:(\\$\\w++)\\s*+=\\s*+)*+(?:mail|curl_[a-z]++)\\s*+\\([^;]++[\\);\\s\\}]*+(?:(\\$\\w++)[\\s\\.]*+=.++\\s++|return[^;]*+;\\s*+|(?:if[\\s\\(]++|else)++[^\\{]*+[\\{\\s]++|\\}\\s*+|\\/\\/.*+\\s*+)*+)++)++($|\\?>)/i"}, {"id": "577", "name": "php goto Variable Function _REQUEST", "regx": "/<\\?(?:php)?+\\s*+(?:(?:\\#|\\/\\/)[^\\r\\n]*+\\s++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+)*+goto\\s++\\w++;\\s*+\\w++\\:.+?(?:\\$\\w++|\\beval|curl_setopt|copy)\\s*+\\(\\s*+(?:\\w++\\s*+\\(\\s*+)*+(?:(?:['\"\\s]++|\\$[^\\\\]++)\\\\(?:x[a-f\\d]{2}|\\d++)|\\)[\\.\\s]++'[\\-\\+\\w\\/=\\\\]++').+?(?:(?<=\\?>)|\\s*+(?:$|\\?>))(?:\\s*+[\\-\\+\\w\\/=\\\\]{200,}\\s*+$)?+/i"}, {"id": "578", "name": "php if function o_0 var o_0 66+", "regx": "/<\\?[ph\\s]*((\\$[a-z_\\-0-9]+)\\s*=[^;]+;[\\}\\s]*|e(xit|lse)[\\s\\{;\\}]*|if\\s*\\([^\\{]+\\{[^;]+;[\\}\\s]*|([o_0]+)\\s*\\([^;]+;[\\s\\}]*|(date_default_timezone_set|error_reporting|header)\\([^\\)]+\\)+;\\s*)*function\\s+([o_0]+)\\s*\\((.*?\\$[o_0-9\\{\\}]{3,}\\s*=){16,}[^;]+['\"\\);\\}\\s]+(((\\$[a-z_0-9]+)\\s*=\\s*)?f(open|close|write)\\s*\\([^;]+;[\\}\\s]*|return\\s*\\10;[\\s\\}]*)*($|\\?>)/i"}, {"id": "579", "name": "php function if header Location add_action", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/[^\\n]*\\s*)*(function\\s+([a-z_0-9]+)[\\s\\(]+[^\\{]+\\{+\\s*(return (in_array\\(|\\$GLOBALS)+[^;]+;[\\}\\s]+|(\\/\\/[^\\n]*\\n+\\s*|\\$[a-z_0-9]+\\s*=[^;]+;\\s*)+if[\\s\\(]+[^\\{]+\\{+\\s*header[\\s\\('\"]+Location\\:[^;]+;\\s*(e(cho|xit)\\s*\\([^;]+;\\s*|else\\s*\\{\\s*|\\}\\s*)+))+add_action\\([^,]+,[\\s'\"]+\\4[^;]+;[\\}\\s]*($|\\?>)/is"}, {"id": "580", "name": "php file_get_contents file_put_contents mkdir curl scandir chmod new pclzip unlink echo flush", "regx": "/<\\?[ph\\s]+([\\@\\/\\#\\s]*(error_reporting|ini_set|set_time_limit|header)\\s*\\([^\\)]*[\\);\\s]+)*((([;\\s]*(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|(\\$[a-z_0-9]+)\\s*=[^;]+;\\s*|if\\s*\\([^;]*|else[\\s\\{]*))*(\\@*((\\$[a-z_0-9]+)\\s*=\\s*)?(file_get_contents\\s*\\(|file_put_contents\\s*\\(([^,]*),|unlink\\s*\\(|mkdir\\s*\\(|curl_[a-z]+\\s*\\(|die\\s*\\(|(echo|print)[^;]+;\\s*(return|exit))[^\\;]+[;\\}\\s]+)+)+.*(\\$[a-z_0-9]+)\\s*=\\s*(scandir\\s*\\(\\s*\\11.+chmod\\s*\\(\\s*\\11|new pclzip\\s*\\(\\s*\\13.+unlink\\s*\\(\\s*\\13)[^;]+;\\s*)+((\\$[a-z_0-9]+)\\s*=[^;]+;[\\}\\s]*)+(echo|print)['\"\\s*\\(]+\\19[^;]*;[\\}\\s]+([ob_]*flush\\(\\);\\s*)+($|\\?>)/is"}, {"id": "581", "name": "php function dir file scandir _POST", "regx": "/<\\?[ph\\s]*(function (([a-z_0-9]+dir)|([a-z_0-9]+file))\\([^{]+\\{\\s*((echo|[\\}\\s]*else[\\s\\{]*|if\\s*\\(|(\\$[a-z_0-9]+)\\s*=\\s*scandir\\(|foreach[\\s\\(]+\\7|\\3\\(\\$_(REQUEST|GET|POST|SERVER))[^;]+;[\\s\\}]*)+)+($|\\?>)/i"}, {"id": "582", "name": "try if isset SERVER COOKIE curl variable function catch Exception", "regx": "/(?:<(\\?)(?:php)?+|try\\s*+(\\{))\\s*+(?:(?:if|for(?:each)?)\\s*+(?<B>\\((?:[^\\(\\)'\"\\/\\#]++|\\/[^\\*\\/]|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/|(?:\\#|\\/\\/)[^\\n]*+\\s++|\"(?:\\\\\"|[^\"])*+(?<!\\\\)\"|'(?:\\\\'|[^'])*+(?<!\\\\)'|(?&B))*+\\))[\\{\\s]*+|\\$\\w++[\\s\\.\\+]*+=[^;]++;\\s*+|(?:\\{|\\}|else)\\s*+)*?(?:(?:(\\$\\w++)\\s*+=\\s*+)?+\\@*+(?:mail|curl_[a-z]++)\\s*+\\([^;]++[;\\s\\}]++)++(?:(?:[^;]*?\\4[^;]*+[;\\s\\}]++)*?(?:[^;]*?(\\$\\w++)(?:[\\s=]++array\\s*+)?+\\([^;]++[;\\}\\s]++)++(?:(?:(\\$\\w++)\\s*+=\\s*+)?+[^;]*?(?:\\5|\\6)[^;]*+;[\\s\\}]*+)*+)(?:catch[\\s\\(]++Exception\\s++\\$\\w++[\\)\\s]++\\2[^\\}]*+\\}\\s*+|[^\\?]*+(?:\\?(?!>)[^\\?]*+)*+\\1>\\s*+)/i"}, {"id": "583", "name": "php function variable function include variable", "regx": "/<\\?(?:php)?+\\s*+(?:\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++)*+[\\s\\?><ph]*+(?:(\\$\\w++)\\s*+=\\s*+\\$_COOKIE|function\\s*(\\w++)).+?(\\$\\w++)(?:\\s*+\\[[^\\]]*+\\]++)*+[\\s\\.]*+=\\s*+(?:\\1.+?(\\$\\w++)[\\s\\.]*+=\\s*+\\3(?:\\s*+\\[[^\\]]*+\\]++)*+|(['\"]).+?\\5[\\.\\s]++\\2\\(\\$\\w++)\\s*\\(.+?(?:eval|include)(?:_once)?+[\\s\\(\"]++(?:\\3|\\4)[^;]*+;\\s*+(?:\\$\\w++\\s*+=[^;]++;\\s*+)*+(?:$|\\?>\\s*+)/is"}, {"id": "584", "name": "php define class Exception class function new class unset", "regx": "/<\\?[ph\\s]*([if\\(\\s\\!]*define(d\\s*\\([^\\)]+|\\s*\\([^,]+,\\s*([a-z_0-9\\(]+|['\"]))[^\\)]*[\\);\\s\\{\\}]+|\\$[\\{\\$a-z_0-9\\}\\[\\'\"\\]\\s]+=[^;]+;[\\}\\s]*)*class\\s+(_+[a-z]_+[0-9]+)\\s+extends\\s+Exception\\s*\\{(.+?\\}\\s*class\\s+(_+[a-z]_+[0-9]+)\\s+extends\\s+\\4)+.+?\\}\\s*class\\s+(_+[a-z]_+[0-9]+)\\s*\\{.+?function __construct\\s*\\(.+?\\}\\s*function\\s+([a-z][0-9]+)\\s*\\(.+(\\$[a-z_0-9]+)\\s*=\\s*new\\s+\\7[^;]+;\\s*\\9\\-\\>\\8\\([^;]+;\\s*unset\\(\\9\\);\\s*($|\\?>\\s*)/is"}, {"id": "585", "name": "php fopen contactinfo fwrite fclose echo", "regx": "/<\\?[ph\\s]+((\\$[a-z_0-9]+)\\s*=[^;]+;\\s*)+((\\$[a-z_0-9]+)\\s*=\\s*fopen\\(.+?contactinfo[^;]+;\\s*fwrite\\(\\4,\\s*\\2\\s*\\);\\s*fclose\\(\\4[^;]+;\\s*)+(echo\\s*[\"'][^;]+[;\\s]+)+($|\\?>)/i"}, {"id": "586", "name": "if class_exists WPTemplatesOptions foreach wp_get_themes if file_exists include", "regx": "/if[\\s\\(\\!]+class_exists[\\s\\('\"]+WPTemplatesOptions[^\\{]+\\{\\s*foreach[\\s\\(]+wp_get_themes[^\\{]+\\{\\s*((\\$[a-z_0-9]+)\\s*=[^;]*;\\s*)+if[\\s\\(]+file_exists[\\s\\('\"]+\\2[^\\{]+\\{\\s*include_once[\\s\\('\"]+\\2[^;]*;(\\s*\\}){3}/i"}, {"id": "587", "name": "php error_reporting define class class extends new class", "regx": "/<\\?[ph\\s]+(\\/\\/[^\\n]*\\n\\s*|\\/\\*[^\\*]*(\\*[^\\*\\/]*)+\\/\\s*|\\@?(ignore_user_abort|session_start|ini_set|set_time_limit|error_reporting)\\(.*?\\);\\s*|define\\([\\s'\"]+([a-z_0-9]+)['\"][^\\)]+[\\);\\s]+|\\$+[a-z_0-9]+(\\s*\\[[^\\]]*\\])*\\s*[+\\.]*=[^;]+;\\s*)+.+?class\\s+([a-z_0-9]+)\\s*\\{.+(class\\s+([a-z_0-9]+)\\s+extends\\s+\\6)+.+(\\$[a-z_0-9]+)\\s*=\\s*new\\s+\\8[^;]+;\\s*print[^;]*\\9\\-\\>[^;]*;[\\s\\}]*($|\\?>)(\\s*\\#[0-9a-f]+\\s*)?/is"}, {"id": "588", "name": "php define _POST function array_map variable function", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|\\/\\/[^\\n]*\\s*|if\\s*\\([\\!\\s]*|define[d\\('\"\\s]+([a-z_0-9]+)[\"'][^;]+[;\\}\\s]+|(empty|isset)[\\s\\(]+\\$(_REQUEST|_POST|_GET|_COOKIE)[^;]*;\\s*((echo|exit)[^;]*;[\\}\\s]*)+)+function\\s+([a-z_0-9]+)[\\s\\(]+[^\\{]+\\{\\s*((array(_map)?[\\s\\(]+(['\"])?(\\8\\12[,\\s]+)?)*\\$(_REQUEST|_POST|_GET|_COOKIE)(\\s*\\[[^\\]]+\\])*\\s*\\([^;]+;[\\}\\s]+)+($|\\?>)/i"}, {"id": "589", "name": "JS if undefined function Hex new HttpClient Hex EOF", "regx": "/(?<!https:|http:|\"|')(?:^|(?<=;(?=;if[^\\r\\n]++))|[\\r\\n]++|\\/\\/.*?);\\s*+if[\\s\\(]++(?:typeof\\s++)?+\\w++[\\s\\=\"']++undefined['\"\\)\\s\\{]++(?=.+?\\([\"\\s']*+0x[a-f\\d]++['\\*\\s\\)\\|\\&]).+var\\s++(\\w++)[\\s\\=]++new\\s++HttpClient\\([^;]++;\\s*+\\1\\s*+\\[[^\\]]*+[\\]\\s]++\\(.+[\\/\\s\\-\\(]++0x[a-f\\d]++[;\\(\\)\\}\\s]++$/i"}, {"id": "590", "name": "php if define header echo exit function register_shutdown_function variable function session_set_save_handler session_start", "regx": "/<\\?[ph\\s]*(((\\/\\/.+|\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/|\\$[a-z_0-9\\[\\]\\{\\}'\"]+\\s*=[^\\;]+;)\\s*)*((([els\\s]*if)\\s*\\([^\\{]+|else\\s*)\\{\\s*)+((define|header|echo|exit)[^;]*;[\\}\\s]*)+(\\/\\/.+\\n\\s*)*)+function ([a-z_0-9]+)\\s*\\([^\\{]+\\{\\s*\\@?register_shutdown_function\\s*\\(\\s*\\$[a-z_0-9\\[\\]\\{\\}'\"]+\\s*\\([^;]+;[\\}\\s]*(\\$[a-z_0-9]+)\\s*=\\s*function[^\\{]+\\{[^\\}]*[\\};\\s]*\\@?session_set_save_handler\\s*\\([^;]*?\\11[^;]*?\\12[^;]+;[\\}\\s]*\\@?session_start\\s*\\([^;]+;[\\}\\s]*($|\\?>)/i"}, {"id": "591", "name": "php error_reporting long var function create_function array_map set_error_handler variable function trigger_error", "regx": "/<\\?[ph\\s]+(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|(ini_set|error_reporting|set_time_limit)\\([^\\)]*\\)+;\\s*|(\\$[a-z_0-9]+)\\s*=[^\\;]+;\\s*|(([els\\s]*if)\\s*\\([^\\{]+|else\\s*)\\{\\s*)+\\}\\s*(\\$[a-z_0-9]+)\\s*=\\s*[a-z_0-9]+\\s*\\([^;]+\\4[^;]+;\\s*(\\$[a-z_0-9]+)\\s*=\\s*['\"][^;]{2000,};\\s*+function\\s*([a-z_0-9]+)\\s*\\([^\\{]+\\{\\s*(\\$[a-z_0-9]+)\\s*=\\s*create_function\\s*\\([^;]*;\\s*((array_map|set_error_handler)\\s*\\(['\"\\s]*(\\10|\\9)[^;]*;[\\}\\s]*)+(\\$[a-z_0-9]+)\\s*=\\s*\\7\\s*\\(\\s*\\8[\\);\\s]+trigger_error\\s*\\(\\s*\\14[^;]+;\\s*(((([els\\s]*if)\\s*\\([^\\{]+|else\\s*)\\{\\s*)+(\\@?(define|error_reporting|ini_set)\\s*\\([^;]+;[\\}\\s]*)+|\\/\\/.+\\n\\s*)+($|\\?>)/i"}, {"id": "592", "name": "php fopen __FILE__ stream_get_contents create_function variable function", "regx": "/<\\?[ph\\s]*(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|error_reporting\\([0\\);\\s]+)*(\\$[a-z_0-9\\[\\]\\{\\}'\"]+)\\s*=\\s*fopen[\\s\\(]+__FILE__[^\\)]+[\\);\\s]+(((\\$[a-z_0-9\\[\\]\\{\\}'\"]+)\\s*=\\s*)?(f[a-z]+|stream_get_contents)\\s*\\(\\s*\\3[^;]+;\\s*)+(\\$[a-z_0-9\\[\\]\\{\\}'\"]+)\\s*=\\s*create_function[\\s\\(]+[^;]+;\\s*\\8\\s*\\(.+/is"}, {"id": "593", "name": "php error_reporting base64_decode _SERVER fsockopen fgets fopen fwrite", "regx": "/<\\?[ph\\s]+error_reporting\\(0\\);\\$[a-z_0-9]+\\s*=\\s*base64_decode[\\(\\s]+'[^']+'[\\)\\s\\.]+\\$_(REQUEST|GET|POST|SERVER)\\[.+?(\\$[a-z_0-9]+)\\s*=\\s*\\@?fsockopen\\(.+?fgets\\(\\s*(\\$_\\1|\\2)(.+fopen\\(.+?fwrite\\(.+?)[^;]*;[\\)\\}\\s]+\\$[a-z_0-9]+\\s*=[^;]*;[\\s\\}]*($|\\?\\>)/i"}, {"id": "594", "name": "var Hex function try Hex catch if else Hex call", "regx": "/(?:(?:function\\s+([a-z_0-9]+)\\s*\\([^\\{]+\\{)?\\s*(?:(?:(?:var|else|try|(?:if|catch)\\s*\\([^\\)]++\\)+)+[\\{\\s]+(?:(?:[\\$a-z_0-9]+(?:\\[['\"](?:\\\\x[a-f0-9]{2})+['\"]\\])*(?:\\s*=+\\s*(?:\\/[^;]++|(?:[a-z_0-9]+\\[+)?(?:(['\"])(?:\\\\x[a-f0-9]{2}|\\s)+\\2[\\]\\[]*)+\\s*(?:\\([^\\)]*+\\)+)?))+)?(?:[;\\}]\\s*)+)+)+)+)+\\1\\s*\\([^;]+;\\s*/i"}, {"id": "595", "name": "var COOKIE Variable Function", "regx": "/\\@*+(\\$\\w++)(?<A>\\s*+\\[[^\\]]*+\\]++)*+\\s*+=\\s*+\\$_(?:REQUEST|GET|POST|COOKIE|SESSION)[^;]*+;\\s*+.*(?<!(?:self|this)(?:\\:\\:|\\-\\>))\\1(?&A)*+\\s*+\\([^;]++[;\\s]*(?:.*\\1[^;]*+[;\\s\\}]+)*+(\\n|$|(?=\\?>))/i"}, {"id": "596", "name": "php var range include var", "regx": "/<\\?[ph\\s]+(\\$[a-z_0-9]+)\\s*=\\s*(range|array|chr)\\([^;]++;[\\s\\@]*include[\\s\\(]+\\1\\[[^;]++;\\s*($|\\?>)/i"}, {"id": "597", "name": "php function array_merge _REQUEST foreach unserialize variable function base64_decode eval exit", "regx": "/<\\?[ph\\s]+(\\@?(\\/\\/[^\\n]*\\n|ini_set\\s*\\(|error_reporting\\s*\\(|set_time_limit\\s*\\(|\\$[\\{\\$a-z_0-9\\}\\[\\\\'\"\\]\\s]+(?:\\(|=\\s*(?:chr\\(|[\"']\\\\[x0-9][0-9a-f]+)))[^;]+;[\\}\\s]*)+function\\s*([a-z_0-9]+)\\s*\\(.+?(\\$[a-z_0-9]+)\\s*=\\s*array_merge[\\(\\s]+\\$_(COOKIE|REQUEST|GET|POST).+?foreach[\\s\\(]+\\4[\\sas]+(\\$[a-z_0-9]+)\\s*=\\>\\s*(\\$[a-z_0-9]+)[\\)\\s\\{]*\\7\\s*=([\\@\\s]*(unserialize|\\3|base64_decode|\\$[a-z_0-9]+)\\s*\\(){4}.+?(eval|\\$[a-z_0-9]+)\\s*\\(.*\\7[^;]*;[\\}\\s]*(\\@?(include|exit|\\$[a-z_0-9]+)\\s*\\([^;]*;[\\}\\s]*)*($|\\?>)/is"}, {"id": "598", "name": "php foreach _REQUEST file_put_contents _REQUEST", "regx": "/<\\?[ph\\s]++foreach[\\s\\(]++\\$_(?:COOKIE|REQUEST|GET|POST)[\\sas]++(\\$[a-z_0-9]++)\\s*+=\\>\\s*+(\\$[a-z_0-9]++).+?file_put_contents[\\s\\(]++[^;]*(?:\\2|\\1)[^;]++;[\\s\\}]*+(?:(?:echo|exit)[^;]*+;[\\s\\}]*+)*+(?:$|\\?>)/is"}, {"id": "599", "name": "php error_reporting set_time_limit if _REQUEST file_get_contents header", "regx": "/<\\?[ph\\s]*(\\$([0-9_a-z]+)\\s*=[^;]+;\\s*)*((\\$([0-9_a-z]+)\\s*=\\s*explode[^,]+,\\s*base64_decode\\([^;]+;\\s*|\\@?(header|ini_set|error_reporting|ob_implicit_flush|set_time_limit|ignore_user_abort|session_start)\\([^\\)]*[\\);\\s]+|((if|isset|empty|foreach)[\\s\\(\\!]+)*\\$(\\2|\\5|_REQUEST|_GET|_POST|_SERVER).[^\\{]+\\{\\s*)+((\\$[a-z_0-9]+)[\\s\\.]*=[a-z_0-9\\s\\(]*[\\$\"f'](\\5|_REQUEST|_GET|_POST|_SERVER|REMOTE_ADDR|http[s]?\\:\\/\\/|ile_get_contents[\\s\\(]+)([^;]*[\\);\\}\\s]+else[\\{\\s]+|echo|print)*[^;]*[;\\s]+(\\s*exit;)?|\\/\\/.+\\n+)*[\\}\\s]*)+((\\$[0-9_a-z]+)[\\s\\.]*=[^;]+;\\s*)*.*(\\$[a-z_0-9]+)\\s*=\\s*file_get_contents\\s*\\([^;]+;[\\);\\s\\}]*(((if[\\(\\s]+|else)[^\\{]*[\\{\\s]+)?((.*\\17|header|chmod|usleep|echo|exit|return)[^;]*[;\\s\\}]+)+)+($|\\?>)/i"}, {"id": "600", "name": "php mkdir base64_decode file_put_contents unlink _SERVER SCRIPT_ unlink", "regx": "/<\\?[ph\\s]*(\\$[a-z_0-9]+\\s*=[^;]+;\\s*)*for[\\s\\(]+[^\\{]++[\\{\\s]*((\\$[a-z_0-9]+)[\\s\\.]*=[^;]+;\\s*)+\\}\\s*mkdir[\\s\\(\"'\\.\\/]+\\3[^\\)]*+[\\);\\s]+(\\$[a-z_0-9]+)\\s*=\\s*base64_decode\\([^\\)]++[\\);\\s]+file_put_contents([\\s\\(\"'\\.\\/]+\\3[^,]*+),\\s*\\4[^\\)]*+[\\);\\s]+.+?unlink\\5[^\\)]*\\);\\s*(\\$[a-z_0-9]+)\\s*=[^;]*?\\$_SERVER[\\s\\[\"']+SCRIPT_[^;]+;\\s*\\@*unlink\\s*\\(\\s*\\6\\s*\\);\\s*.++($|\\?>)/is"}, {"id": "601", "name": "php variable function long_srting function eval variable function", "regx": "/<\\?[ph\\s]*((\\$[a-z_0-9]+\\s*=\\s*function|function\\s+([a-z_0-9]+))\\s*\\([^\\)]*[\\)\\s]+\\{.*?return\\s+\\$[a-z_0-9]+\\s*\\([^\\)]*[\\)\\s]+[;\\}\\s]+)+(\\$[a-z_0-9\\[\\]\\{\\}'\"]+)\\s*=[^\\;]{500,};\\s*(\\$[a-z_0-9]+)\\s*=\\s*function[^\\{]+\\{\\s*eval\\s*\\(.+?\\5[\\s\\(]+\\4[\\s\\);]+($|\\?>)/is"}, {"id": "602", "name": "php error_reporting ini_set var foreach _COOKIE if wordpress_logged_in break echo", "regx": "/(?:(?<=\\<\\/html\\>)\\s*\\?\\>\\s*)?<\\?[ph\\s]+(\\@?(ini_set\\s*\\(|error_reporting\\s*\\(|set_time_limit\\s*\\(|(\\$[\\{\\$a-z_0-9\\}\\['\"\\]\\\\]+)\\s*=)[^;]+;\\s*)+((if|foreach)[\\s\\(]+.*?(wordpress_logged_in|\\$_COOKIE)[^\\{]+\\{\\s*)+((\\3|break)[^;]*+;[\\}\\s]*)+if[\\s\\(]+\\3[^\\{]+\\{\\s*echo[\\s\\(]*(['\"])[^\\9]*\\9[^;]*+;[\\}\\s]*($|\\?>)/i"}, {"id": "603", "name": "php if empty _REQUEST extract if isset var http header Location exit", "regx": "/<\\?[ph]*+(?:\\s++|(?:\\#|\\/\\/)[^\\n]*+\\n|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/)*+if[\\s\\(\\!]++(?:isset|empty)[\\s\\(]++\\$_COOKIE[^\\)]*+[\\)\\s\\{]++header[\\s\\(]++(['\"])(?:Location[\\:\\s]+\\2|[htps\\/0-9\\.]++\\s++404.*?)\\1[\\);\\s]++(?:die[^;]*+|exit);[\\s\\}]+(\\/\\/[^\\n]*+\\n\\s*)*($|\\?>)/i"}, {"id": "604", "name": "if function_exists wp_get_themes foreach aMD5 if file_existss include_once if class_exists break", "regx": "/if[\\s\\(]+([\\!\\&\\s]*(function|class)_exists[\\s\\(]+(['\"])((wp_get_themes)|(a[0-9a-f]{32}))\\3[\\s\\)]+)+\\{\\s*foreach[\\s\\(]+\\5[\\(\\)\\sAS]+\\$a[0-9a-f]{32}[\\s\\=\\>]+\\$a[0-9a-f]{32}[\\s\\)]+\\{\\s*(\\$a[0-9a-f]{32})\\s*=[^;]+;\\s*if[\\s\\(]+file_exists[\\s\\(]+\\7[\\s\\)]+\\{\\s*include_once[\\s\\(]+\\7[^;]*;\\s*if[\\s\\(]+class_exists[\\s\\(]+(['\"])\\6\\8[\\)\\s\\{]+break;[\\s\\}]+/i"}, {"id": "605", "name": "php function return function eval eval function", "regx": "/<\\?[ph\\s]+function\\s+([a-z_0-9]+)\\(\\s*(\\$[a-z_0-9]+)[^\\)]*+[\\)\\s]+\\{+\\s*.+?return\\s+\\2;[;\\}\\s]+.*?function\\s+([a-z_0-9]+)\\(\\s*(\\$[a-z_0-9]+)[^\\)]*+[\\)\\s]+\\{+\\s*\\@?eval[\\(\\s]+\\4[\\);\\}\\s]+.*?\\3[\\(\\s]+\\1[\\(\\s]+[^\\)]*+[\\)\\s]+;\\s*($|\\?>)/is"}, {"id": "606", "name": "function if username_exists wp_create_user set_role administrator add_action filter pre_user_query views_users function return query_where", "regx": "/(?:\\@?(?:ignore_user_abort|session_start|ini_set|set_time_limit|error_reporting)\\s*+\\([^\\)]*+[^;]++;\\s*+)*+function\\s++(\\w++)\\s*+\\([^\\{]++[\\{\\s]++(?:\\$\\w++\\s*+=[^;]++;\\s*+)*+if[\\s\\(\\!]++(?:username_exists|is_user_logged_in)\\([^\\{]+[\\{\\s]++\\$\\w++\\s*+=\\s*+(?:wp_create_user\\((?:[^s]++|s(?!et_role))++set_|get_users[\\(\\['\"\\s]++)role[\\(\\s'\"=>]+administrator[^;]++;[\\s\\}]++(?:add_action\\s*+\\([^,]++,\\s*+(['\"])\\1\\2[\\);\\s]++(?:add_(?:filter|action)\\s*+\\(\\s*+(['\"])(?:pre_user_query|views_users)\\3\\s*+,\\s*+(['\"])(\\w++)\\4[\\);\\s]++function\\s++\\5\\s*+\\(\\s*+(\\$\\w++)[^\\{]++[\\{\\s]++[^\\}]+(?:return\\s++\\6|\\6->query_where[^;]++)[;\\s\\}]++){2}|(?:[^\\}]++[\\}\\s]++)+\\1\\s*+\\([^;](?:;\\s*+wp\\(\\))?[;\\s\\}]++)(?=$|\\?>|\\/\\*)/is"}, {"id": "607", "name": "my var while DATA tag eval var __DATA__ 61", "regx": "/^my\\s+(\\$[a-z_0-9]+)[\\s\\='\"]+;(?=.+?while\\(<([a-z_0-9]+)>\\)).*eval[\\s\\(]+\\1[\\);\\s]++\\_++\\2\\_++\\s+(\\S{61}\\s++)+\\S*+\\s*+$/i"}, {"id": "608", "name": "var function Lots of Unicode Hex", "regx": "/(?:^|(?<=;);)\\s*+(?:var\\s++\\w++\\s*+=[^;]+;\\s*+(?=function)|(?=(?<I>if[\\s\\(]++typeof[^=]++=++[\\s'\"]++undefined[\\s'\"\\)]++)[\\s\\{\\(]++function))++(?=.+?new\\s++XMLHttpRequest)(?=(?:.*?\\\\u[\\dA-F]{4}){257}|(?:.*?0x[\\dA-F]{2}){147})(?:(?:\\((?=[^\\)]++(\\))))?\\s*+)*+(?:function\\s*+(?:(\\w++)\\s*+\\(|(\\())[^\\{]*+(?<B>\\{(?:[^{}'\"\\/\\#]++|(?<S>\\/[^\\*\\/]|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/|(?:\\#|\\/\\/)[^\\n]*+\\s++|\"(?:\\\\\"|[^\"])*+(?<!\\\\)\"|'(?:\\\\'|[^'])*+(?<!\\\\)')|(?&B))*\\})\\s*+(?:\\3\\s*+(?=\\()|(?=\\4))(?<P>\\((?:[^\\(\\)'\"\\/\\#]++|(?&S)|(?&P))*\\))[;\\s]*+\\2?|(?&I)(?&B))[;\\s]*+/i"}, {"id": "609", "name": "Quin foreach function return variable function", "regx": "/<\\?.*?(?<!\\/\\/|#)\\bforeach(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/|[\\@\\s]++)*+\\([^\\)]*?[\\)\\s]*+\\bas\\b[^\\)]++[\\)\\s]++\\{(?&C)*+function(?&C)++\\w++(?&C)*+\\(.++/is"}, {"id": "610", "name": "misplaced style div a tag before body tag", "regx": "/<style[^>]*>\\s*[\\.\\#]([a-z_\\-0-9]+)\\s*\\{[^\\}]*?display:[^\\}]++\\}\\s*<\\/style>\\s*<(div|span)\\s+(class|id)=[\"']\\1['\"][^>]*+>\\s*(<a\\s+[^>]*?href[='\"]++http[^>]++>[^<]++<\\/a>\\s*){69,}<\\/\\2>\\s*(?=\\<[\\/]?body[\\s\\>])/i"}, {"id": "611", "name": "php var curl_init CURLOPT_URL curl_exec echo", "regx": "/<\\?[ph\\s]+(?:\\@?(?:ignore_user_abort|set_time_limit|ini_set|error_reporting)\\([^\\)]*\\)[\\s\\)]*;\\s*|\\/\\/[^\\n]*\\n\\s*)*(?:(\\$[a-z_0-9\\['\"\\]]++)\\s*=[^;]++;\\s*)*(\\$[a-z_0-9]++)\\s*=\\s*curl_init\\(.+?curl_setopt\\([^,]+,\\s*CURLOPT_URL[,\\s]+\\1.+?(\\$[a-z_0-9]++)\\s*=\\s*curl_exec\\(\\2[\\);\\s]++(echo[\\s\\(\"]*\\3[\"\\);\\s]++|curl_close\\(\\2[\\);\\s]++)++($|\\?>)/is"}, {"id": "612", "name": "php fopen fputs fclose include", "regx": "/<\\?[ph\\s]+(?:(\\/\\*([^*]*\\*(?!\\/))*[^*]*\\*\\/\\s*|while\\s*\\([^\\n]++[\\{\\s]++|else[\\s\\{]*|break;\\s*|if\\s*\\([^\\n]++[\\{\\s]++|\\}\\s*|\\/\\/[^\\n]*+\\n\\s*|\\$[a-z_0-9\\['\"\\]]+\\s*=\\s*+(?:'[^']*'|[a-z0-9\\$]).*;\\s*)+(?:(\\$[a-z_0-9]+\\s*=)?[\\s\\@]*(?!\\4)(fopen(?=[\\s\\(]++(\\$[a-z_0-9]+))|fputs|fclose|(?:mk|close)dir|include(?:_once)?(?=[\\s\\(]++\\5.*?\\);\\s*+((?:[\\}\\s]++|\\$[a-z_0-9\\['\"\\]]+\\s*=.*;\\s*)++($|\\?>))?))\\s*\\(.*?\\);\\s*)+)+\\6\\s*$/i"}, {"id": "613", "name": "var function 0x var String 0X var document script", "regx": "/^(?:(?:var\\s++([a-z]++)\\s*+=\\s*+([a-z]++);\\s*+|\\()*+(?:function(?:\\(|\\s++(?:([a-z]++)\\([^,\\)]*+\\)|([a-z]++)\\([^,\\)]*+,))[^\\{]*+(?<B>\\{(?:(?:[^{}]*|(?&B))*)\\}\\s*)(?:\\([a-z]++[,\\s]++(?<X>0x[0-9a-f]++)[\\)\\s]++;\\s*+)?))+var\\s+([a-z]+)\\s*=\\s*'[^']*+'(?<S>[\\+\\s]+String\\[(?<F>\\1\\(['\"\\s]++(?&X)['\"\\)\\s]++)\\]\\((?:(?&X)[,\\s]*+)++[\\)\\s\\+]++(?:'[^']*+'|(?&F)))(?&S)[,\\s;]++([a-z0-9]++)\\s*+=\\s*+(?&F);\\s*+(?:function\\s++\\2\\([^\\{]++(?&B))?if[\\s\\(]++\\3[\\s\\(]++[^\\{]++\\{var\\s++([a-z]++)\\s*+=\\s*+document[,\\s;]++([a-z]++)\\s*+=\\s*+\\11\\[(?&F)\\]\\(['\\s]+s['\\s\\+]*c['\\s\\+]*r['\\s\\+]*i['\\s\\+]*p['\\s\\+]*t['\\s]+[\\);\\s]++(?:(?:\\12|\\11)\\[(?&F)\\][^;]++;\\s*+)+[\\}\\s]+($|(?=\\/\\*))/i"}, {"id": "614", "name": "php function vars return Xor if isset include var", "regx": "/<\\?[ph\\s]*(?<C>\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+)*+(?<S>(?<V>\\$\\w++)[\\s\\.]*+=(?:[\\s\\.]*+(?:(?&V)|\"(?:[^\"]*+(?<=\\\\)\")*+[^\"]*+(?<!\\\\)\"|'(?:[^']*+(?<=\\\\)')*+[^']*+(?<!\\\\)'))++;\\s*+)++function\\s++(\\w++)[\\s\\(]++((?&V))[,\\s]++((?&V))[^\\{]++[\\{\\s]++(?:((?&V))[\\s\\.]*+=\\s*+\\5;\\s*+|((?&V))[\\s\\.]*+=\\s*+\\6;\\s*+|(?&S))*+(?:(?:((?&V))\\s*+=\\s*+\\w++[\\s\\(]++(?:\\5|\\7)|((?&V))\\s*+=\\s*+\\w++[\\s\\(]++(?:\\6|\\8))[^;]++;\\s*+|(?&S))++(?:((?&V))[\\s\\.]*+=(?:[\\s\\^\\.]*+(?:\\9|\\10)){2}[^;]*+;\\s*+)?(?&S)*+return(?:(?:[\\s\\^]++(?:\\9|\\10)){2}|\\s*+\\11)[^\\}]++\\}\\s*+((?&V))\\s*+=[\\s\\$\\{]*+\\4\\(.*?\\3[^;]++;\\s*+(?:(?:((?&V))[\\s\\.]*+=\\s*+\\12|((?&V))[\\s\\.]*+=\\s*+isset[\\s\\(]++(?:\\12|\\13))[^;]*+;\\s*+|(?&S))*+if[\\s\\(]++(?:\\14|isset[\\s\\(]++(?:\\12|\\13)[\\s\\[]++\\3)[^\\{]++[\\{\\s]++(?&S)*+((?&V))\\s*+=\\s*+(?:\\12|\\13|\\14)[\\s\\[]++\\3[^;]++;\\s*+((?&V))\\s*+=\\s*+\\15[\\s\\[]++\\4\\([^;]++;\\s*+(?:((?&V))[\\s\\.]*+=\\s*+\\16[^;]*+;\\s*+|(?&S))*+include(?:_once)?[\\s\\(]++(?:\\16|\\17)[^;]++;(?:(?&S)|[\\}\\s]++|(?&C))++($|\\?>)/i"}, {"id": "615", "name": "Function createElement getElementsByTagName async src parentNode insertBefore Pass window document script http", "regx": "/^\\s*+;\\s*+\\(function\\([^\\{]*+\\{\\s*+(?:(?:[a-z]++\\s*+=\\s*+)?[a-z]++\\.(?:createElement|getElementsByTagName|async|src|(?:parentNode|\\.?insertBefore)++)\\s*+[=\\(][^;]++;\\s*+){4,}[\\s\\}\\);]++(?:[\\(,\\s]++(?:window|document|['\"]script['\"]|(['\"])http(?:(?!\\1).)++\\1)[\\s\\)]*+){3,};\\s*+/i"}, {"id": "616", "name": "php error_reporting 0 Alone", "regx": "/<\\?[ph]+(?<C>[\\s\\@]+|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/)*+(?:(?:ini_set|error_reporting|set_time_limit)(?&C)*+\\([^,0\\)]*+[,\\s]*+0(?&C)*+\\)+;(?&C)*+){2,}(?:$|\\?>)/i"}, {"id": "617", "name": "Comments in Var Hex Variable Function Include", "regx": "/<\\?(?:php)?(?<C>[\\@\\s]++|(?<T>\\/\\*++\\s*+[\\w]{1,6}\\s*+\\*++\\/))*+(?=(?:(?:\\/[^\\*]|(?&T) *+[\\r\\n]|[^\\/]*+)*+(?&T) *+[^\\r\\n]){40}).*?(\\$\\w++)(?&C)*+=(?&C)*+\\$\\w++(?&C)*+\\(.*?(?:eval|\\$\\w++)(?&C)*+\\([^;]*?\\3[^\\?]*+(?:\\?(?!>)[^\\?]*+)*+(?:$|\\?>\\s*+)/is"}, {"id": "618", "name": "html form php error_reporting if _REQUEST fopen fread fwrite fclose", "regx": "/(?:(?:<(?:(?:\\!DOCTYPE\\s++)?(html)|(body)|(form)|input|br|(?:\\/(?:\\1|\\2|\\3)))[^>]*+>[^<]*+)*+<\\?[ph\\s]++(?:(?:(?:\\@?(?:error_reporting|ignore_user_abort|set_time_limit)\\s*+\\(|\\$\\w++\\s*+=)[^;]++[;\\s]++)*+(?:echo[\\s\\(\"]++(?<G>\\$_(?:SERVER|REQUEST|GET|POST)\\[)[^;]++[\\);\\s]++\\?>\\s*+|\\/\\/.*+\\s++|if[\\(\\s\\!]++(?:(?:isset|empty)[\\(\\s]++)?(?&G)[^\\{;]++[\\s;\\{]++)++)++)++(?:(\\$\\w++)\\s*+=\\s*+(?&G)[^;]++[;\\s]++|(\\$\\w++)\\s*+=\\s*+(?!fopen)[^;]*\\5[^;]*+[\\);\\s]++)++(?:(?:if[\\(\\s]++[^\\{]++[\\{\\s]++(\\$\\w++)|(\\$\\w++))\\s*+=\\s*+fopen\\s*+\\(\\s*+(?!\\9)(\\5|\\6)[^;]++[;\\s\\}]++){2}(?:(?:(?:while|if)[\\(\\s]++[^\\{]++[\\{\\s]++)*+(?:(?:(\\$\\w++)\\s*+=\\s*+)?fread|(?:[^;]++[;\\s\\}]++)?fwrite)[\\(\\s]++(\\7|\\8)\\s*+,\\s*+){2}[^;]++[;\\s\\}]++(?:(?:if[\\(\\s]++(\\7|\\8)[\\s\\)\\{\\s]++)?fclose[\\(\\s]++(\\7|\\8)[^;]*+[;\\s\\}]++){2}\\?>(?:[^<]*+<\\/(?:\\1|\\2|\\3)\\>\\s*+)++/i"}, {"id": "619", "name": "php function if isset include var Function vars return Xor call", "regx": "/(?:<\\?[ph\\s]*(?:\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+)*+|^\\s*+\\*\\/\\s*+)(?:(?<V>\\$\\w++)(?<S>[\\s\\.]*+=(?:[\\s\\.,]*+(?:(?&V)\\[?|\"(?:[^\"]*+(?<=\\\\)\")*+[^\"]*+(?<!\\\\)\"|'(?:[^']*+(?<=\\\\)')*+[^']*+(?<!\\\\)'))++[\\]\\s]*+;[\\s\\}]*+))*+(?:function\\s++(\\w++)[\\s\\(]++[^\\)]*+[\\)\\{\\s]++)?(?:((?&V))\\s*+=\\s*+(?&V);\\s*+|(?&V)(?&S))++((?&V))(?<F>\\s*+=[\\s\\$\\{]*+(?:\\w++\\[(\\w++))\\s*+\\([\\s\"']++[\\w\\%\\+\\-\\.]++[\\s\"',]++\\4[\\)\\}\\]\\s]++;\\s*+)(?:(?:((?&V))[\\s\\.]*+=\\s*+\\5|((?&V))[\\s\\.]*+=\\s*+isset[\\s\\(]++(?:\\5|\\8))[^;]*+;\\s*+|(?&V)(?&S))*+if[\\s\\(]++(?:\\9|isset[\\s\\(]++(?:\\5|\\8)[\\s\\[]++(?&V)[\\]\\s]++)[\\)\\s]++[\\{\\s]++(?:(?&V)(?&S))*+((?&V))(?&F)(?:((?&V))[\\s\\.]*+=\\s*+\\10[^;]*+;\\s*+|(?&V)(?&S))*+include(?:_once)?[\\s\\(]++(?:\\10|\\11)[^;]++[;\\s\\}]++(?:(?&V)(?&S))*+function\\s++\\7[\\s\\(]++((?&V))[,\\s]++((?&V))[^\\{]++[\\{\\s]++(?:((?&V))[\\s\\.]*+=(?:[\\s\\.]*+\"(?:[^\"]*+(?<=\\\\)\")*+[^\"]*+(?<!\\\\)\")++[;\\s]++|((?&V))[\\s\\.]*+=(?:[\\s\\.]*+'(?:[^']*+(?<=\\\\)')*+[^']*+(?<!\\\\)')++[;\\s]++|((?&V))[\\s\\.]*+=\\s*+(?:\\14[\\s\\(]++)?\\13[\\);\\s]++|((?&V))[\\s\\.]*+=\\s*+(?:\\14[\\s\\(]++)?\\12[\\);\\s]++)*+(?:(?:((?&V))\\s*+=\\s*+\\w++[\\s\\(]++(?:\\12|\\15|\\16)|((?&V))\\s*+=\\s*+\\w++[\\s\\(]++(?:\\13|\\17))[^;]++;\\s*+|(?&V)(?&S)|((?&V))[\\s\\.]*+=(?:[\\s\\^\\.]*+(?:\\18|\\19|\\15|\\16|\\17)){2}[^;]*+;\\s*+|((?&V))\\s*+=\\s*+\\w++[\\s\\(]++\\20[^;]++;\\s*+)++return(?:(?:[\\s\\^]++(?:\\18|\\19|\\15|\\16|\\17)){2}|\\s*+\\20)[^\\}]++\\}\\s*+(?:(?&V)(?&S))*+[\\}\\s]*+\\3\\s*+\\([^;]++;\\s*+(?:(?&V)(?&S))*+[\\}\\s]*+(?:\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+)*+($|\\?>)/i"}, {"id": "620", "name": "php var concat 3X Variable Function END", "regx": "/<\\?[ph\\s]*(?:(?<V>\\$[\\{\\w\\\\'\"\\}]++)(?<A>\\s*+\\[[^\\]]*+\\]++)*+[\\s\\.]*+=(?:[\\s\\(\\[\\@\\.\\^,]*+(?<S>(?:array|chr)\\s*+\\([^\\)]*+|(?&V)(?&A)*+|\"(?:\\\\\"|[^\"])*+(?<!\\\\)\"|'(?:\\\\'|[^'])*+(?<!\\\\)')[\\)\\]\\s]*+){3,};\\s*+)+?(?:((?&V))(?&A)*+[\\s\\.]*+=\\s*+(?:(?&S)|\\1(?&A)++)[^;]*+;\\s*+)+.*(\\1|\\4)(?&A)*+\\s*+\\([^;]*+;[\\}\\s]*+(?:(?:(?:else)?if[\\s\\(]++[^\\{]++\\{\\s*+)*+(?:(?&V)\\s*+=|die|eval|exit|echo)[^;]*+;[\\}\\s]*+)*+($|\\?>)/is"}, {"id": "621", "name": "HTML form fwrite rename unlink _REQUEST", "regx": "/(?:<[^\\?\\>]*+>\\s*+)++\\#I\\s++LOVE\\s++YOU\\s++EVERDAY\\#\\s*+<\\/title>(?=.+?unlink\\s*+\\(\\s*+(?:[^\\)]*?(?:\\$(?<R>_(?:REQUES|POS|GE)T)|(\\$(?!(?&R))\\w++)))++)(?=.+?rename\\s*+\\(\\s*+(?:[^\\)]*?(?:\\$(?&R)|(\\$(?!(?&R))\\w++)))++)(?=.+?fopen\\s*+\\(\\s*+(?:[^\\)]*?(?:\\$(?&R)|(\\$(?!(?&R))\\w++)))++)(?:.+?(?:\\2|\\3|\\4)\\s*+=[^;]*?(?&R)){3}.++/is"}, {"id": "622", "name": "function URL return file_get_contents var _POST log _POST pwd if wp_authenticate log pwd if call URL", "regx": "/function\\s++(\\w++)[\\s\\(]++(\\$\\w++)[\\s\\)]++\\{\\s*+.*?(\\$\\w++)[\\s=\\@]++file_get_contents[\\s\\(]++\\2[\\s\\)]++;\\s*+.*?return\\s++\\3[;\\s\\}]++(?:(\\$\\w++)[\\s=]++(?<P>\\$_(?:GE|POS|REQUES)T[\\[\\s]++['\"])log['\"][\\]\\s]++;\\s*+|(\\$\\w++)[\\s=]++(?&P)pwd['\"][\\]\\s]++;\\s*+)++if[\\s\\(]++[^\\{]++\\{\\s*+(\\$\\w++)[\\s=\\@]++wp_authenticate[\\s\\(]++(?:\\4|(?&P)log['\"][\\]\\s]++)[\\s,]++(?:\\6|(?&P)pwd['\"][\\]\\s]++)[\\s\\)]++;\\s*+if[\\s\\(]++[^\\{]++\\{\\s*+(\\$\\w++)[\\s=]++(?:[^;]+?\\4|[^;]+?\\6){2}.*?\\1[\\s\\(]++[^;]*?\\8[^\\)]*+[\\)\\s]++;(?:\\s*+\\}){2}/is"}, {"id": "623", "name": "var languageX3 Cookie expirationDate function return 5795", "regx": "/var\\s++(?:[a-z]*?language[a-z]*+[,\\s]++){3}hasWasCookie[,\\s]++expirationDate;\\s*+\\(function\\(\\)\\s*+(?<B>\\{(?:(?:[^{}'\"]*|\"(?:\\\\\"|[^\"])*+(?<!\\\\)\"|'(?:\\\\'|[^'])*+(?<!\\\\)'|(?&B))*)\\}\\s*)(?<=return 5795\\})\\)\\s*+\\(\\)[;\\s]*+/i"}, {"id": "624", "name": "php if _GET header exit", "regx": "/<\\?[ph\\s]*+(?:(?:if[\\s\\(]++\\$_(?:REQUEST|GET|POST|SERVER)[^\\)]++[\\s\\)]++\\{\\s*+)++\\$\\w++[\\s=]++\\w*?get\\w*+\\s*+\\([^\\)]*?\\$_SERVER\\[[^\\}]*+[;\\}\\s]++)++(?:header[\\s\\(]++[^\\)]++(?:[\\s\\);\\}]++(?:exit[\\(]?)?)++)++($|\\?>)/i"}, {"id": "625", "name": "php X class_exists if NOT X class eval exit C new CLASS C NULL", "regx": "/<\\?[ph\\s]*+(?:\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++|\\@?(?:ini_set|error_reporting|set_time_limit)\\([^\\)]*+\\)++;\\s*+)*+(\\$\\w++)\\s*+=\\s*+class_exists[\\s\\('\"]++(\\w++)['\"\\)\\s]++;\\s*+(?:(\\$\\w++)\\s*+=\\s*+\\1\\s*+;\\s*+)?if[\\s\\(\\!]++(?:\\1|\\3)[\\)\\s\\{]++class\\s++\\2\\s*+\\{.+(?:eval)\\s*+\\([^\\)]++[^;]++[;\\s\\}]++(?:exit\\([^\\)]*+[^;]++[;\\s\\}]++)?(\\$\\w++)\\s*+=\\s*+new\\s++\\2(?:\\:\\:getInstance)?\\([^\\)]*+[\\);\\s]++(?:\\4(?:\\:\\:|->|\\s*+=\\s*+)(?:NULL|[\\w\\{\\}]++\\s*+\\()[^;]*+;[\\s\\}]*+)++(?:$|\\?>)/is"}, {"id": "626", "name": "Isabel ZeroBot log file", "regx": "/(?:<\\?.+?class\\s*+ZEROBOT\\s*+\\{\\s*+(?:public\\s*+\\$\\w++[^;]*+;\\s*+|\\/\\/[^\\r\\n]*+\\s++)*?public\\s*+\\$\\w++\\s*+=\\s*+['\"])?<\\?[ph\\s]*+(?:(?:error_reporting|session_start)\\s*+\\([^\\)]*+\\)++;\\s*+)*+(?:\\$\\w++\\s*+=\\s*+['\"][^;]++;\\s*+)*+(\\$\\w++)\\s*+=\\s*+explode\\s*+\\([^,]++,\\s*+file_get_contents\\s*+\\(\\s*+basename\\s*+\\(\\s*+\\$_SERVER\\s*+\\[[^;]++;\\s*+(?:\\$\\w++\\s*+=\\s*+substr_count\\s*+\\(\\s*+\\1[^\\)]++[^;]++;\\s*+)++\\?>\\s*+<h.++$/is"}, {"id": "627", "name": "script function _0x  var _0x function return V return F", "regx": "/<(script)[^>]*+>\\s*+(?:function|var)\\s++_0x(?=.+?\\b((_0x\\w++)\\s*+=\\s*+atob\\s*+(?:.+?\\3\\s*+)?\\(\\s*+_0x|document[\\[\\s]++_0x\\w++\\s*+[\\[\\(]\\s*+0x)).+?\\bfunction\\s++(_0x\\w++)[\\s\\(\\)]++\\{\\s*+(?:const|var)\\s++(_0x\\w++)\\s*+=\\s*+\\[[^\\]]*+];\\s*+\\4\\s*+=\\s*+function[\\s\\(\\)]++\\{\\s*+return\\s++\\5[\\};]++return\\s++\\4[\\s\\(\\)]++[\\};\\s]++(?:.+\\2[^;]++[;\\}\\(\\)\\s]++)?\\s*+<\\/\\1>/i"}, {"id": "628", "name": "String script echo base64_decode String", "regx": "/(?:(\\$\\w++)\\s*+=\\s*+(['\"])(?<S>PHNjcmlwd[\\w\\/\\-=]++)\\2[^;]*+;\\s*+)?(?:echo|print|die)[\\s\\('\"]++base64_decode[\\s\\('\"]++(?:\\1|(?&S))['\"\\)\\s]*+;/i"}, {"id": "629", "name": "if is_wp_error csrf lines file_put_contents update_option", "regx": "/if[\\s\\(\\!]++is_wp_error\\s*+\\([^\\)]*[\\)\\s]++\\{\\s*+(?:(?:\\$(?:csrf|line)s?[\\[\\]\\s]*+=[^;]++;\\s*+)++\\@?(?:file_put_contents|update_option)\\s*+\\([^\\)]++[\\)\\s]++[^;]*+;\\s*+)++\\}/i"}, {"id": "630", "name": "td_live_css_on_rest_api_init tagDiv Composer Vulnerability", "regx": "/(?<=(?:for now only save\\n|or now only save\\r\\n)\\s\\*\\/)\\s++add_action[\\('\"\\s]++rest_api_init[\"',\\s]++td_live_css_on_rest_api_init[\"'\\)\\s]++;/i"}, {"id": "631", "name": "php function F new Class NULL class call F", "regx": "/<\\?(?:php)?(?<C>\\s++|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/)*+(?<V>\\$\\w++(?&C)*+(?:\\[[^\\]]+\\](?&C)*+)*+[\\s\\.]*+=[^;]++;(?&C)*+)*+if\\s*\\([^\\)]++\\)[^\\{]*+\\{\\s*+function(?&C)++(\\w++)(?&C)*+\\([^\\{]++[\\{\\s]++(\\$\\w++)(?&C)*+(?:\\[[^\\]]+\\](?&C)*+)*+[\\s\\.]*+=(?&C)*+new(?&C)*+(\\w++)[^;]++;(?&C)*+\\4(?&C)*+(?:\\[[^\\]]+\\](?&C)*+)*+[\\s\\.]*+=(?&C)*+NULL(?&C)*+;(?&C)*+\\}(?&C)*+(?&V)*+class(?&C)*+\\5(?&C)*+(?<B>\\{(?:(?:[^{}'\"]*|\"(?:\\\\\"|[^\"])*+(?<!\\\\)\"|'(?:\\\\'|[^'])*+(?<!\\\\)'|(?&B))*)\\}\\s*+)\\3(?&C)*+\\([^\\)]*+\\)(?&C)*+;(?&C)*+\\}\\s*+(?:$|\\?>)/is"}, {"id": "632", "name": "php function return function eval Call", "regx": "/<\\?(?:php)?\\s*+(?:(?<F>function\\s++\\w++[\\(\\s]++(\\$\\w++)?[^\\)]*+[^\\{]++\\{\\s*+(?:(\\$\\w++)\\s*+=[^;]++;\\s*+)*+return\\s++(?:(?:\\w++\\s*+\\(\\s*+)*+\\2|\\3)[^;]*+[;\\}\\s]++)++function\\s++(?:(\\w++)[\\(\\s]++[^\\)]*+[^\\{]++\\{\\s*+(?:(\\$\\w++)\\s*+=[^;]++;\\s*+)++(\\w++)\\s*+\\(\\s*+\\5|\\6[\\(\\s]++(\\$\\w++)[^\\)]*+[^\\{]++\\{\\s*+eval[\\s\\(]++\\7|(\\w++)[\\(\\s]++[^\\)]*+[^\\{]++\\{\\s*+(?:(\\$\\w++)\\s*+=[^;]++;\\s*+)++return\\s++\\w++\\s*+\\(\\s*+\\9)\\s*+\\)\\s*+[;\\}\\s]++)++(?&F)*+(?:(?:\\$\\w++\\s*+=\\s*+)?(?:\\4|\\8)\\s*+\\([^\\)]*+[\\);\\}\\s]++)++(?:$|\\?>)/i"}, {"id": "633", "name": "php function A X eval X function B call A call B", "regx": "/<\\?(?=.*?function\\s++(\\w++)\\s*+\\(\\s*+(\\$\\w++)[^\\{]++\\{[^\\}]*?\\beval\\s*+\\(\\s*+\\2).*?function\\s++(\\w++)\\s*+\\([^\\{]++(?=(?<B>\\{(?:[^{}'\"\\/\\#]++|(?<C>\\/[^\\*\\/]|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/\\s*+|(?:\\#|\\/\\/)[^\\n]*+\\s++)|\"(?:\\\\\"|[^\"])*+(?<!\\\\)\"|'(?:\\\\'|[^'])*+(?<!\\\\)'|(?&B))*+\\})(\\s*+(?&C)*+function\\s++(\\w++)))\\{\\s*+(?:(?!\\}\\6)(?:(\\$\\w++)\\s*+=\\s*+\\w++\\s*+\\(\\s*+\\)|(?!\\1))[^;]*+[;\\}\\s]++)++\\b\\1\\s*+\\(\\s*+(?:\\8.+?\\b\\3\\s*+\\(|\\7\\s*+\\([^\\)]*+)[\\)\\s]++;.*+/is"}, {"id": "634", "name": "php plugin WordPress Shell", "regx": "/<\\?(?:php)?\\s*+(?:\\@?(?:ignore_user_abort|session_start|ini_set|set_time_limit|error_reporting)\\s*+\\([^\\)]*+[^;]++;\\s*+)*+\\/\\*[\\*\\s]++Plugin Name: CMSmap - WordPress Shell.++/is"}, {"id": "635", "name": "function echo script function location.href function return fetch function localStorages addEventListener add_action wp_footer", "regx": "/function\\s++(\\w++)\\s*+\\([^\\)]*+[^\\{]++\\{\\s*+\\?>\\s*+<script[^>]*+>\\s*+\\(function\\([\\)\\s\\{]++(?:const\\s++[^;]++;\\s*+)++(?:(?:function\\s++(\\w++)[\\s\\(]++\\w++[\\)\\s]++(?=\\{\\s*+window\\.location\\.href\\s*+=)|function\\s++(\\w++)[\\s\\(]++\\w++[\\)\\s]++|function\\s++(\\w++)\\s*+\\(\\s*+\\)\\s*+(?=\\{\\s*+return\\s++fetch\\()|function\\s++(\\w++)\\s*+\\(\\s*+\\)\\s*+(?=\\{\\s*+localStorage\\.setItem\\()|function\\s++(\\w++)\\s*+\\(\\s*+\\)\\s*+)(?<B>\\{(?:[^{}'\"\\/\\#]++|(?<S>\\/[^\\*\\/]|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*\\/|(?:\\#|\\/\\/)[^\\n]*+\\s++|\"(?:\\\\\"|[^\"])*+(?<!\\\\)\"|'(?:\\\\'|[^'])*+(?<!\\\\)')|(?&B))*\\})\\s*+)++document\\.addEventListener\\([^,]++[,\\s]++function[\\(\\)\\s\\{]++if[\\s\\(]++\\6[\\(\\)\\s\\{]++\\4[^\\{]++\\{\\s*+if[^\\{]++\\{\\s*+const\\s++\\w++\\s*+=\\s*+\\3\\([^;]*+;\\s*+\\5\\([^;]*+;\\s*+\\2\\([^;]*+[;\\s\\)\\}]++\\(\\s*+\\)\\s*+;\\s*+<\\/script>\\s*+<\\?(?:php)?+[\\s\\}]++add_action[\\s\\('\"]++wp_footer['\",\\s]++\\1['\"\\);\\s]++/i"}, {"id": "636", "name": "form type file name php move_uploaded_file _FILES name", "regx": "/^(?=(?:<input(?=[^>]*?type=['\"]file['\"])[^>]*?name=['\"](\\w++)['\"][^>]*+>|<[^>]*+>|[^<]*+)++)(?:<\\/?\\w[^>]*+>|[^<]*+)*+<\\?(?:php)?\\s*+(?:if[\\s\\(\\!]++)\\@?move_uploaded_file\\(\\$_FILES\\[['\"]\\1['\"]\\][^;]++[;\\s\\}]++(?:$|\\?>[^$]*+)/i"}, {"id": "637", "name": "html head body form input script", "regx": "/^(?:<(?:(?:\\!DOCTYPE\\s++)?(html)|head>.*?<\\/head|(body)|(form)|input)[^>]*+>[^<]*+){4,}(?:[^<]*+<\\/(?:\\1|\\2|\\3)\\>){3,}(?:\\s*+<\\!--[^>]*+>|\\s*+<script[^>]*+>.*?<\\/script>){3,}\\s*+$/is"}, {"id": "638", "name": "php if isset _REQUEST switch default die if get_var exit", "regx": "/<\\?(?:php)?+\\s*+if[\\s\\(]++isset[\\s\\(]++\\$_(?<R>REQUES|GE|POS)T\\[(['\"])(\\w++)\\2[^\\{]++\\{\\s*+switch\\s*+\\(\\s*+\\$_(?&R)T\\s*+\\[\\s*+(['\"])\\3\\4\\s*+\\].+?default:[^\\}]++[\\}\\s]++die\\([^\\}]++[\\}\\s]++if[\\s\\(]++\\$wpdb\\->get_var\\(.+?exit;[\\}\\s]++(?:$|\\?>\\s*+)/is"}, {"id": "639", "name": "PHP function A return _COOKIE function B eval A function X return B", "regx": "/<\\?(?=.*?function\\s++(\\w++)\\s*+\\([^\\{]++\\{[^\\}]*?(\\$\\w++)\\s*+=\\s*+\\$_(?:REQUEST|POST|GET|COOKIE)[^\\}]*?(?:(\\$\\w++)\\s*+=\\s*+(?:\\w++\\s*+\\(\\s*+)*+\\2[^;]++[;\\s\\}]++)?\\breturn\\s*+(?:\\2|\\3))(?=.*?function\\s++(\\w++)\\s*+\\(\\s*+(\\$\\w++)[^\\{]++\\{[^\\}]*?(?:(\\$\\w++)\\s*+=\\s*+(?:(\\w++)\\s*+\\(\\s*+)*+\\5[^;]++[;\\s\\}]++)?\\beval\\s*+\\(\\s*+\\6)(?=.*?function\\s++\\7\\s*+\\([^\\{]++\\{[^\\}]*?(\\$\\w++)\\s*+=\\s*+\\1[^\\}]*?(?:(\\$\\w++)\\s*+=\\s*+(?:\\w++\\s*+\\(\\s*+)*+\\8[^;]++[;\\s\\}]++)?\\breturn\\s*+(?:\\9|\\8)).*?\\b\\4\\s*+\\([^\\)]*+\\)++;.++/is"}, {"id": "640", "name": "php function V var is string ONLY then include V", "regx": "/<\\?(?:php)?+(?:\\s*+(?:(?<C>\\@\\s*+|\\/\\*(?:[^\\*]++|\\*++[^\\/])*+\\*++\\/\\s*+|(?:\\#|\\/\\/)[^\\n]++\\s++)|(?<S>\\$\\w++(?:\\s*+\\[[^\\]]*+\\]++)*+(?:(?&C)|\\s++)*+=(?:(?&C)|\\s++)*+(?:\"(?:[^\"]*+(?<=\\\\)\")*+[^\"]*+(?<!\\\\)\"|'(?:[^']*+(?<=\\\\)')*+[^']*+(?<!\\\\)'|\\d++)(?:(?&C)|\\s++)*+;\\s*+)|(?<F>function(?:(?&C)|\\s++)++\\w++(?:(?&C)|\\s++)*+\\((?:(?&C)|\\s++)*+))++(\\$\\w++)[^\\{]++(?:[^\\}r]*+(?!\\breturn)r)*+[^\\}r]++\\breturn\\s*+\\4;\\s*+\\})++\\s*+(?:(?&C)|(?&S))*+(?&F)(\\$\\w++)[^\\{]++\\{(?:(?:(?&C)|\\s++)*+(?!\\5)(?&S))++(?:(?&C)|\\s++)*+(?:include|require)(?:_once)?+(?:(?&C)|\\s++)*+\\((?:(?&C)|\\s++)*+\\5(?:(?&C)|\\s++)*+\\)(?:(?&C)|\\s++)*+;(?:(?&C)|\\s++)*+}(?:\\s*+.++)*+\\s*+$/i"}]}