<?php
// Prevent direct access
if (!defined('GUARDIAN_GAZE_SERVER')) {
    header('HTTP/1.0 403 Forbidden');
    exit;
}

// Return array of patterns
return [
    'php_shell' => '/\b(shell_exec|system|passthru|exec|popen|proc_open)\s*\(/i',
    'eval_base64' => '/\b(eval|assert|create_function)\s*\(\s*(base64_decode|str_rot13|gzinflate|gzuncompress|strrev|convert_uudecode)\s*\(/i',
    'remote_file_inclusion' => '/\b(include|require|include_once|require_once)\s*[\'"(]?\s*(https?|ftp|php|data|glob|phar|zlib|zip|ssh2|rar|ogg|expect):\/\//i',
    // Add more patterns here...
];