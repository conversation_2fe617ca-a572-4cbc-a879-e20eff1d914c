<?php
// guardian-gaze-server/test/check_registration.php

require_once '../config.php';

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Check registered sites
    $stmt = $pdo->query("SELECT * FROM registered_sites ORDER BY created_at DESC LIMIT 5");
    echo "<h2>Last 5 Registered Sites:</h2>";
    echo "<pre>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        print_r($row);
    }
    echo "</pre>";

} catch(Exception $e) {
    echo "Error: " . $e->getMessage();
}