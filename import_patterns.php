<?php
// guardian-gaze-server/import_patterns.php
exit;
require_once 'config.php';

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Read the JSON file
    $json_file = 'data/Exploits.json';
    $json_content = file_get_contents($json_file);
    $patterns = json_decode($json_content, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("JSON decode error: " . json_last_error_msg());
    }

    // Prepare insert statement
    $stmt = $pdo->prepare("INSERT INTO malware_patterns (name, pattern, description, severity) 
                          VALUES (:name, :pattern, :description, :severity)
                          ON DUPLICATE KEY UPDATE 
                          pattern = VALUES(pattern),
                          description = VALUES(description),
                          severity = VALUES(severity)");

    // Insert patterns
    $pdo->beginTransaction();
    
    foreach ($patterns['regex'] as $pattern) {
        $stmt->execute([
            'name' => $pattern['name'],
            'pattern' => $pattern['regx'],
            'description' => $pattern['description'] ?? '',
            'severity' => $pattern['severity'] ?? 'medium'
        ]);
    }

    $pdo->commit();
    echo "Patterns imported successfully!";

} catch(Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    echo "Error: " . $e->getMessage();
}