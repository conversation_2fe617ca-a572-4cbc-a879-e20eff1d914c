<?php
include('includes/config.php');
checkLogin();

$userInfo = getUserInfo($_SESSION['adminUserid']);
if (!$userInfo) {
    session_destroy();
    header("location: " . adminurl . "/index.php");
    exit;
}

// Get statistics
$totalPatterns = $pdo->query("SELECT COUNT(*) as count FROM malware_patterns")->fetch()['count'];
$activePatterns = $pdo->query("SELECT COUNT(*) as count FROM malware_patterns WHERE is_active = 1")->fetch()['count'];
$highSeverity = $pdo->query("SELECT COUNT(*) as count FROM malware_patterns WHERE severity = 'high'")->fetch()['count'];
$recentPatterns = $pdo->query("SELECT COUNT(*) as count FROM malware_patterns WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)")->fetch()['count'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Dashboard | <?php echo sitename; ?></title>
    <link rel="shortcut icon" href="<?php echo logoMini; ?>" type="image/x-icon">
    <link href="assets/bootstrap/css/bootstrap.min.css" rel="stylesheet" />
    <link href="assets/pe-icon-7-stroke/css/pe-icon-7-stroke.css" rel="stylesheet" />
    <link href="assets/dist/css/stylecrm.css" rel="stylesheet" />
    <link href="assets/dist/css/stylecrm_new.css" rel="stylesheet" />
</head>
<body>
    <div class="wrapper">
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
            <div class="container-fluid">
                <a class="navbar-brand" href="dashboard.php">
                    <img src="<?php echo logo; ?>" alt="Logo" height="30">
                    <?php echo sitename; ?>
                </a>
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            Welcome, <?php echo $userInfo['name']; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <div class="container-fluid mt-4">
            <div class="row">
                <!-- Sidebar -->
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-header">
                            <h5>Navigation</h5>
                        </div>
                        <div class="list-group list-group-flush">
                            <a href="dashboard.php" class="list-group-item list-group-item-action active">
                                <i class="pe-7s-graph"></i> Dashboard
                            </a>
                            <a href="patterns.php" class="list-group-item list-group-item-action">
                                <i class="pe-7s-shield"></i> Malware Patterns
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="col-md-9">
                    <div class="row">
                        <div class="col-md-12">
                            <h2>Dashboard</h2>
                            <p class="text-muted">Malware Pattern Management System</p>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card text-white bg-primary">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?php echo $totalPatterns; ?></h4>
                                            <p class="mb-0">Total Patterns</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="pe-7s-shield" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-white bg-success">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?php echo $activePatterns; ?></h4>
                                            <p class="mb-0">Active Patterns</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="pe-7s-check" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-white bg-danger">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?php echo $highSeverity; ?></h4>
                                            <p class="mb-0">High Severity</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="pe-7s-attention" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-white bg-info">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?php echo $recentPatterns; ?></h4>
                                            <p class="mb-0">Recent (7 days)</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="pe-7s-clock" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Patterns -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5>Recent Patterns</h5>
                                    <a href="patterns.php" class="btn btn-primary btn-sm">View All</a>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Severity</th>
                                                    <th>Status</th>
                                                    <th>Created</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                $recentStmt = $pdo->query("SELECT * FROM malware_patterns ORDER BY created_at DESC LIMIT 5");
                                                while ($pattern = $recentStmt->fetch()) {
                                                    $severityClass = $pattern['severity'] == 'high' ? 'danger' : ($pattern['severity'] == 'medium' ? 'warning' : 'info');
                                                    $statusClass = $pattern['is_active'] ? 'success' : 'secondary';
                                                    $statusText = $pattern['is_active'] ? 'Active' : 'Inactive';
                                                ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($pattern['name']); ?></td>
                                                    <td><span class="badge bg-<?php echo $severityClass; ?>"><?php echo ucfirst($pattern['severity']); ?></span></td>
                                                    <td><span class="badge bg-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span></td>
                                                    <td><?php echo date('M d, Y', strtotime($pattern['created_at'])); ?></td>
                                                </tr>
                                                <?php } ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/plugins/jQuery/jquery-1.12.4.min.js"></script>
    <script src="assets/bootstrap/js/popper.min.js"></script>
    <script src="assets/bootstrap/js/bootstrap.min.js"></script>
</body>
</html>
