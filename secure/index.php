<?php
    
    include('includes/config.php');
    if (isset($_SESSION['msg']) && $_SESSION['msg'] != '') {
      $l_message = $_SESSION['msg'];
      unset($_SESSION['msg']);
  }
    if (isset($_POST['action'])) {
      if ($_POST['action'] == "login") {
        $email = mysqli_real_escape_string($connection, $_POST['email']);
        $password = mysqli_real_escape_string($connection, $_POST['password']);
        $strSQL = mysqli_query($connection, "select `id`,`name`,`type`,`password`,`status` from `users` where `email`='" . $email . "'");
        if (mysqli_num_rows($strSQL) > 0) {
          $results = mysqli_fetch_array($strSQL);
          if (password_verify($password, $results['password'])) {
            if ($results['status'] == 2) {
              $l_message = alert("danger", "Your account is inactive please contact administrator");
            } else {
              //Login Sucessfully!!
              
              if ($results['type'] == 1){ //admin
                setcookie("adminUserid", $results['id'], time() + (86400 * 30), "/");
                $_SESSION['adminUserid'] = $results['id'];
                header("location: " . adminurl . "/dashboard.php");
                exit;
              }
              elseif ($results['type'] == 2){ //user
                setcookie("adminUserid", $results['id'], time() + (86400 * 30), "/");
                $_SESSION['adminUserid'] = $results['id'];
                header("location: " . adminurl . "/dashboard.php");
              }
              elseif ($results['type'] == 3){ //user
                setcookie("adminUserid", $results['id'], time() + (86400 * 30), "/");
                $_SESSION['adminUserid'] = $results['id'];
                header("location: " . adminurl . "/dashboard.php");
              }
              
            }
          } else {
            $l_message = alert("danger", "Invalid password!!");
          }
        } else {
          $l_message = alert("danger", "Invalid email or password!!");
        }
      }
    }
?>
<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Login | <?php echo sitename; ?></title>

        <!-- Favicon and touch icons -->
        <link rel="shortcut icon" href="<?php echo logoMini; ?>" type="image/x-icon">
        <!-- Bootstrap -->
        <link href="assets/bootstrap/css/bootstrap.min.css" rel="stylesheet" />
        <!-- Pe-icon-7-stroke -->
        <link href="assets/pe-icon-7-stroke/css/pe-icon-7-stroke.css" rel="stylesheet" />
        <!-- style css -->
        <link href="assets/dist/css/stylecrm.css" rel="stylesheet" />
        <link href="assets/dist/css/stylecrm_new.css" rel="stylesheet" />
        <style>
         .bgImage{
  
  background:linear-gradient(0deg, rgb(255 255 255 / 82%), rgb(64 96 50 / 82%)), url(assets/1.jpg);
  background-position: center top;
  background-size: cover;
  position: relative;
  width: 100%;
  height: 100%;
}
.content-wrapper,.content{
  background: none;
}
      </style>
    </head>
    <body class="bgImage">
        <!-- Content Wrapper -->
        <div class="login-wrapper">
            
            <div class="container-center">
            <?php echo isset($l_message) ? $l_message : ''; ?>
            <div class="login-area">
                <div class="card panel-custom">
                    <img src="<?php echo logo; ?>" alt="">
                    <div class="card-heading custom_head blue"> 
                        <div class="view-header">
                            <div class="header-icon">
                                <i class="pe-7s-unlock"></i>
                            </div>
                            <div class="header-title">
                                <h3>Login</h3>
                                <small><strong>Please enter your credentials to login.</strong></small>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <form action="" id="loginForm" method="post">
                            <div class="form-group">
                                <label class="control-label" for="email">Email</label>
                                <input type="email" placeholder="<EMAIL>" title="Please enter your Email" required value="" name="email" id="email" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="control-label" for="password">Password</label>
                                <input type="password" title="Please enter your password" placeholder="******" required value="" name="password" id="password" class="form-control">
                            <div class="mt-2 mb-2 text-right">
                            <a class="text-blue" href="forget.php">Forgot Password?</a>
                            </div> 
                            </div>
                            <div>
                                <button name="action" value="login" class="btn green_btn blue">Login</button>
                            </div>
                            
                        </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /.content-wrapper -->
        <!-- jQuery -->
      <script src="assets/plugins/jQuery/jquery-1.12.4.min.js" ></script>
      <!-- Bootstrap proper -->
      <script src="assets/bootstrap/js/popper.min.js" ></script>
       <!-- Bootstrap -->
       <script src="assets/bootstrap/js/bootstrap.min.js" ></script>
    </body>
</html>