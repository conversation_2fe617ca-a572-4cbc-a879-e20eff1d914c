<?php
include('includes/config.php');
checkLogin();

$userInfo = getUserInfo($_SESSION['adminUserid']);
if (!$userInfo) {
    session_destroy();
    header("location: " . adminurl . "/index.php");
    exit;
}

$message = '';

// Handle form submissions
if (isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'add':
                // Validate required fields
                if (empty($_POST['name']) || empty($_POST['pattern']) || empty($_POST['severity'])) {
                    $message = alert('danger', 'Please fill in all required fields!');
                    break;
                }

                $name = trim($_POST['name']);
                $pattern = trim($_POST['pattern']);
                $description = trim($_POST['description']);
                $severity = $_POST['severity'];
                $is_active = isset($_POST['is_active']) ? 1 : 0;

                $stmt = $pdo->prepare("INSERT INTO malware_patterns (name, pattern, description, severity, is_active) VALUES (?, ?, ?, ?, ?)");
                if ($stmt->execute([$name, $pattern, $description, $severity, $is_active])) {
                    // Redirect to prevent form resubmission
                    header("Location: patterns.php?success=1");
                    exit;
                } else {
                    $errorInfo = $stmt->errorInfo();
                    $message = alert('danger', 'Error adding pattern: ' . $errorInfo[2]);
                }
                break;

            case 'edit':
                // Validate required fields
                if (empty($_POST['name']) || empty($_POST['pattern']) || empty($_POST['severity']) || empty($_POST['id'])) {
                    $message = alert('danger', 'Please fill in all required fields!');
                    break;
                }

                $id = (int)$_POST['id'];
                $name = trim($_POST['name']);
                $pattern = trim($_POST['pattern']);
                $description = trim($_POST['description']);
                $severity = $_POST['severity'];
                $is_active = isset($_POST['is_active']) ? 1 : 0;

                $stmt = $pdo->prepare("UPDATE malware_patterns SET name=?, pattern=?, description=?, severity=?, is_active=? WHERE id=?");
                if ($stmt->execute([$name, $pattern, $description, $severity, $is_active, $id])) {
                    // Redirect to prevent form resubmission and modal popup
                    header("Location: patterns.php?success=2");
                    exit;
                } else {
                    $errorInfo = $stmt->errorInfo();
                    $message = alert('danger', 'Error updating pattern: ' . $errorInfo[2]);
                }
                break;

            case 'delete':
                $id = (int)$_POST['id'];
                $stmt = $pdo->prepare("DELETE FROM malware_patterns WHERE id=?");
                if ($stmt->execute([$id])) {
                    $message = alert('success', 'Pattern deleted successfully!');
                    header("Location: patterns.php?success=3");
                    exit;
                } else {
                    $message = alert('danger', 'Error deleting pattern!');
                }
                break;

            case 'toggle_status':
                $id = (int)$_POST['id'];
                $stmt = $pdo->prepare("UPDATE malware_patterns SET is_active = NOT is_active WHERE id=?");
                if ($stmt->execute([$id])) {
                    $message = alert('success', 'Pattern status updated successfully!');
                    header("Location: patterns.php?success=4");
                    exit;
                } else {
                    $message = alert('danger', 'Error updating status!');
                }
                break;
        }
    } catch (PDOException $e) {
        $message = alert('danger', 'Database error: ' . $e->getMessage());
    }
}

// Handle success messages from redirects
if (isset($_GET['success'])) {
    switch ($_GET['success']) {
        case '1':
            $message = alert('success', 'Pattern added successfully!');
            break;
        case '2':
            $message = alert('success', 'Pattern updated successfully!');
            break;
        case '3':
            $message = alert('success', 'Pattern deleted successfully!');
            break;
        case '4':
            $message = alert('success', 'Pattern status updated successfully!');
            break;
    }
}

// Get pattern for editing
$editPattern = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $editId = (int)$_GET['edit'];
    $stmt = $pdo->prepare("SELECT * FROM malware_patterns WHERE id = ?");
    $stmt->execute([$editId]);
    $editPattern = $stmt->fetch();
}

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 50;
$offset = ($page - 1) * $limit;

// Search functionality
$search = isset($_GET['search']) ? $_GET['search'] : '';
$searchCondition = '';
$searchParams = [];

if ($search) {
    $searchCondition = "WHERE name LIKE ? OR description LIKE ?";
    $searchParams = ["%$search%", "%$search%"];
}

// Get total count for pagination
$countSql = "SELECT COUNT(*) as total FROM malware_patterns $searchCondition";
$countStmt = $pdo->prepare($countSql);
$countStmt->execute($searchParams);
$totalRecords = $countStmt->fetch()['total'];
$totalPages = ceil($totalRecords / $limit);

// Get patterns
$patternsSql = "SELECT * FROM malware_patterns $searchCondition ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
$patternsStmt = $pdo->prepare($patternsSql);
$patternsStmt->execute($searchParams);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Malware Patterns | <?php echo sitename; ?></title>
    <link rel="shortcut icon" href="<?php echo logoMini; ?>" type="image/x-icon">
    <link href="assets/bootstrap/css/bootstrap.min.css" rel="stylesheet" />
    <link href="assets/pe-icon-7-stroke/css/pe-icon-7-stroke.css" rel="stylesheet" />
    <!-- fontawsome -->
     <link rel="stylesheet" href="secure/assets/font-awesome/css/font-awesome.css">
    <link href="assets/dist/css/stylecrm.css" rel="stylesheet" />
    <link href="assets/dist/css/stylecrm_new.css" rel="stylesheet" />
    <style>
        .pattern-text {
            font-family: monospace;
            font-size: 0.9em;
            max-width: 300px;
            word-break: break-all;
        }
        .table-actions {
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
            <div class="container-fluid">
                <a class="navbar-brand" href="dashboard.php">
                    <img src="<?php echo logo; ?>" alt="Logo" height="30">
                    <?php echo sitename; ?>
                </a>
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            Welcome, <?php echo $userInfo['name']; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <div class="container-fluid mt-4">
            <div class="row">
                <!-- Sidebar -->
                <div class="col-md-2">
                    <div class="card">
                        
                        <div class="list-group list-group-flush">
                            <a href="dashboard.php" class="list-group-item list-group-item-action">
                                <i class="pe-7s-graph"></i> Dashboard
                            </a>
                            <a href="patterns.php" class="list-group-item list-group-item-action active">
                                <i class="pe-7s-shield"></i> Malware Patterns
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="col-md-10">
                    <?php echo $message; ?>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h2>Malware Patterns</h2>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#patternModal">
                                    <i class="pe-7s-plus"></i> Add New Pattern
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Search -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <form method="GET" action="">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="search" placeholder="Search patterns..." value="<?php echo htmlspecialchars($search); ?>">
                                    <button class="btn btn-outline-secondary" type="submit">
                                        <i class="pe-7s-search"></i> Search
                                    </button>
                                    <?php if ($search): ?>
                                    <a href="patterns.php" class="btn btn-outline-secondary">Clear</a>
                                    <?php endif; ?>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Patterns Table -->
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Name</th>
                                            <th>Pattern</th>
                                            <th>Description</th>
                                            <th>Severity</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($pattern = $patternsStmt->fetch()): ?>
                                        <?php
                                            $severityClass = $pattern['severity'] == 'high' ? 'danger' : ($pattern['severity'] == 'medium' ? 'warning' : 'info');
                                            $statusClass = $pattern['is_active'] ? 'success' : 'secondary';
                                            $statusText = $pattern['is_active'] ? 'Active' : 'Inactive';
                                        ?>
                                        <tr>
                                            <td><?php echo $pattern['id']; ?></td>
                                            <td><?php echo htmlspecialchars($pattern['name']); ?></td>
                                            <td class="pattern-text"><?php echo htmlspecialchars(substr($pattern['pattern'], 0, 50)) . (strlen($pattern['pattern']) > 50 ? '...' : ''); ?></td>
                                            <td><?php echo htmlspecialchars(substr($pattern['description'], 0, 100)) . (strlen($pattern['description']) > 100 ? '...' : ''); ?></td>
                                            <td><span class="badge bg-<?php echo $severityClass; ?>"><?php echo ucfirst($pattern['severity']); ?></span></td>
                                            <td><span class="badge bg-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span></td>
                                            <td><?php echo date('M d, Y', strtotime($pattern['created_at'])); ?></td>
                                            <td class="table-actions">
                                                <a href="pattern_detail.php?id=<?php echo $pattern['id']; ?>" class="btn btn-sm btn-outline-info" title="View Details">
                                                    <i class="pe-7s-look"></i>
                                                </a>
                                                <a href="?edit=<?php echo $pattern['id']; ?>" class="btn btn-sm btn-outline-primary" title="Edit">
                                                    <i class="pe-7s-pen"></i>
                                                </a>
                                                <form method="POST" style="display: inline;" onsubmit="return confirm('Toggle status for this pattern?');">
                                                    <input type="hidden" name="action" value="toggle_status">
                                                    <input type="hidden" name="id" value="<?php echo $pattern['id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-outline-warning" title="Toggle Status">
                                                        <i class="pe-7s-refresh"></i>
                                                    </button>
                                                </form>
                                                <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this pattern?');">
                                                    <input type="hidden" name="action" value="delete">
                                                    <input type="hidden" name="id" value="<?php echo $pattern['id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="pe-7s-trash"></i>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <?php if ($totalPages > 1): ?>
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page-1; ?><?php echo $search ? '&search='.urlencode($search) : ''; ?>">Previous</a>
                                    </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?><?php echo $search ? '&search='.urlencode($search) : ''; ?>"><?php echo $i; ?></a>
                                    </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page+1; ?><?php echo $search ? '&search='.urlencode($search) : ''; ?>">Next</a>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pattern Modal -->
    <div class="modal fade" id="patternModal" tabindex="-1" aria-labelledby="patternModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="patternModalLabel"><?php echo $editPattern ? 'Edit Pattern' : 'Add New Pattern'; ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="<?php echo $editPattern ? 'edit' : 'add'; ?>">
                        <?php if ($editPattern): ?>
                        <input type="hidden" name="id" value="<?php echo $editPattern['id']; ?>">
                        <?php endif; ?>

                        <div class="mb-3">
                            <label for="name" class="form-label">Pattern Name *</label>
                            <input type="text" class="form-control" id="name" name="name" required
                                   value="<?php echo $editPattern ? htmlspecialchars($editPattern['name']) : ''; ?>">
                        </div>

                        <div class="mb-3">
                            <label for="pattern" class="form-label">Pattern (Regex) *</label>
                            <textarea class="form-control" id="pattern" name="pattern" rows="3" required
                                      style="font-family: monospace;"><?php echo $editPattern ? htmlspecialchars($editPattern['pattern']) : ''; ?></textarea>
                            <div class="form-text">Enter the regular expression pattern for malware detection.</div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"><?php echo $editPattern ? htmlspecialchars($editPattern['description']) : ''; ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="severity" class="form-label">Severity *</label>
                            <select class="form-select" id="severity" name="severity" required>
                                <option value="low" <?php echo ($editPattern && $editPattern['severity'] == 'low') ? 'selected' : ''; ?>>Low</option>
                                <option value="medium" <?php echo ($editPattern && $editPattern['severity'] == 'medium') ? 'selected' : ''; ?>>Medium</option>
                                <option value="high" <?php echo ($editPattern && $editPattern['severity'] == 'high') ? 'selected' : ''; ?>>High</option>
                            </select>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active"
                                   <?php echo ($editPattern && $editPattern['is_active']) || !$editPattern ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_active">
                                Active
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <?php echo $editPattern ? 'Update Pattern' : 'Add Pattern'; ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="assets/plugins/jQuery/jquery-1.12.4.min.js"></script>
    <script src="assets/bootstrap/js/popper.min.js"></script>
    <script src="assets/bootstrap/js/bootstrap.min.js"></script>

    <script>
        // Auto-show modal if editing (only if no success parameter in URL)
        <?php if ($editPattern && !isset($_GET['success'])): ?>
        $(document).ready(function() {
            $('#patternModal').modal('show');
        });
        <?php endif; ?>

        // Clear form when modal is hidden
        $('#patternModal').on('hidden.bs.modal', function () {
            if (!<?php echo $editPattern ? 'true' : 'false'; ?>) {
                $(this).find('form')[0].reset();
            }
            // Remove edit parameter from URL when modal is closed
            if (window.location.href.indexOf('edit=') > -1) {
                var url = new URL(window.location);
                url.searchParams.delete('edit');
                window.history.replaceState({}, document.title, url);
            }
        });

        // Auto-hide success messages after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    </script>
</body>
</html>
