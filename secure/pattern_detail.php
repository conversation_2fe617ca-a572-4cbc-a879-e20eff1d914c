<?php
include('includes/config.php');
checkLogin();

$userInfo = getUserInfo($_SESSION['adminUserid']);
if (!$userInfo) {
    session_destroy();
    header("location: " . adminurl . "/index.php");
    exit;
}

// Get pattern ID
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header("location: patterns.php");
    exit;
}

$patternId = (int)$_GET['id'];
$patternStmt = $pdo->prepare("SELECT * FROM malware_patterns WHERE id = ?");
$patternStmt->execute([$patternId]);
$pattern = $patternStmt->fetch();

if (!$pattern) {
    $_SESSION['msg'] = alert('danger', 'Pattern not found!');
    header("location: patterns.php");
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Pattern Details | <?php echo sitename; ?></title>
    <link rel="shortcut icon" href="<?php echo logoMini; ?>" type="image/x-icon">
    <link href="assets/bootstrap/css/bootstrap.min.css" rel="stylesheet" />
    <link href="assets/pe-icon-7-stroke/css/pe-icon-7-stroke.css" rel="stylesheet" />
    <link href="assets/dist/css/stylecrm.css" rel="stylesheet" />
    <link href="assets/dist/css/stylecrm_new.css" rel="stylesheet" />
    <style>
        .pattern-code {
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            white-space: pre-wrap;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
            <div class="container-fluid">
                <a class="navbar-brand" href="dashboard.php">
                    <img src="<?php echo logo; ?>" alt="Logo" height="30">
                    <?php echo sitename; ?>
                </a>
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            Welcome, <?php echo $userInfo['name']; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <div class="container-fluid mt-4">
            <div class="row">
                <!-- Sidebar -->
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-header">
                            <h5>Navigation</h5>
                        </div>
                        <div class="list-group list-group-flush">
                            <a href="dashboard.php" class="list-group-item list-group-item-action">
                                <i class="pe-7s-graph"></i> Dashboard
                            </a>
                            <a href="patterns.php" class="list-group-item list-group-item-action active">
                                <i class="pe-7s-shield"></i> Malware Patterns
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="col-md-9">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2>Pattern Details</h2>
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                                    <li class="breadcrumb-item"><a href="patterns.php">Patterns</a></li>
                                    <li class="breadcrumb-item active"><?php echo htmlspecialchars($pattern['name']); ?></li>
                                </ol>
                            </nav>
                        </div>
                        <div>
                            <a href="patterns.php?edit=<?php echo $pattern['id']; ?>" class="btn btn-primary">
                                <i class="pe-7s-pen"></i> Edit Pattern
                            </a>
                            <a href="patterns.php" class="btn btn-secondary">
                                <i class="pe-7s-back"></i> Back to List
                            </a>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <?php echo htmlspecialchars($pattern['name']); ?>
                                        <?php
                                        $severityClass = $pattern['severity'] == 'high' ? 'danger' : ($pattern['severity'] == 'medium' ? 'warning' : 'info');
                                        $statusClass = $pattern['is_active'] ? 'success' : 'secondary';
                                        $statusText = $pattern['is_active'] ? 'Active' : 'Inactive';
                                        ?>
                                        <span class="badge bg-<?php echo $severityClass; ?> ms-2"><?php echo ucfirst($pattern['severity']); ?></span>
                                        <span class="badge bg-<?php echo $statusClass; ?> ms-1"><?php echo $statusText; ?></span>
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Pattern Information</h6>
                                            <table class="table table-borderless">
                                                <tr>
                                                    <td><strong>ID:</strong></td>
                                                    <td><?php echo $pattern['id']; ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Name:</strong></td>
                                                    <td><?php echo htmlspecialchars($pattern['name']); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Severity:</strong></td>
                                                    <td><span class="badge bg-<?php echo $severityClass; ?>"><?php echo ucfirst($pattern['severity']); ?></span></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Status:</strong></td>
                                                    <td><span class="badge bg-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span></td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Timestamps</h6>
                                            <table class="table table-borderless">
                                                <tr>
                                                    <td><strong>Created:</strong></td>
                                                    <td><?php echo date('F j, Y g:i A', strtotime($pattern['created_at'])); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Updated:</strong></td>
                                                    <td><?php echo date('F j, Y g:i A', strtotime($pattern['updated_at'])); ?></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>

                                    <?php if (!empty($pattern['description'])): ?>
                                    <div class="mt-4">
                                        <h6>Description</h6>
                                        <p class="text-muted"><?php echo nl2br(htmlspecialchars($pattern['description'])); ?></p>
                                    </div>
                                    <?php endif; ?>

                                    <div class="mt-4">
                                        <h6>Regular Expression Pattern</h6>
                                        <div class="pattern-code"><?php echo htmlspecialchars($pattern['pattern']); ?></div>
                                        <small class="text-muted mt-2 d-block">
                                            <i class="pe-7s-info"></i> This is the regular expression used to detect malware patterns in files.
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/plugins/jQuery/jquery-1.12.4.min.js"></script>
    <script src="assets/bootstrap/js/popper.min.js"></script>
    <script src="assets/bootstrap/js/bootstrap.min.js"></script>
</body>
</html>
