<?php
// Database update script to add missing is_active column
include('includes/config.php');

echo "<h2>Database Update Script</h2>";

try {
    // Check if is_active column exists
    $checkColumn = $pdo->query("SHOW COLUMNS FROM malware_patterns LIKE 'is_active'");

    if ($checkColumn->rowCount() == 0) {
        // Add is_active column
        $addColumn = $pdo->exec("ALTER TABLE malware_patterns ADD COLUMN is_active TINYINT(1) DEFAULT 1 AFTER severity");

        echo "<p style='color: green;'>✓ Successfully added 'is_active' column to malware_patterns table</p>";
    } else {
        echo "<p style='color: blue;'>ℹ 'is_active' column already exists</p>";
    }

    // Show current table structure
    echo "<h3>Current Table Structure:</h3>";
    $structure = $pdo->query("DESCRIBE malware_patterns");
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $structure->fetch()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";

} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
}

echo "<p><a href='dashboard.php'>Go to Dashboard</a></p>";
?>
