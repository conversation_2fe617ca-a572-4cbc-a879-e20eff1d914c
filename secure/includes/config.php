<?php
session_start();

// Include root config
require_once(__DIR__ . '/../../config.php');

// Site configuration
define('sitename', 'Malware Pattern Management');
define('adminurl', 'http://localhost/wp/rafay/secure');
define('logo', 'assets/logo.png');
define('logoMini', 'assets/favicon.ico');

// Create PDO connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Create mysqli connection for backward compatibility (if needed)
$connection = mysqli_connect(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if (!$connection) {
    die("Connection failed: " . mysqli_connect_error());
}
mysqli_set_charset($connection, "utf8");

// Helper functions
function alert($type, $message) {
    $class = ($type == 'success') ? 'alert-success' : (($type == 'danger') ? 'alert-danger' : 'alert-info');
    return '<div class="alert ' . $class . ' alert-dismissible fade show" role="alert">
                ' . $message . '
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>';
}

function checkLogin() {
    if (!isset($_SESSION['adminUserid']) || empty($_SESSION['adminUserid'])) {
        header("location: " . adminurl . "/index.php");
        exit;
    }
}

function getUserInfo($userid) {
    global $connection;
    $strSQL = mysqli_query($connection, "SELECT * FROM `users` WHERE `id`='" . $userid . "'");
    if (mysqli_num_rows($strSQL) > 0) {
        return mysqli_fetch_array($strSQL);
    }
    return false;
}
?>
