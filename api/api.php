<?php
// Prevent any output before our JSON response
ob_start();

// Disable error display but keep logging
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', dirname(__FILE__) . '/error.log');
// include php  
use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

require_once(__DIR__ . '/../vendor/autoload.php');

// Function to send JSON response and exit
function send_json_response($data, $status_code = 200) {
    // Clear any previous output
    if (ob_get_length()) ob_clean();
    
    // Set headers
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST');
    http_response_code($status_code);
    
    // Send response
    echo json_encode($data);
    exit;
}

// Include database connection
require_once '../config.php';

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage());
    send_json_response([
        'success' => false,
        'error' => 'Database connection failed',
        'details' => $e->getMessage()
    ], 500);
}

// Function to encrypt patterns
function encryptPatterns($patterns, $site_key) {
    try {
        error_log("Starting pattern encryption...");
        error_log("Number of patterns to encrypt: " . count($patterns));
        
        // Generate a random IV
        $ivlen = openssl_cipher_iv_length('aes-256-gcm');
        if ($ivlen === false) {
            throw new Exception("Failed to get IV length for AES-GCM");
        }
        
        $iv = openssl_random_pseudo_bytes($ivlen);
        if ($iv === false) {
            throw new Exception("Failed to generate IV");
        }
        
        $tag = null; // Initialize tag variable
        
        // Convert patterns array to JSON
        $json_patterns = json_encode($patterns);
        if ($json_patterns === false) {
            throw new Exception("JSON encoding failed: " . json_last_error_msg());
        }
        
        error_log("JSON encoding successful, length: " . strlen($json_patterns));
        
        // Encrypt using AES-256-GCM
        $encrypted = openssl_encrypt(
            $json_patterns,
            'aes-256-gcm',
            $site_key,
            OPENSSL_RAW_DATA,
            $iv,
            $tag
        );
        
        if ($encrypted === false) {
            throw new Exception("Encryption failed: " . openssl_error_string());
        }
        
        if ($tag === null) {
            throw new Exception("Authentication tag generation failed");
        }
        
        error_log("Encryption successful");
        
        // Combine IV, tag and encrypted data
        $combined = $iv . $tag . $encrypted;
        $result = base64_encode($combined);
        
        error_log("Final encrypted data length: " . strlen($result));
        
        return $result;
    } catch (Exception $e) {
        error_log("Encryption error: " . $e->getMessage());
        throw $e;
    }
}

// Function to generate site-specific encryption key
function generateSiteKey($site_url, $email) {
    try {
        if (empty($site_url) || empty($email)) {
            throw new Exception("Site URL and email are required for key generation");
        }
        
        
        
        // Use site URL and email to create a unique but reproducible key
        $data = $site_url . '|' . $email ;
        $key = hash('sha256', $data, true);
        
        error_log("Site key generated successfully for: " . $site_url);
        
        return $key;
    } catch (Exception $e) {
        error_log("Key generation error: " . $e->getMessage());
        throw $e;
    }
}



// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    send_json_response([
        'success' => false,
        'error' => 'Method not allowed'
    ], 405);
}

// Get POST data
$action = $_POST['action'] ?? '';
$email = $_POST['email'] ?? '';
$site_url = $_POST['site_url'] ?? '';
$api_key = $_POST['api_key'] ?? '';
$plugin_version = $_POST['plugin_version'] ?? '1.0';
$current_version = $_POST['current_version'] ?? '0';
$emailData = $_POST['emailData'] ?? '';

error_log("Processing request - Action: $action, Email: $email, Site URL: $site_url");

// Validate email for actions that require it
if (in_array($action, ['register_site', 'check_registration', '_gaze_global_insert', 'check_updates', 'check_whitlisting', 'send_email_report']) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
    send_json_response([
        'success' => false,
        'error' => 'Invalid email address'
    ], 400);
}


try {
    // Handle different actions
    switch ($action) {
        case 'send_email_report':
            // First verify registration
            $stmt = $pdo->prepare("SELECT id, api_key FROM registered_sites WHERE email = ? AND site_url = ?");
            $stmt->execute([$email, $site_url]);
            $site_data = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$site_data) {
                send_json_response([
                    'success' => false,
                    'error' => 'Site not registered'
                ], 403);
            }
            $mail = new PHPMailer(true);
            try {
                //Server settings
                $mail->SMTPDebug = SMTP::DEBUG_SERVER;                      // Enable verbose debug output
                $mail->isSMTP();                                            // Send using SMTP
                $mail->Host = 'smtp.mailjet.com';
                $mail->SMTPAuth = true;
                $mail->Username = 'd57baaef503aeef2f93bb3dc1c80e0ca';
                $mail->Password = '41eee5c90da8e7912d042552860b9298';
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
                $mail->Port = 587;



                //Recipients
                $mail->setFrom('<EMAIL>', 'Guardian Gaze');
                // $mail->addAddress('<EMAIL>', 'Guardian Gaze');
                
                // Content
                $mail->isHTML(true);                                  // Set email format to HTML
                $mail->Subject = 'Guardian Gaze Security Scan Report';

                $emailData = json_decode($emailData, true);
                $recipients = explode(',', $emailData['recipients']);
                $content = $emailData['content'];
                foreach ($recipients as $recipient) {
                    $mail->addAddress($recipient, 'Admin');
                }


                $mail->Body = $content;
            
                $mail->send();
                error_log('Email sent');
                send_json_response([
                    'success' => true,
                    'message' => 'Email sent'
                ]);
            } catch (Exception $e) {
                error_log("Email could not be sent. Mailer Error: {$mail->ErrorInfo}");
                send_json_response([
                    'success' => false,
                    'error' => 'Email could not be sent',
                    'details' => $mail->ErrorInfo
                ], 500);
            }
            break;
        case 'check_registration':
            // Check if site is registered
            $stmt = $pdo->prepare("SELECT id, api_key,created_at FROM registered_sites WHERE email = ? AND site_url = ?");
            $stmt->execute([$email, $site_url]);
            $data = $stmt->fetch(PDO::FETCH_ASSOC);
            if( !$data) {
                send_json_response([
                    'success' => false,
                    'error' => 'Site not registered'
                ], 403);
            }
            send_json_response([
                'success' => true,
                'created_at' => $data['created_at'],
            ]);
            break;
        case 'check_whitlisting':
            send_json_response([
                'success' => false
            ]);
            break;

        case 'check_updates':
            // First verify registration
            $stmt = $pdo->prepare("SELECT id, api_key FROM registered_sites WHERE email = ? AND site_url = ?");
            $stmt->execute([$email, $site_url]);
            $site_data = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$site_data) {
                send_json_response([
                    'success' => false,
                    'error' => 'Site not registered'
                ], 403);
            }

            // Get all active patterns
            $stmt = $pdo->prepare("SELECT * FROM malware_patterns WHERE is_active = 1");
            $stmt->execute();
            $patterns = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $patterns[$row['name']] = $row['pattern'];
            }

            // Get the latest version
            $stmt = $pdo->prepare("SELECT MAX(updated_at) as version FROM malware_patterns WHERE is_active = 1");
            $stmt->execute();
            $latest_version = $stmt->fetch(PDO::FETCH_ASSOC)['version'] ?? date('Y-m-d H:i:s');

            error_log("Check updates - Current version: $current_version, Latest version: $latest_version");
            error_log("Returning " . count($patterns) . " patterns");

            send_json_response([
                'success' => true,
                'has_updates' => $latest_version > $current_version,
                'version' => $latest_version,
                'patterns' => $patterns,
                'pattern_count' => count($patterns)
            ]);
            break;

        case 'register_site':
            // Check if already registered
            $stmt = $pdo->prepare("SELECT id, api_key,created_at FROM registered_sites WHERE email = ? AND site_url = ?");
            $stmt->execute([$email, $site_url]);
            $existing = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($existing) {
                error_log("Site already registered with API key: " . $existing['api_key']);
                send_json_response([
                    'success' => true,
                    'message' => 'Site already registered',
                    'created_at' => $existing['created_at']
                ]);
            }

            // Register new site
            $stmt = $pdo->prepare("INSERT INTO registered_sites (email, site_url, plugin_version, api_key, created_at) VALUES (?, ?, ?, ?, NOW())");
            
            if ($stmt->execute([$email, $site_url, $plugin_version, $api_key])) {
                error_log("Site registered successfully");
                send_json_response([
                    'success' => true,
                    'message' => 'Site registered successfully',
                    'api_key' => $api_key,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            } else {
                throw new Exception('Registration failed');
            }
            break;

        case '_gaze_global_insert':
            try {
                error_log("Starting get_patterns request processing...");
                
                // Validate inputs
                if (empty($email) || empty($site_url)) {
                    throw new Exception("Email and site URL are required");
                }
                
                error_log("Checking site registration for email: $email and URL: $site_url");
                
                // First check if site is registered
                $stmt = $pdo->prepare("SELECT id, api_key FROM registered_sites WHERE email = ? AND site_url = ?");
                $stmt->execute([$email, $site_url]);
                $site_data = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$site_data) {
                    error_log("Site not registered, attempting registration...");
                    // Site not registered, register it first
                    
                    $stmt = $pdo->prepare("INSERT INTO registered_sites (email, site_url, plugin_version, api_key, created_at) VALUES (?, ?, ?, ?, NOW())");
                    if (!$stmt->execute([$email, $site_url, $plugin_version, $api_key])) {
                        throw new Exception('Registration failed: ' . implode(', ', $stmt->errorInfo()));
                    }
                    $site_data = ['api_key' => $api_key];
                    error_log("New site registered with API key: $api_key");
                }

                error_log("Fetching patterns from database...");
                
                // Get patterns from database
                $stmt = $pdo->prepare("SELECT * FROM malware_patterns WHERE is_active = 1");
                $stmt->execute();
                $patterns = [];
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $patterns[$row['name']] = $row['pattern'];
                    // severity
                    // $patterns[$row['name']]['severity'] = $row['severity'];
                }
                
                error_log("Retrieved " . count($patterns) . " patterns from database");

                // Get the latest version
                $stmt = $pdo->prepare("SELECT MAX(updated_at) as version FROM malware_patterns WHERE is_active = 1");
                $stmt->execute();
                $version = $stmt->fetch(PDO::FETCH_ASSOC)['version'] ?? date('Y-m-d H:i:s');
                
                error_log("Pattern version: $version");

                // Generate site-specific encryption key
                error_log("Generating site-specific encryption key...");
                $site_key = generateSiteKey($site_url, $email);

                // Encrypt patterns
                error_log("Encrypting patterns...");
                $encrypted_patterns = encryptPatterns($patterns, $site_key);

                error_log("Successfully encrypted " . count($patterns) . " patterns");

                send_json_response([
                    'success2' => true,
                    'success' => true,
                    'global_insert' => $encrypted_patterns,
                    'version' => $version,
                    'count' => count($patterns),
                    'api_key' => $site_data['api_key']
                ]);
                
            } catch (Exception $e) {
                error_log("Error in get_patterns: " . $e->getMessage());
                error_log("Stack trace: " . $e->getTraceAsString());
                throw $e; // Re-throw to be caught by the main try-catch block
            }
            break;

        default:
            send_json_response([
                'success' => false,
                'error' => 'Invalid action: ' . $action
            ], 400);
    }
} catch (Exception $e) {
    error_log("Error in patterns.php: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    send_json_response([
        'success' => false,
        'error' => 'Server error occurred',
        'details' => $e->getMessage()
    ], 500);
} 