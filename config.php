<?php
// guardian-gaze-server/config.php

define('GUARDIAN_GAZE_SERVER', true);

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'rafay');
define('DB_USER', 'root');
define('DB_PASS', '12345678');

// Add salt for pattern encryption
define('DB_SALT', 'guardian_gaze_patterns_salt_2024'); // Using a strong default salt

// Security settings
define('API_SECRET', 'asdfa&Y#(hcofe92fhoqdoi320dj0ifj0349urwiefjofihiksdjfskjf'); // Use a strong random string

// Allowed domains (optional)
define('ALLOWED_DOMAINS', [
    'example.com',
    'example.org'
]);